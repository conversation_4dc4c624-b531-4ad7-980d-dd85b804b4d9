Average Logic (.dart)

import 'dart:io';

// Class to represent each entry in the data file
class MeterEntry {
  DateTime date;
  String type; // "meter_reading" or "top_up"
  double amount;

  MeterEntry(this.date, this.type, this.amount);
}

// Function to parse date string in "dd/MM/yyyy HH:mm" format
DateTime parseDate(String dateStr) {
  List<String> parts = dateStr.split(' ');
  List<String> dateParts = parts[0].split('/');
  List<String> timeParts = parts[1].split(':');
  int day = int.parse(dateParts[0]);
  int month = int.parse(dateParts[1]);
  int year = int.parse(dateParts[2]);
  int hour = int.parse(timeParts[0]);
  int minute = int.parse(timeParts[1]);
  return DateTime(year, month, day, hour, minute);
}

// Function to format date as "dd/MM/yyyy"
String formatDate(DateTime dt) {
  return "${dt.day.toString().padLeft(2, '0')}/${dt.month.toString().padLeft(2, '0')}/${dt.year}";
}

// Function to get the date part (ignoring time)
DateTime dateOnly(DateTime dt) {
  return DateTime(dt.year, dt.month, dt.day);
}

void main() {
  // Path to the data file
  String filePath = 'lekky_cost_data.csv';
  File file = File(filePath);
  List<String> lines = file.readAsLinesSync();

  // Parse the lines into MeterEntry objects
  List<MeterEntry> entries = [];
  for (String line in lines) {
    List<String> parts = line.split(',');
    if (parts.length == 3) {
      String dateStr = parts[0].trim();
      String type = parts[1].trim();
      String amountStr = parts[2].trim();
      try {
        DateTime date = parseDate(dateStr);
        double amount = double.parse(amountStr);
        entries.add(MeterEntry(date, type, amount));
      } catch (e) {
        print('Error parsing line: $line');
      }
    } else {
      print('Invalid line: $line');
    }
  }

  // Sort entries by date to ensure chronological order
  entries.sort((a, b) => a.date.compareTo(b.date));

  // Initialize variables for processing
  double? balance; // Current balance after top-ups and usage
  DateTime? lastReadingDate; // Date of the last meter reading
  double totalUsage = 0; // Cumulative usage from start
  DateTime? startDate; // Date of the first meter reading
  List<List<String>> table2 = []; // List to hold the output table rows

  // Process each entry
  for (var entry in entries) {
    if (entry.type == "meter_reading") {
      if (balance == null) {
        // First meter reading: set initial balance and dates
        balance = entry.amount;
        lastReadingDate = entry.date;
        startDate = entry.date;
        table2.add([formatDate(entry.date), "", entry.amount.toStringAsFixed(2), "0", "0"]);
      } else {
        // Calculate days since last reading using date part only
        int days = dateOnly(entry.date).difference(dateOnly(lastReadingDate)).inDays;
        if (days > 0) {
          // Calculate usage and averages
          double usage = balance - entry.amount;
          double shortTermUsage = usage / days;
          int totalDays = dateOnly(entry.date).difference(dateOnly(startDate)).inDays;
          totalUsage += usage;
          double totalAverage = totalUsage / totalDays;
          table2.add([formatDate(entry.date), "", entry.amount.toStringAsFixed(2), shortTermUsage.toStringAsFixed(2), totalAverage.toStringAsFixed(2)]);
        }
        // Update balance and last reading date
        balance = entry.amount;
        lastReadingDate = entry.date;
      }
    } else if (entry.type == "top_up") {
      if (balance != null) {
        // Apply top-up to the balance
        balance += entry.amount;
      }
      table2.add([formatDate(entry.date), entry.amount.toStringAsFixed(2), "n/a", "n/a", "n/a"]);
    }
  }

  // Print the output table
  print("| Date of Meter Reading | Amount Topped Up | Amount Left (£) | Short-term Usage per day (£) | Total Average to Date (£) |");
  print("|-----------------------|------------------|-----------------|------------------------------|---------------------------|");
  for (var row in table2) {
    print("| ${row[0].padRight(21)} | ${row[1].padLeft(16)} | ${row[2].padLeft(15)} | ${row[3].padLeft(28)} | ${row[4].padLeft(25)} |");
  }
}