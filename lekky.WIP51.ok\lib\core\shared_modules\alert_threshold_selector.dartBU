// File: lib/core/shared_modules/alert_threshold_selector.dart
import 'package:flutter/material.dart';
import '../widgets/app_text_field.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class AlertThresholdSelector extends BaseSettingsWidget {
  final double currentValue;
  final Function(double) onChanged;
  final String currencySymbol;
  final String? errorText;
  final bool hasMeterReadings;

  const AlertThresholdSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    this.hasMeterReadings = false,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Alert Threshold'),

        if (showHelperText)
          buildHelperText(context,
              'You will be notified when your balance falls below this amount.'),

        if (!hasMeterReadings) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber[800], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Low balance alerts will be active after you enter your first meter reading.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.amber[800]
                          : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Slider for quick adjustment
        Row(
          children: [
            Text('${currencySymbol}0', style: const TextStyle(fontSize: 12)),
            Expanded(
              child: Slider(
                value: currentValue.clamp(0.0, 50.0),
                min: 0.0,
                max: 50.0,
                divisions: 100,
                label: '$currencySymbol${currentValue.toStringAsFixed(2)}',
                onChanged: onChanged,
              ),
            ),
            Text('${currencySymbol}50', style: const TextStyle(fontSize: 12)),
          ],
        ),

        const SizedBox(height: 8),

        // Precise input field
        AppTextField(
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          labelText: 'Alert Threshold Value',
          prefixText: currencySymbol,
          helperText: 'Enter a specific amount',
          controller:
              TextEditingController(text: currentValue.toStringAsFixed(2)),
          selectAllOnFocus: true,
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null) {
              onChanged(parsedValue);
            }
          },
        ),

        const SizedBox(height: 8),
        Text(
          'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Alert Threshold'),

        if (showTitle)
          Text(
            'Current: $currencySymbol${currentValue.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (!hasMeterReadings) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[800], size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Requires meter reading',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.amber[800]
                        : Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ],

        if (showHelperText)
          buildHelperText(context, 'Set when to receive low balance alerts'),

        const SizedBox(height: 8),

        // Slider - more compact
        Slider(
          value: currentValue.clamp(0.0, 50.0),
          min: 0.0,
          max: 50.0,
          divisions: 100,
          label: '$currencySymbol${currentValue.toStringAsFixed(2)}',
          onChanged: onChanged,
        ),

        // Precise input field - more compact
        AppTextField(
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          labelText: 'Custom',
          prefixText: currencySymbol,
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          controller:
              TextEditingController(text: currentValue.toStringAsFixed(2)),
          selectAllOnFocus: true,
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null) {
              onChanged(parsedValue);
            }
          },
        ),

        buildErrorText(context, errorText),
      ],
    );
  }
}
