// File: lib/features/settings/presentation/widgets/meter_reading_reminder_settings.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/notification_provider.dart';

/// A widget for configuring meter reading reminder settings
class MeterReadingReminderSettings extends StatefulWidget {
  const MeterReadingReminderSettings({Key? key}) : super(key: key);

  @override
  State<MeterReadingReminderSettings> createState() =>
      _MeterReadingReminderSettingsState();
}

class _MeterReadingReminderSettingsState
    extends State<MeterReadingReminderSettings> {
  bool _remindersEnabled = false;
  bool _frequencyEnabled = true; // Toggle for frequency options
  int _reminderFrequency = 7; // Default to weekly
  DateTime? _lastReminderDate;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    final remindersEnabled =
        await notificationProvider.areMeterReadingRemindersEnabled();
    final frequencyEnabled =
        await notificationProvider.isFrequencyOptionsEnabled();
    final reminderFrequency =
        await notificationProvider.getMeterReadingReminderFrequency();
    final lastReminderDate =
        await notificationProvider.getLastMeterReadingReminderDate();

    setState(() {
      _remindersEnabled = remindersEnabled;
      _frequencyEnabled = frequencyEnabled;
      _reminderFrequency = reminderFrequency;
      _lastReminderDate = lastReminderDate;
      _isLoading = false;
    });
  }

  String _getFrequencyText(int days) {
    switch (days) {
      case 1:
        return 'Daily';
      case 7:
        return 'Weekly';
      case 14:
        return 'Bi-weekly';
      case 30:
        return 'Monthly';
      default:
        return 'Every $days days';
    }
  }

  // Methods for showing dialog and building frequency options removed
  // as we now show the options directly in the settings screen

  @override
  Widget build(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (_isLoading) {
      return Container(
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[850] : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Meter Reading Reminders',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Get reminded to record your meter readings regularly',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Enable Meter Reading Reminders',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _remindersEnabled
                            ? 'Reminders every ${_getFrequencyText(_reminderFrequency).toLowerCase()}'
                            : 'Reminders disabled',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Transform.scale(
                  scale: 0.8, // Scale down the switch by 20%
                  child: Switch(
                    value: _remindersEnabled,
                    onChanged: (value) async {
                      await notificationProvider
                          .setMeterReadingRemindersEnabled(value);
                      setState(() {
                        _remindersEnabled = value;
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          if (_remindersEnabled) ...[
            const Divider(height: 1, thickness: 0.5),
            // Reminder Frequency Toggle
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Reminder Frequency',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _frequencyEnabled
                              ? 'Set how often to receive reminders'
                              : 'Using default frequency (weekly)',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Transform.scale(
                    scale: 0.8, // Scale down the switch by 20%
                    child: Switch(
                      value: _frequencyEnabled,
                      onChanged: (value) async {
                        await notificationProvider
                            .setFrequencyOptionsEnabled(value);
                        setState(() {
                          _frequencyEnabled = value;
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
            // Show frequency options directly under the toggle when enabled
            if (_frequencyEnabled) ...[
              const Divider(height: 1, thickness: 0.5),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Select frequency:',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Daily option
                    RadioListTile<int>(
                      title: const Text('Daily'),
                      value: 1,
                      groupValue: _reminderFrequency,
                      onChanged: (value) async {
                        if (value != null) {
                          await notificationProvider
                              .setMeterReadingReminderFrequency(value);
                          setState(() {
                            _reminderFrequency = value;
                          });
                        }
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    // Weekly option
                    RadioListTile<int>(
                      title: const Text('Weekly'),
                      value: 7,
                      groupValue: _reminderFrequency,
                      onChanged: (value) async {
                        if (value != null) {
                          await notificationProvider
                              .setMeterReadingReminderFrequency(value);
                          setState(() {
                            _reminderFrequency = value;
                          });
                        }
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    // Bi-weekly option
                    RadioListTile<int>(
                      title: const Text('Bi-weekly'),
                      value: 14,
                      groupValue: _reminderFrequency,
                      onChanged: (value) async {
                        if (value != null) {
                          await notificationProvider
                              .setMeterReadingReminderFrequency(value);
                          setState(() {
                            _reminderFrequency = value;
                          });
                        }
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    // Monthly option
                    RadioListTile<int>(
                      title: const Text('Monthly'),
                      value: 30,
                      groupValue: _reminderFrequency,
                      onChanged: (value) async {
                        if (value != null) {
                          await notificationProvider
                              .setMeterReadingReminderFrequency(value);
                          setState(() {
                            _reminderFrequency = value;
                          });
                        }
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ),
            ],
            if (_lastReminderDate != null) ...[
              const Divider(height: 1, thickness: 0.5),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Last Reminder',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${_lastReminderDate!.day}/${_lastReminderDate!.month}/${_lastReminderDate!.year}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const Divider(height: 1, thickness: 0.5),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: ElevatedButton(
                onPressed: () async {
                  // Capture the context before the async gap
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  await notificationProvider
                      .showMeterReadingReminderNotification();

                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('Reminder sent'),
                      ),
                    );
                    await _loadSettings(); // Reload to update last reminder date
                  }
                },
                child: const Text('Send Reminder Now'),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
