// File: lib/features/home/<USER>/widgets/add_top_up_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A dialog for adding a top-up
class AddTopUpDialog extends StatefulWidget {
  final String meterUnit;

  const AddTopUpDialog({
    super.key,
    required this.meterUnit,
  });

  /// Show the dialog
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    required String meterUnit,
  }) async {
    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AddTopUpDialog(
        meterUnit: meterUnit,
      ),
    );
  }

  @override
  State<AddTopUpDialog> createState() => _AddTopUpDialogState();
}

class _AddTopUpDialogState extends State<AddTopUpDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      Navigator.of(context).pop({
        'amount': double.parse(_amountController.text),
        'date': _selectedDate,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Top-up'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: _amountController,
              labelText: 'Amount',
              hintText: 'e.g. 20',
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              prefixText: widget.meterUnit,
              validator: (value) {
                final result = InputValidator.validateTopUpAmount(value ?? '');
                if (!result['isValid']) {
                  return result['errorMessage'];
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Date',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(DateTimeUtils.formatDateDefault(_selectedDate)),
                    const Icon(Icons.calendar_today),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
