// File: lib/features/history/presentation/widgets/filter_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/dialogs/selection_dialog.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../domain/models/history_filter.dart';

/// A dialog for filtering history entries
class FilterDialog extends StatefulWidget {
  final HistoryFilter filter;
  final Function(HistoryFilter) onApply;
  final DateTime? earliestDate;
  final DateTime? latestDate;
  final bool showDateRangeFilter;

  const FilterDialog({
    super.key,
    required this.filter,
    required this.onApply,
    this.earliestDate,
    this.latestDate,
    this.showDateRangeFilter = true,
  });

  static Future<void> show({
    required BuildContext context,
    required HistoryFilter filter,
    required Function(HistoryFilter) onApply,
    DateTime? earliestDate,
    DateTime? latestDate,
    bool showDateRangeFilter = true,
  }) async {
    await SelectionDialog.showCustom(
      context: context,
      title: 'Filter Options',
      icon: Icons.filter_list,
      content: FilterDialog(
        filter: filter,
        onApply: onApply,
        earliestDate: earliestDate,
        latestDate: latestDate,
        showDateRangeFilter: showDateRangeFilter,
      ),
      barrierDismissible: true,
    );
  }

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late HistoryFilter _filter;

  @override
  void initState() {
    super.initState();
    // Initialize with the widget filter but ensure isInvalidFilter is set correctly
    // This ensures that when the dialog opens, no specific filter type is selected by default
    _filter = widget.filter.copyWith(
      isInvalidFilter: widget.filter.isInvalidFilter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDateRangeFilter) ...[
          _buildDateRangeFilter(),
          const SizedBox(height: 16),
        ] else ...[
          _buildDateRangeUnavailableMessage(),
          const SizedBox(height: 16),
        ],
        _buildEntryTypeFilter(),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              width: 100,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Colors.blue),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 100,
              child: ElevatedButton(
                onPressed: () {
                  // Check if we're in invalid entries mode
                  if (_getEntryTypeValue() == 'invalid') {
                    // Pass the filter to the parent, but the parent will need to
                    // apply special filtering logic for invalid entries
                    widget.onApply(_filter);

                    // Close the dialog
                    Navigator.of(context).pop();

                    // Show a snackbar to inform the user
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Showing invalid entries'),
                        duration: Duration(seconds: 2),
                        backgroundColor: Colors.red,
                      ),
                    );
                  } else {
                    // Normal filter application
                    widget.onApply(_filter);
                    Navigator.of(context).pop();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Apply'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'From Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: Text(
                    _filter.startDate != null
                        ? DateTimeUtils.formatDateDefault(_filter.startDate!)
                        : 'Select Date',
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'To Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: Text(
                    _filter.endDate != null
                        ? DateTimeUtils.formatDateDefault(_filter.endDate!)
                        : 'Select Date',
                  ),
                ),
              ),
            ),
          ],
        ),
        if (_hasDateRangeError())
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'From Date must be before To Date',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDateRangeUnavailableMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.tertiary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Date range filtering requires at least 2 meter readings. Add more meter readings to enable this feature.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors
                            .white70 // Lighter color for better readability in dark mode
                        : AppColors.onSurfaceVariant,
                    fontSize:
                        13.0, // Slightly larger font size for better readability
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEntryTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Type',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        RadioListTile<String>(
          title: const Text('All Entries'),
          value: 'all',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: true,
                  showTopUps: true,
                  isInvalidFilter:
                      false, // Explicitly set to false to ensure it's not an invalid entries filter
                );
              });
            }
          },
          activeColor: Colors.black,
          dense: true,
        ),
        RadioListTile<String>(
          title: const Text('Meter Readings Only'),
          value: 'readings',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: true,
                  showTopUps: false,
                  isInvalidFilter: false, // Not an invalid entries filter
                );
              });
            }
          },
          activeColor: Colors.blue, // Match the meter filter notification color
          dense: true,
        ),
        RadioListTile<String>(
          title: const Text('Top-Ups Only'),
          value: 'topups',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: false,
                  showTopUps: true,
                  showInvalidEntries: true, // Reset invalid entries filter
                  isInvalidFilter: false, // Not an invalid entries filter
                );
              });
            }
          },
          activeColor: AppColors.tertiary,
          dense: true,
        ),
        RadioListTile<String>(
          title: const Text('Invalid Entries Only'),
          value: 'invalid',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                // Reset any date filters
                _filter = _filter.copyWith(
                  startDate: null,
                  endDate: null,
                  showMeterReadings: true,
                  showTopUps: true,
                  showInvalidEntries: true,
                  isInvalidFilter:
                      true, // Set the flag to indicate this is an invalid entries filter
                  // The actual filtering of invalid entries is done in the controller
                  // This just marks that we want to show only invalid entries
                );
              });
            }
          },
          activeColor: AppColors.error,
          dense: true,
        ),
      ],
    );
  }

  String _getEntryTypeValue() {
    // Check if we're in invalid entries mode using the isInvalidFilter flag
    if (_filter.isInvalidFilter) {
      return 'invalid';
    } else if (_filter.showMeterReadings && !_filter.showTopUps) {
      return 'readings';
    } else if (!_filter.showMeterReadings && _filter.showTopUps) {
      return 'topups';
    } else if (_filter.showMeterReadings && _filter.showTopUps) {
      return 'all';
    } else {
      // Default to all if nothing is selected
      return 'all';
    }
  }

  bool _hasDateRangeError() {
    if (_filter.startDate != null && _filter.endDate != null) {
      return _filter.startDate!.isAfter(_filter.endDate!);
    }
    return false;
  }

  Future<void> _selectStartDate(BuildContext context) async {
    // Use the earliest date from entries if available, otherwise default to 1 year ago
    final firstDate = widget.earliestDate ??
        DateTime.now().subtract(const Duration(days: 365));

    // Use the latest date from entries if available, otherwise default to today
    final lastDate = widget.latestDate ?? DateTime.now();

    // For initial date, use the filter's start date if set, otherwise use the earliest date
    final initialDate = _filter.startDate ??
        (_filter.endDate != null && _filter.endDate!.isAfter(firstDate)
            ? firstDate
            : (_filter.endDate ?? firstDate));

    // Use our new DatePickerDialog class
    final selectedDate =
        await custom_date_picker.DatePickerDialog.showDatePicker(
      context: context,
      title: 'Select Start Date',
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: _filter.endDate ?? lastDate,
      confirmText: 'Select',
      cancelText: 'Cancel',
      helpText: 'Choose a start date for filtering',
    );

    if (selectedDate != null) {
      setState(() {
        _filter = _filter.copyWith(startDate: selectedDate);
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    // Use the earliest date from entries if available, otherwise default to 1 year ago
    final firstDate = widget.earliestDate ??
        DateTime.now().subtract(const Duration(days: 365));

    // Use the latest date from entries if available, otherwise default to today
    final lastDate = widget.latestDate ?? DateTime.now();

    // For initial date, use the filter's end date if set, otherwise use the latest date
    final initialDate = _filter.endDate ??
        (_filter.startDate != null && _filter.startDate!.isBefore(lastDate)
            ? lastDate
            : (_filter.startDate ?? lastDate));

    // Use our new DatePickerDialog class
    final selectedDate =
        await custom_date_picker.DatePickerDialog.showDatePicker(
      context: context,
      title: 'Select End Date',
      initialDate: initialDate,
      firstDate: _filter.startDate ?? firstDate,
      lastDate: lastDate,
      confirmText: 'Select',
      cancelText: 'Cancel',
      helpText: 'Choose an end date for filtering',
    );

    if (selectedDate != null) {
      setState(() {
        _filter = _filter.copyWith(endDate: selectedDate);
      });
    }
  }
}
