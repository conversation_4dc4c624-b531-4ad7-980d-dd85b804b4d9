// File: lib/core/widgets/dialogs/selection_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../app_dialog.dart';

/// A specialized dialog for allowing users to choose from multiple options.
///
/// This dialog presents a clear title, a list of options to choose from,
/// and Save/Cancel buttons.
class SelectionDialog {
  /// Shows a selection dialog with radio buttons for single selection.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate what to select.
  /// - [options]: The list of options to choose from.
  /// - [selectedOption]: The currently selected option.
  /// - [labelBuilder]: A function that builds a label for each option.
  /// - [subtitleBuilder]: An optional function that builds a subtitle for each option.
  /// - [saveText]: The text for the save button (default: "Save").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [message]: An optional message to display above the options.
  static Future<T?> showSingleSelection<T>({
    required BuildContext context,
    required String title,
    required List<T> options,
    T? selectedOption,
    required String Function(T) labelBuilder,
    String? Function(T)? subtitleBuilder,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? message,
  }) async {
    T? selected = selectedOption;

    return showDialog<T>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AppDialog(
              title: title,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message != null) ...[
                    Text(
                      message,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.onSurfaceDark.withOpacity(0.8)
                            : AppColors.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: options.map((option) {
                          return RadioListTile<T>(
                            title: Text(labelBuilder(option)),
                            subtitle: subtitleBuilder != null
                                ? (subtitleBuilder(option) != null
                                    ? Text(subtitleBuilder(option)!)
                                    : null)
                                : null,
                            value: option,
                            groupValue: selected,
                            onChanged: (value) {
                              setState(() {
                                selected = value;
                              });
                            },
                            activeColor:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                            dense: true,
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primaryDark
                            : AppColors.primary,
                    side: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(cancelText),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(selected),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primaryDark
                            : AppColors.primary,
                  ),
                  child: Text(saveText),
                ),
              ],
              scrollable: true,
            );
          },
        );
      },
    );
  }

  /// Shows a selection dialog with checkboxes for multiple selection.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate what to select.
  /// - [options]: The list of options to choose from.
  /// - [selectedOptions]: The list of currently selected options.
  /// - [labelBuilder]: A function that builds a label for each option.
  /// - [subtitleBuilder]: An optional function that builds a subtitle for each option.
  /// - [saveText]: The text for the save button (default: "Save").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [message]: An optional message to display above the options.
  static Future<List<T>?> showMultiSelection<T>({
    required BuildContext context,
    required String title,
    required List<T> options,
    List<T>? selectedOptions,
    required String Function(T) labelBuilder,
    String? Function(T)? subtitleBuilder,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? message,
  }) async {
    List<T> selected = List<T>.from(selectedOptions ?? []);

    return showDialog<List<T>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AppDialog(
              title: title,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message != null) ...[
                    Text(
                      message,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.onSurfaceDark.withOpacity(0.8)
                            : AppColors.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: options.map((option) {
                          return CheckboxListTile(
                            title: Text(labelBuilder(option)),
                            subtitle: subtitleBuilder != null
                                ? (subtitleBuilder(option) != null
                                    ? Text(subtitleBuilder(option)!)
                                    : null)
                                : null,
                            value: selected.contains(option),
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                  if (!selected.contains(option)) {
                                    selected.add(option);
                                  }
                                } else {
                                  selected.remove(option);
                                }
                              });
                            },
                            activeColor:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                            dense: true,
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primaryDark
                            : AppColors.primary,
                    side: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(cancelText),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(selected),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primaryDark
                            : AppColors.primary,
                  ),
                  child: Text(saveText),
                ),
              ],
              scrollable: true,
            );
          },
        );
      },
    );
  }

  /// Shows a custom selection dialog with a provided widget as content.
  ///
  /// This is useful for complex selection interfaces that can't be represented
  /// by simple radio buttons or checkboxes.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [content]: The custom widget to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  /// - [icon]: An optional icon to display in the dialog title.
  /// - [iconColor]: The color of the icon (if provided).
  static Future<void> showCustom({
    required BuildContext context,
    required String title,
    required Widget content,
    bool barrierDismissible = true,
    IconData? icon,
    Color? iconColor,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Determine the icon color if not specified
    final Color effectiveIconColor =
        iconColor ?? (isDarkMode ? AppColors.primaryDark : AppColors.primary);

    // Create the title widget with the icon if provided
    Widget titleWidget = Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.bold,
        color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
        fontSize: 20,
      ),
    );

    if (icon != null) {
      titleWidget = Row(
        children: [
          Icon(icon, color: effectiveIconColor),
          const SizedBox(width: 10),
          Expanded(child: titleWidget),
        ],
      );
    }

    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AppDialog(
        title: titleWidget,
        content: content,
        scrollable: true,
      ),
    );
  }
}
