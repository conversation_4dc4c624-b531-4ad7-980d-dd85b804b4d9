// File: lib/features/welcome/presentation/screens/welcome_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/shared_modules/data_import_widget.dart' as data_import;
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../features/backup/backup_service.dart';
import '../../../../features/settings/presentation/controllers/settings_controller.dart';
import '../widgets/welcome_feature_item.dart';

/// The welcome screen of the app
class WelcomeScreen extends StatelessWidget {
  WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF003087),
            Color(0xFF0057B8)
          ], // Primary gradient from lekky_pallet.md
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom -
                    48.0, // Account for padding
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      const SizedBox(height: 24),
                      _buildHeader(),
                      const SizedBox(height: 48),
                      _buildFeaturesList(),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 32.0, bottom: 16.0),
                    child: _buildGetStartedButton(context),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const Icon(
          Icons.electric_meter,
          size: 80,
          color: Colors.white,
        ),
        const SizedBox(height: 16),
        Text(
          'Welcome to Lekky',
          style: AppTextStyles.headlineLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Your personal prepaid meter assistant',
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFeaturesList() {
    return Column(
      children: const [
        WelcomeFeatureItem(
          icon: Icons.track_changes,
          title: 'Track Your Usage',
          description: 'Monitor your electricity consumption and spending',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.notifications,
          title: 'Get Timely Alerts',
          description: 'Receive notifications when your balance is running low',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.history,
          title: 'View History',
          description: 'See your past meter readings and top-ups',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.attach_money,
          title: 'Calculate Costs',
          description: 'Estimate your electricity costs over different periods',
        ),
      ],
    );
  }

  Widget _buildGetStartedButton(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Check for backup data
        FutureBuilder<bool>(
          future: _checkForBackupData(),
          builder: (context, snapshot) {
            // Show loading indicator while checking for backup
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox(
                height: 48,
                child: Center(
                  child: Text(
                    "Checking for previous data...",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              );
            }

            // Show restore button if backup exists
            if (snapshot.hasData && snapshot.data == true) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OutlinedButton.icon(
                    onPressed: () => _showRestoreDataDialog(context),
                    icon: const Icon(Icons.restore),
                    label: const Text('Restore Previous Data'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Backup file found on this device',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                  const SizedBox(height: 16),
                ],
              );
            }

            // No backup found
            return const SizedBox(
              height: 48,
              child: Center(
                child: Text(
                  "Sorry, no backup file exists on this device.",
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ),
            );
          },
        ),
        // Get Started button
        GradientButton(
          text: 'Get Started',
          onPressed: () {
            Navigator.of(context).pushReplacementNamed(AppConstants.routeSetup);
          },
          gradientColors: const [
            Color(0xFF1976D2),
            Color(0xFF42A5F5)
          ], // Primary color from lekky_pallet.md
          icon: const Icon(
            Icons.arrow_forward,
            color: Colors.white,
          ),
          width: double.infinity,
        ),
      ],
    );
  }

  // Create an instance of the BackupService
  final BackupService _backupService = BackupService();

  Future<bool> _checkForBackupData() async {
    try {
      // Check if backup file exists using the BackupService
      final exists = await _backupService.backupFileExists();

      if (exists) {
        debugPrint('Backup file found');
        return true;
      } else {
        debugPrint('No backup file found');
        return false;
      }
    } catch (e) {
      debugPrint('Error checking for backup data: $e');
      return false;
    }
  }

  void _showRestoreDataDialog(BuildContext context) {
    // Get the settings controller
    final settingsController =
        Provider.of<SettingsController>(context, listen: false);

    // Track import state
    bool isImporting = false;
    String? importResult;
    String? errorText;

    showDialog(
      context: context,
      builder: (dialogContext) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.restore,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text('Restore Data'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Would you like to restore your previously backed up data? This will skip the setup process and take you directly to the home screen.',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),

                // Use the shared DataImportWidget
                SizedBox(
                  width: double.maxFinite,
                  child: data_import.DataImportWidget(
                    importMode: data_import.ImportMode.automatic,
                    clearExistingData: true,
                    isImporting: isImporting,
                    importResult: importResult,
                    errorText: errorText,
                    onImportSuccess: (entries) async {
                      setState(() {
                        isImporting = false;
                        importResult =
                            'Successfully imported ${entries.length} entries';
                      });

                      try {
                        // Use the existing restoreDataFromBackup method which handles all these steps
                        final result =
                            await settingsController.restoreDataFromBackup();

                        if (result.isFailure) {
                          setState(() {
                            errorText =
                                "Failed to restore data: ${result.error.message}";
                          });
                        }
                      } catch (e) {
                        setState(() {
                          errorText = "Error: $e";
                        });
                      }

                      // Navigate to home screen after a short delay
                      Future.delayed(const Duration(seconds: 1), () {
                        if (context.mounted) {
                          Navigator.of(context).pop(); // Close dialog
                          Navigator.of(context)
                              .pushReplacementNamed(AppConstants.routeHome);
                        }
                      });
                    },
                    onImportError: (message) {
                      setState(() {
                        isImporting = false;
                        errorText = message;
                      });
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          );
        });
      },
    );
  }
}
