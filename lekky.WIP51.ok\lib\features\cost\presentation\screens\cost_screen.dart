// File: lib/features/cost/presentation/screens/cost_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/widgets/app_card.dart';

import '../../domain/models/cost_period.dart';
import '../../domain/utils/cost_message_provider.dart';
import '../controllers/cost_controller.dart';

/// The cost screen of the app
class CostScreen extends StatefulWidget {
  const CostScreen({super.key});

  @override
  State<CostScreen> createState() => _CostScreenState();
}

class _CostScreenState extends State<CostScreen> {
  // Focus node for initial unfocus
  final FocusNode _dummyFocusNode = FocusNode();

  // Index for the current message to display
  int _currentMessageIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CostController>().init();
      // Ensure no buttons are focused on screen load
      FocusScope.of(context).requestFocus(_dummyFocusNode);
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  void dispose() {
    _dummyFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<CostController>(
      builder: (context, controller, _) {
        return RefreshIndicator(
          onRefresh: () async {
            await controller.init();
          },
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    if (controller.isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 32),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else
                      _buildCostCard(controller),
                    if (controller.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          controller.error,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.errorDark
                                    : AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ]),
                ),
              ),
              // Add a dummy focus node to request focus on load
              SliverToBoxAdapter(
                child: SizedBox(
                  width: 0,
                  height: 0,
                  child: Focus(
                    focusNode: _dummyFocusNode,
                    child: Container(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
      height: 32, // Reduced height to 32px to match Homepage
      child: Center(
        // Ensure text is perfectly centered
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            HelpfulMessages.allMessages[_currentMessageIndex],
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDarkMode ? Colors.white70 : Colors.black87,
              height: 1.0, // Ensure single line height
              fontSize: 13.0, // Slightly smaller font size to fit more text
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px to match Homepage
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use fixed height Stack instead of FlexibleSpaceBar
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.costGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - matching Home screen style and position
            const Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Home screen
              child: Text(
                'Cost', // Banner text
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Matching Home screen font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
      // Remove notification button
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }

  Widget _buildCostCard(CostController controller) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.primaryTextDark : AppColors.primary;

    // Get screen dimensions to make the card extend to the bottom
    // Match the position of the Add Entry button on the Homepage
    final screenHeight = MediaQuery.of(context).size.height;
    const appBarHeight = 96 + 32; // Banner (96px) + Message bar (32px)
    const paddingHeight = 32; // Top and bottom padding (16px each)
    const bottomSafeArea = 16; // Additional padding for bottom safe area
    const bottomNavHeight =
        80; // Match the height of the Add Entry button (80px)
    final availableHeight = screenHeight -
        appBarHeight -
        paddingHeight -
        bottomSafeArea -
        bottomNavHeight;

    return AppCard(
      padding: const EdgeInsets.all(16),
      elevation: 2,
      borderRadius: BorderRadius.circular(16),
      backgroundColor: null, // Use default from AppCard
      hasShadow: true,
      child: Container(
        // Use constraints to ensure the card doesn't extend beyond the screen
        constraints: BoxConstraints(
          maxHeight: availableHeight,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and icon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Cost of Electric',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: labelColor,
                  ),
                ),
                const CircleAvatar(
                  backgroundColor: AppColors.costTab,
                  radius: 14,
                  child: Icon(
                    Icons.attach_money,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Fixed height container for cost display to prevent layout shifts
            SizedBox(
              height: 120, // Adjusted height to accommodate combined text
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Cost Display - styled like MeterTotal on homepage
                  Text(
                    controller.formattedCostPerPeriod,
                    style: AppTextStyles.valueText.copyWith(
                      fontSize: 48, // Increased font size to match homepage
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                    ),
                  ),

                  // Combined period and calculation method text
                  SizedBox(
                    height: 40, // Fixed height for 2 lines of text
                    child: Text(
                      _getCombinedPeriodText(controller),
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontSize: 16, // Increased font size to match homepage
                        color: isDarkMode ? Colors.white70 : Colors.black87,
                        height:
                            1.2, // Slightly tighter line height for better fit
                      ),
                      textAlign: TextAlign.left,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // Divider between Cost Display and Period Selector
            Divider(
              color: isDarkMode ? Colors.white24 : Colors.black12,
              height: 16, // Reduced height
            ),

            // Period buttons (Day, Week, Month, Year) - using future periods
            Row(
              children: CostPeriod.futurePeriods.map((period) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2.0),
                    child: _buildPeriodButton(period, controller),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 12),

            // Date pickers row
            Row(
              children: [
                // FROM date picker
                Expanded(
                  child: _buildDatePickerField(
                    'From',
                    controller.dateRange.startDate,
                    (date) {
                      controller.fromDate = date;
                      if (controller.selectedPeriod != CostPeriod.custom) {
                        controller.updatePeriod(CostPeriod.custom);
                      }
                    },
                    true,
                    controller,
                    isDarkMode,
                  ),
                ),

                const SizedBox(width: 8),

                // TO date picker
                Expanded(
                  child: _buildDatePickerField(
                    'To',
                    controller.dateRange.endDate,
                    (date) {
                      controller.toDate = date;
                      if (controller.selectedPeriod != CostPeriod.custom) {
                        controller.updatePeriod(CostPeriod.custom);
                      }
                    },
                    false,
                    controller,
                    isDarkMode,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Custom button
            SizedBox(
              width: double.infinity,
              child: _buildCustomButton(controller, isDarkMode),
            ),

            // Error message if applicable
            if (controller.dateRangeError != null &&
                controller.dateRangeError!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  controller.dateRangeError!,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isDarkMode ? AppColors.errorDark : AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Build a period button (Day, Week, Month, Year)
  Widget _buildPeriodButton(CostPeriod period, CostController controller) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = controller.selectedPeriod == period;

    return SizedBox(
      height: 40,
      child: ElevatedButton(
        onPressed: () => controller.updatePeriod(period),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected
              ? (isDarkMode ? AppColors.primaryDark : AppColors.primary)
              : Colors.transparent,
          foregroundColor: isSelected
              ? Colors.white
              : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
          elevation: isSelected ? 2 : 0,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
            side: BorderSide(
              color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
              width: 1,
            ),
          ),
        ),
        child: Text(
          period.name,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  // Build a date picker field
  Widget _buildDatePickerField(
    String label,
    DateTime? date,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
    CostController controller,
    bool isDarkMode,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white70 : Colors.black87,
          ),
        ),
        const SizedBox(height: 4),

        // Date field
        InkWell(
          onTap: () => _selectDate(
            context,
            date,
            onDateChanged,
            isStartDate,
            controller,
          ),
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: isDarkMode ? Colors.white30 : Colors.black26,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Date text
                Text(
                  date != null
                      ? _formatDate(date)
                      : 'Today', // Both from and to dates default to today
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),

                // Calendar icon
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: isDarkMode ? Colors.white54 : Colors.black54,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build the custom button
  Widget _buildCustomButton(CostController controller, bool isDarkMode) {
    final isSelected = controller.selectedPeriod == CostPeriod.custom;
    final hasInvalidRecords = controller.hasInvalidHistoryRecords;

    return ElevatedButton(
      onPressed: hasInvalidRecords
          ? () {
              // Show error message if there are invalid history records
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    "Please fix any invalid history entries.",
                    style: TextStyle(color: Colors.white),
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          : () => controller.updatePeriod(CostPeriod.custom),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected
            ? (isDarkMode ? AppColors.primaryDark : AppColors.primary)
            : Colors.transparent,
        foregroundColor: isSelected
            ? Colors.white
            : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
        elevation: isSelected ? 2 : 0,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
          side: BorderSide(
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
            width: 1,
          ),
        ),
      ),
      child: Text(
        'Custom',
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  // Format a date as a string
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Show date picker dialog
  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentDate,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
    CostController controller,
  ) async {
    // Check if total average is valid (not 0 or null)
    final totalAverage = await controller.getTotalAverageUsage();
    final isTotalAverageValid = totalAverage > 0;

    // Check if the widget is still mounted before proceeding
    if (!mounted) return;

    // Now that we've done the async work, we can use the context safely
    // We need to use a local variable to avoid BuildContext across async gaps warning
    final localContext = context;

    // Only proceed if the widget is still mounted
    if (mounted) {
      _showDatePickerDialog(localContext, currentDate, onDateChanged,
          isStartDate, controller, isTotalAverageValid);
    }
  }

  // Helper method to show the date picker dialog after async validation
  void _showDatePickerDialog(
    BuildContext context,
    DateTime? currentDate,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
    CostController controller,
    bool isTotalAverageValid,
  ) {
    // Determine the valid date range for this field
    final today = DateTime.now();
    final fiveYearsFromNow = today.add(const Duration(days: 365 * 5));

    // Default values
    DateTime? firstDate;
    DateTime? lastDate;

    if (isStartDate) {
      // "From" date constraints
      // Minimum date is the earliest meter reading date
      firstDate = controller.earliestDate;

      // Maximum date is the currently selected "To" date (can be the same day)
      lastDate = controller.dateRange.endDate;

      // If no valid dates are available or total average is not valid, show error message
      if (firstDate == null || !isTotalAverageValid) {
        // No meter readings exist or total average is 0
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              firstDate == null
                  ? "Insufficient Meter Readings."
                  : "Cannot calculate cost with zero average usage.",
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
        return; // Exit the method
      }

      // If no "To" date is set, use today or latest reading date
      lastDate ??= controller.latestDate ?? today;
    } else {
      // "To" date constraints
      // Minimum date is the currently selected "From" date (can be the same day)
      firstDate = controller.dateRange.startDate;

      // Maximum date is five years from now
      lastDate = fiveYearsFromNow;

      // If no 'from date' is available or total average is not valid, then 'date to' also has no date available
      if (firstDate == null || !isTotalAverageValid) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              firstDate == null
                  ? "Please select a 'From' date first."
                  : "Cannot calculate cost with zero average usage.",
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
        return; // Exit the method
      }
    }

    // Set a valid initial date
    DateTime initialDate;
    if (currentDate != null) {
      initialDate = currentDate;
    } else {
      initialDate = today; // Both from and to dates default to today
    }

    // Make sure initialDate is within the valid range
    if (initialDate.isBefore(firstDate)) {
      initialDate = firstDate;
    } else if (initialDate.isAfter(lastDate)) {
      initialDate = lastDate;
    }

    // Get the current theme mode
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Show the date picker
    showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: isDarkMode
                ? ColorScheme.dark(
                    primary: AppColors.primaryDark,
                    onPrimary: Colors.white,
                    surface: Theme.of(context).cardColor,
                    onSurface: Colors.white,
                  )
                : ColorScheme.light(
                    primary: AppColors.primary,
                    onPrimary: Colors.white,
                    surface: Theme.of(context).cardColor,
                    onSurface: Colors.black87,
                  ),
          ),
          child: child!,
        );
      },
    ).then((picked) {
      if (picked != null) {
        // Add time component to the picked date
        final DateTime dateWithTime = isStartDate
            ? DateTime(picked.year, picked.month, picked.day, 0,
                1) // 00:01 for 'from date'
            : DateTime(picked.year, picked.month, picked.day, 23,
                59); // 23:59 for 'to date'

        onDateChanged(dateWithTime);
      }
    });
  }

  /// Returns a combined message with period and calculation method
  String _getCombinedPeriodText(CostController controller) {
    // Get the time frame text
    String timeFrameText;
    if (controller.selectedPeriod == CostPeriod.custom) {
      // Use singular form "day" when the period is exactly 1 day
      timeFrameText = controller.dateRange.days == 1
          ? '1 day'
          : '${controller.dateRange.days} days';

      // For custom periods, use "for" prefix
      String prefix = 'for';

      // Get the calculation method message for custom periods
      String calculationMethod = CostMessageProvider.getAverageUsageMessage(
        selectedPeriod: controller.selectedPeriod,
        fromDate: controller.fromDate,
        toDate: controller.toDate,
        earliestDate: controller.earliestDate,
        latestDate: controller.latestDate,
      );

      // Extract just the calculation basis part
      String calculationBasis;
      if (calculationMethod.contains('total average')) {
        calculationBasis = 'your total average use.';
      } else if (calculationMethod.contains('records and averages')) {
        calculationBasis = 'your records and total averages.';
      } else {
        calculationBasis = 'your records.';
      }

      // Combine the messages for custom periods
      return '$prefix $timeFrameText based on $calculationBasis';
    } else {
      // For standard periods (day, week, month, year), always use total average
      final period = controller.selectedPeriod.name.toLowerCase();
      timeFrameText = 'per $period'; // Use "per" instead of "for a"

      // Standard periods always use total average
      String calculationBasis = 'your total average use.';

      // Combine the messages for standard periods
      return '$timeFrameText based on $calculationBasis';
    }
  }
}
