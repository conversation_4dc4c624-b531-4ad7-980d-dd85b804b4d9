﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{215E8837-09C0-382F-9413-18481F5E98D0}"
	ProjectSection(ProjectDependencies) = postProject
		{DB719A9E-E134-3F3B-AC29-642D030D70BF} = {DB719A9E-E134-3F3B-AC29-642D030D70BF}
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0} = {9F9856C6-9E87-3D87-981B-1DEAA1D757E0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{CDFBAD82-9A80-3CF2-8C4E-D4A632300B40}"
	ProjectSection(ProjectDependencies) = postProject
		{215E8837-09C0-382F-9413-18481F5E98D0} = {215E8837-09C0-382F-9413-18481F5E98D0}
		{DB719A9E-E134-3F3B-AC29-642D030D70BF} = {DB719A9E-E134-3F3B-AC29-642D030D70BF}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{DB719A9E-E134-3F3B-AC29-642D030D70BF}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{22F738A7-9A23-3ED1-ADF4-0F95F8793388}"
	ProjectSection(ProjectDependencies) = postProject
		{DB719A9E-E134-3F3B-AC29-642D030D70BF} = {DB719A9E-E134-3F3B-AC29-642D030D70BF}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}"
	ProjectSection(ProjectDependencies) = postProject
		{DB719A9E-E134-3F3B-AC29-642D030D70BF} = {DB719A9E-E134-3F3B-AC29-642D030D70BF}
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388} = {22F738A7-9A23-3ED1-ADF4-0F95F8793388}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "permission_handler_windows_plugin", "permission_handler_windows_plugin.vcxproj", "{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}"
	ProjectSection(ProjectDependencies) = postProject
		{DB719A9E-E134-3F3B-AC29-642D030D70BF} = {DB719A9E-E134-3F3B-AC29-642D030D70BF}
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388} = {22F738A7-9A23-3ED1-ADF4-0F95F8793388}
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0} = {EEF913B2-9794-35D9-9A0C-573E5DC33AF0}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{215E8837-09C0-382F-9413-18481F5E98D0}.Debug|x64.ActiveCfg = Debug|x64
		{215E8837-09C0-382F-9413-18481F5E98D0}.Debug|x64.Build.0 = Debug|x64
		{215E8837-09C0-382F-9413-18481F5E98D0}.Profile|x64.ActiveCfg = Profile|x64
		{215E8837-09C0-382F-9413-18481F5E98D0}.Profile|x64.Build.0 = Profile|x64
		{215E8837-09C0-382F-9413-18481F5E98D0}.Release|x64.ActiveCfg = Release|x64
		{215E8837-09C0-382F-9413-18481F5E98D0}.Release|x64.Build.0 = Release|x64
		{CDFBAD82-9A80-3CF2-8C4E-D4A632300B40}.Debug|x64.ActiveCfg = Debug|x64
		{CDFBAD82-9A80-3CF2-8C4E-D4A632300B40}.Profile|x64.ActiveCfg = Profile|x64
		{CDFBAD82-9A80-3CF2-8C4E-D4A632300B40}.Release|x64.ActiveCfg = Release|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Debug|x64.ActiveCfg = Debug|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Debug|x64.Build.0 = Debug|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Profile|x64.ActiveCfg = Profile|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Profile|x64.Build.0 = Profile|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Release|x64.ActiveCfg = Release|x64
		{DB719A9E-E134-3F3B-AC29-642D030D70BF}.Release|x64.Build.0 = Release|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Debug|x64.ActiveCfg = Debug|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Debug|x64.Build.0 = Debug|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Profile|x64.ActiveCfg = Profile|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Profile|x64.Build.0 = Profile|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Release|x64.ActiveCfg = Release|x64
		{22F738A7-9A23-3ED1-ADF4-0F95F8793388}.Release|x64.Build.0 = Release|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Debug|x64.ActiveCfg = Debug|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Debug|x64.Build.0 = Debug|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Profile|x64.ActiveCfg = Profile|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Profile|x64.Build.0 = Profile|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Release|x64.ActiveCfg = Release|x64
		{EEF913B2-9794-35D9-9A0C-573E5DC33AF0}.Release|x64.Build.0 = Release|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Debug|x64.ActiveCfg = Debug|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Debug|x64.Build.0 = Debug|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Profile|x64.ActiveCfg = Profile|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Profile|x64.Build.0 = Profile|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Release|x64.ActiveCfg = Release|x64
		{9F9856C6-9E87-3D87-981B-1DEAA1D757E0}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6A9A8752-09AE-364B-8229-F28EB657185D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
