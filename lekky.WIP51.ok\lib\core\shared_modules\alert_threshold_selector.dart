// File: lib/core/shared_modules/alert_threshold_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_text_field.dart';
import 'settings_model.dart';

class AlertThresholdSelector extends StatefulWidget {
  final double currentValue;
  final ValueChanged<double> onChanged;
  final String currencySymbol;
  final String? errorText;
  final bool hasMeterReadings;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const AlertThresholdSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    this.hasMeterReadings = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  @override
  State<AlertThresholdSelector> createState() => _AlertThresholdSelectorState();
}

class _AlertThresholdSelectorState extends State<AlertThresholdSelector> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toStringAsFixed(2),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant AlertThresholdSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus && widget.currentValue != oldWidget.currentValue) {
      _controller.text = widget.currentValue.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.displayMode == SettingsDisplayMode.expanded
        ? _buildExpandedView(context)
        : _buildCompactView(context);
  }

  Widget _buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildSectionTitle(context, 'Alert Threshold'),

        if (widget.showHelperText)
          _buildHelperText(context,
              'You will be notified when your balance falls below this amount.'),

        if (!widget.hasMeterReadings) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber[800], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Low balance alerts will be active after you enter your first meter reading.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.amber[800]
                          : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Slider for quick adjustment
        Row(
          children: [
            Text('${widget.currencySymbol}0',
                style: const TextStyle(fontSize: 12)),
            Expanded(
              child: Slider(
                value: widget.currentValue.clamp(0.0, 50.0),
                min: 0.0,
                max: 50.0,
                divisions: 100,
                label:
                    '${widget.currencySymbol}${widget.currentValue.toStringAsFixed(2)}',
                onChanged: widget.onChanged,
              ),
            ),
            Text('${widget.currencySymbol}50',
                style: const TextStyle(fontSize: 12)),
          ],
        ),

        const SizedBox(height: 8),

        // Precise input field
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
          ],
          labelText: 'Alert Threshold Value',
          prefixText: widget.currencySymbol,
          helperText: 'Enter a specific amount',
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        const SizedBox(height: 8),
        Text(
          'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }

  Widget _buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildLabel(context, 'Alert Threshold'),

        if (widget.showTitle)
          Text(
            'Current: ${widget.currencySymbol}${widget.currentValue.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (!widget.hasMeterReadings) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[800], size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Requires meter reading',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.amber[800] : Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ],

        if (widget.showHelperText)
          _buildHelperText(context, 'Set when to receive low balance alerts'),

        const SizedBox(height: 8),

        // Slider - more compact
        Slider(
          value: widget.currentValue.clamp(0.0, 50.0),
          min: 0.0,
          max: 50.0,
          divisions: 100,
          label:
              '${widget.currencySymbol}${widget.currentValue.toStringAsFixed(2)}',
          onChanged: widget.onChanged,
        ),

        // Precise input field - more compact
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
          ],
          labelText: 'Custom',
          prefixText: widget.currencySymbol,
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLabel(BuildContext context, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildHelperText(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }
}
