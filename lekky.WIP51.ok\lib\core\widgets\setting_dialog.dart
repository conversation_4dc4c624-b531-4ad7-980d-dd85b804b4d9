// File: lib/core/widgets/setting_dialog.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../utils/dialog_button_styles.dart';

/// A reusable dialog for settings
class SettingDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget content;
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final String cancelText;
  final String saveText;

  const SettingDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.content,
    this.onCancel,
    this.onSave,
    this.cancelText = 'Cancel',
    this.saveText = 'Save',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;

    return Dialog(
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 16),
            content,
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (onCancel != null)
                  DialogButtonStyles.createCancelButton(
                    context: context,
                    onPressed: () {
                      onCancel?.call();
                      Navigator.of(context).pop();
                    },
                    text: cancelText,
                  ),
                if (onSave != null) ...[
                  const SizedBox(width: 8),
                  DialogButtonStyles.createSaveButton(
                    context: context,
                    onPressed: onSave!,
                    text: saveText,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
