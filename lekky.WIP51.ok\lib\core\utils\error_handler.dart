// File: lib/core/utils/error_handler.dart
import 'package:flutter/foundation.dart';

/// Error severity levels
enum ErrorSeverity {
  /// Low severity - warning only
  low,

  /// Medium severity - may affect functionality
  medium,

  /// High severity - critical error
  high,
}

/// Error types
enum ErrorType {
  /// Network error
  network,

  /// Database error
  database,

  /// Validation error
  validation,

  /// Authentication error
  authentication,

  /// Permission error
  permission,

  /// Unknown error
  unknown,
}

/// Error model
class AppError {
  /// Error message
  final String message;

  /// Error severity
  final ErrorSeverity severity;

  /// Error type
  final ErrorType type;

  /// Error details
  final dynamic details;

  /// Error timestamp
  final DateTime timestamp;

  /// Error stack trace
  final StackTrace? stackTrace;

  /// Create a new error
  AppError({
    required this.message,
    this.severity = ErrorSeverity.medium,
    this.type = ErrorType.unknown,
    this.details,
    StackTrace? stackTrace,
  })  : timestamp = DateTime.now(),
        stackTrace = stackTrace ?? StackTrace.current;

  /// Create a network error
  factory AppError.network(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.medium,
      type: ErrorType.network,
      details: details,
    );
  }

  /// Create a database error
  factory AppError.database(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.high,
      type: ErrorType.database,
      details: details,
    );
  }

  /// Create a validation error
  factory AppError.validation(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.low,
      type: ErrorType.validation,
      details: details,
    );
  }

  /// Create an authentication error
  factory AppError.authentication(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.high,
      type: ErrorType.authentication,
      details: details,
    );
  }

  /// Create a permission error
  factory AppError.permission(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.medium,
      type: ErrorType.permission,
      details: details,
    );
  }

  /// Create an unknown error
  factory AppError.unknown(String message, {dynamic details}) {
    return AppError(
      message: message,
      severity: ErrorSeverity.medium,
      type: ErrorType.unknown,
      details: details,
    );
  }

  /// Convert to string
  @override
  String toString() {
    return 'AppError: $message (${type.toString().split('.').last}, ${severity.toString().split('.').last})';
  }

  /// Log the error
  void log() {
    if (kDebugMode) {
      print('ERROR: $this');
      if (details != null) {
        print('Details: $details');
      }
      if (stackTrace != null) {
        print('Stack trace: $stackTrace');
      }
    }
  }
}

/// Error handler
class ErrorHandler {
  /// List of errors
  static final List<AppError> _errors = [];

  /// Get all errors
  static List<AppError> get errors => List.unmodifiable(_errors);

  /// Add an error
  static void addError(AppError error) {
    _errors.add(error);
    error.log();
  }

  /// Clear all errors
  static void clearErrors() {
    _errors.clear();
  }

  /// Get errors by type
  static List<AppError> getErrorsByType(ErrorType type) {
    return _errors.where((error) => error.type == type).toList();
  }

  /// Get errors by severity
  static List<AppError> getErrorsBySeverity(ErrorSeverity severity) {
    return _errors.where((error) => error.severity == severity).toList();
  }

  /// Handle an exception
  static AppError handleException(dynamic exception, {StackTrace? stackTrace}) {
    final error = AppError(
      message: exception.toString(),
      stackTrace: stackTrace,
    );
    addError(error);
    return error;
  }
}
