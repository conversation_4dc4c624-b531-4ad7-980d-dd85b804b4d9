// File: lib/features/cost/presentation/widgets/cost_summary_card.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A card that displays cost summary information
class CostSummaryCard extends StatelessWidget {
  final double totalCost;
  final double averageDailyCost;
  final double estimatedMonthlyCost;
  final double estimatedYearlyCost;
  final String meterUnit;

  const CostSummaryCard({
    super.key,
    required this.totalCost,
    required this.averageDailyCost,
    required this.estimatedMonthlyCost,
    required this.estimatedYearlyCost,
    required this.meterUnit,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cost Summary',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 16),
          _buildTotalCost(),
          const Divider(height: 32),
          _buildCostDetails(),
        ],
      ),
    );
  }

  Widget _buildTotalCost() {
    return Center(
      child: Column(
        children: [
          Text(
            'Total Cost',
            style: AppTextStyles.titleSmall.copyWith(
              color: AppColors.secondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '$meterUnit${totalCost.toStringAsFixed(2)}',
            style: AppTextStyles.headlineLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostDetails() {
    return Column(
      children: [
        _buildCostItem(
          'Average Daily Cost',
          '$meterUnit${averageDailyCost.toStringAsFixed(2)}',
          Icons.calendar_today,
        ),
        const SizedBox(height: 16),
        _buildCostItem(
          'Estimated Monthly Cost',
          '$meterUnit${estimatedMonthlyCost.toStringAsFixed(2)}',
          Icons.date_range,
        ),
        const SizedBox(height: 16),
        _buildCostItem(
          'Estimated Yearly Cost',
          '$meterUnit${estimatedYearlyCost.toStringAsFixed(2)}',
          Icons.calendar_month,
        ),
      ],
    );
  }

  Widget _buildCostItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.secondary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
