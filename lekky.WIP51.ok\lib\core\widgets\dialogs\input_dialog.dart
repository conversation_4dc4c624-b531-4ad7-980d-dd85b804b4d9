// File: lib/core/widgets/dialogs/input_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/dialog_button_styles.dart';
import '../app_dialog.dart';
import '../app_text_field.dart';

/// A specialized dialog for collecting specific data from users.
///
/// This dialog presents a clear title, appropriate input fields with validation,
/// and Save/Cancel buttons.
class InputDialog {
  /// Shows an input dialog with a single text field.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [labelText]: The label for the text field.
  /// - [hintText]: The hint text for the text field.
  /// - [initialValue]: The initial value for the text field.
  /// - [validator]: A function that validates the input and returns an error message if invalid.
  /// - [keyboardType]: The keyboard type for the text field.
  /// - [inputFormatters]: Input formatters for the text field.
  /// - [saveText]: The text for the save button (default: "Save").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helperText]: Optional helper text to provide guidance.
  /// - [prefixText]: Optional prefix text for the text field (e.g., currency symbol).
  /// - [maxLength]: Optional maximum length for the input.
  static Future<String?> show({
    required BuildContext context,
    required String title,
    required String labelText,
    String? hintText,
    String? initialValue,
    String? Function(String?)? validator,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? helperText,
    String? prefixText,
    int? maxLength,
  }) async {
    final TextEditingController controller =
        TextEditingController(text: initialValue);
    String? errorText;

    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AppDialog(
              title: title,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (helperText != null) ...[
                    Text(
                      helperText,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.onSurfaceDark.withOpacity(0.8)
                            : AppColors.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  AppTextField(
                    controller: controller,
                    labelText: labelText,
                    hintText: hintText,
                    errorText: errorText,
                    keyboardType: keyboardType,
                    inputFormatters: inputFormatters,
                    prefixText: prefixText,
                    maxLength: maxLength,
                    autofocus: true,
                    onChanged: (value) {
                      if (errorText != null) {
                        setState(() {
                          errorText = validator?.call(value);
                        });
                      }
                    },
                  ),
                ],
              ),
              actions: [
                DialogButtonStyles.createCancelButton(
                  context: context,
                  onPressed: () => Navigator.of(context).pop(),
                  text: cancelText,
                ),
                DialogButtonStyles.createSaveButton(
                  context: context,
                  onPressed: () {
                    final value = controller.text;
                    final error = validator?.call(value);

                    if (error != null) {
                      setState(() {
                        errorText = error;
                      });
                    } else {
                      Navigator.of(context).pop(value);
                    }
                  },
                  text: saveText,
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Shows a numeric input dialog for collecting numeric values.
  ///
  /// This is a specialized version of the input dialog for numeric values,
  /// with appropriate keyboard type and input formatters.
  static Future<double?> showNumeric({
    required BuildContext context,
    required String title,
    required String labelText,
    String? hintText,
    double? initialValue,
    String? Function(String?)? validator,
    bool allowDecimal = true,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? helperText,
    String? prefixText,
    int? maxLength,
  }) async {
    final String? result = await show(
      context: context,
      title: title,
      labelText: labelText,
      hintText: hintText,
      initialValue: initialValue?.toString(),
      validator: validator,
      keyboardType: TextInputType.numberWithOptions(decimal: allowDecimal),
      inputFormatters: [
        if (allowDecimal)
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$'))
        else
          FilteringTextInputFormatter.digitsOnly,
      ],
      saveText: saveText,
      cancelText: cancelText,
      helperText: helperText,
      prefixText: prefixText,
      maxLength: maxLength,
    );

    if (result == null) {
      return null;
    }

    return double.tryParse(result);
  }

  /// Shows a custom form dialog with a provided widget as content.
  ///
  /// This is a specialized version of the input dialog for complex forms,
  /// allowing you to provide a custom widget as the content.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [widget]: The custom widget to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  /// - [icon]: An optional icon to display in the dialog.
  /// - [iconColor]: The color of the icon (if provided).
  static Future<void> showCustomForm({
    required BuildContext context,
    required String title,
    required Widget widget,
    bool barrierDismissible = true,
    IconData? icon,
    Color? iconColor,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Determine the icon color if not specified
    final Color effectiveIconColor =
        iconColor ?? (isDarkMode ? AppColors.primaryDark : AppColors.primary);

    // Create the content widget with the icon if provided
    Widget content = widget;

    if (icon != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 48,
            color: effectiveIconColor,
          ),
          const SizedBox(height: 16),
          content,
        ],
      );
    }

    return AppDialog.show(
      context: context,
      title: title,
      content: content,
      barrierDismissible: barrierDismissible,
      scrollable: true,
    );
  }
}
