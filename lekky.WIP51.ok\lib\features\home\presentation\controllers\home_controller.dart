// File: lib/features/home/<USER>/controllers/home_controller.dart
import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';
import '../../data/home_repository.dart';

/// Controller for the home screen
class HomeController extends ChangeNotifier {
  final HomeRepository _repository;

  // State variables
  double _meterTotal = 0.0;
  double _averageUsage = 0.0;
  double _shortTermAverageUsage = 0.0;
  DateTime? _dateToTopUp;
  String _meterUnit = '£';
  MeterEntry? _mostRecentMeterEntry;
  MeterEntry? _mostRecentTopUpEntry;
  double _topUpsSinceLastReading = 0.0;
  int _meterReadingCount = 0; // Track the number of meter readings
  bool _isLoading = true;
  String _error = '';

  // Event subscription
  StreamSubscription<EventType>? _eventSubscription;

  // Getters
  double get meterTotal => _meterTotal;
  double get averageUsage => _averageUsage;
  double get shortTermAverageUsage => _shortTermAverageUsage;
  DateTime? get dateToTopUp => _dateToTopUp;
  String get meterUnit => _meterUnit;
  MeterEntry? get mostRecentMeterEntry => _mostRecentMeterEntry;
  MeterEntry? get mostRecentTopUpEntry => _mostRecentTopUpEntry;
  double get topUpsSinceLastReading => _topUpsSinceLastReading;
  bool get isLoading => _isLoading;
  String get error => _error;

  // Formatted getters
  String get formattedMeterTotal {
    // If no meter readings yet, but we have top-ups
    if (_mostRecentMeterEntry == null && _mostRecentTopUpEntry != null) {
      // Use the most recent top-up entry
      double totalTopUps = _mostRecentTopUpEntry!.amountToppedUp;
      final formattedTopUps = totalTopUps.toStringAsFixed(2);
      return '$_meterUnit 0.00 + $_meterUnit $formattedTopUps';
    }

    // If no meter readings and no top-ups
    if (_mostRecentMeterEntry == null) {
      return '$_meterUnit ${_meterTotal.toStringAsFixed(2)}';
    }

    // Format as "£XX.XX + £YY.YY"
    final lastReading = _mostRecentMeterEntry!.reading;
    if (_topUpsSinceLastReading > 0) {
      final formattedReading = lastReading.toStringAsFixed(2);
      final formattedTopUps = _topUpsSinceLastReading.toStringAsFixed(2);
      return '$_meterUnit $formattedReading + $_meterUnit $formattedTopUps';
    } else {
      return '$_meterUnit ${lastReading.toStringAsFixed(2)}';
    }
  }

  String get formattedAverageUsage {
    // Show "No averages yet :(" when there's only one meter reading or no readings
    if (_averageUsage <= 0 || _meterReadingCount <= 1) {
      return 'No averages yet :(';
    }
    return '$_meterUnit${_averageUsage.toStringAsFixed(2)}/day';
  }

  String get formattedShortTermAverageUsage {
    // Show "No averages yet :(" when there's only one meter reading or no readings
    if (_shortTermAverageUsage <= 0 || _meterReadingCount <= 1) {
      return 'No averages yet :(';
    }
    return '$_meterUnit${_shortTermAverageUsage.toStringAsFixed(2)}/day';
  }

  String get formattedDateToTopUp => _dateToTopUp != null
      ? DateTimeUtils.formatDateRelative(_dateToTopUp!)
      : 'N/A';
  String get formattedLastReadingDate => _mostRecentMeterEntry != null
      ? DateTimeUtils.formatDateRelative(_mostRecentMeterEntry!.timestamp)
      : 'N/A';
  String get formattedLastTopUpDate => _mostRecentTopUpEntry != null
      ? DateTimeUtils.formatDateRelative(_mostRecentTopUpEntry!.timestamp)
      : 'N/A';
  String get formattedLastTopUpAmount => _mostRecentTopUpEntry != null
      ? '$_meterUnit${_mostRecentTopUpEntry!.amountToppedUp.toStringAsFixed(2)}'
      : 'N/A';

  HomeController(this._repository) {
    // Subscribe to data update events
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        logger.i('HomeController: Received data update event, refreshing data');
        refresh();
      }
    });
  }

  /// Initialize the controller
  Future<void> init() async {
    logger.i('HomeController: Initializing');
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      logger.i('HomeController: Initialized successfully');
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load data: $e';
      logger.e('HomeController: Failed to initialize', details: e.toString());
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Cancel event subscription
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Refresh the data
  Future<void> refresh() async {
    logger.i('HomeController: Refreshing data');
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      logger.i('HomeController: Data refreshed successfully');
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to refresh data: $e';
      logger.e('HomeController: Failed to refresh data', details: e.toString());
      notifyListeners();
    }
  }

  /// Load all data
  Future<void> _loadData() async {
    logger.d('HomeController: Loading data');
    try {
      // Load data in parallel
      final results = await Future.wait([
        _repository.getMeterTotal(),
        _repository.getAverageUsage(),
        _repository.getShortTermAverageUsage(),
        _repository.getMeterUnit(),
        _repository.getMostRecentMeterEntry(),
        _repository.getMostRecentTopUpEntry(),
        _repository
            .getAllEntries(), // Get all entries to calculate top-ups since last reading
        _repository.getMeterReadingCount(), // Get the count of meter readings
      ]);

      _meterTotal = results[0] as double;
      _averageUsage = results[1] as double;
      _shortTermAverageUsage = results[2] as double;
      _meterUnit = results[3] as String;
      _mostRecentMeterEntry = results[4] as MeterEntry?;
      _mostRecentTopUpEntry = results[5] as MeterEntry?;
      final allEntries = results[6] as List<MeterEntry>;
      _meterReadingCount = results[7] as int; // Set the meter reading count

      logger.d(
          'HomeController: Data loaded - Meter Total: $_meterTotal, Average Usage: $_averageUsage, Meter Reading Count: $_meterReadingCount');

      // Calculate top-ups since last reading
      _calculateTopUpsSinceLastReading(allEntries);

      // Calculate date to top up
      _dateToTopUp =
          await _repository.getDateToTopUp(_meterTotal, _averageUsage);
      logger.d(
          'HomeController: Date to top up calculated: ${_dateToTopUp?.toIso8601String() ?? 'N/A'}');
    } catch (e) {
      logger.e('HomeController: Error loading data', details: e.toString());
      rethrow; // Rethrow to be caught by the caller
    }
  }

  /// Calculate the total amount topped up since the last meter reading
  void _calculateTopUpsSinceLastReading(List<MeterEntry> entries) {
    _topUpsSinceLastReading = 0.0;

    if (_mostRecentMeterEntry == null || entries.isEmpty) {
      return;
    }

    // Get the date of the most recent meter reading
    final lastReadingDate = _mostRecentMeterEntry!.timestamp;

    // Find all top-ups that occurred after the last meter reading
    final topUpsAfterLastReading = entries
        .where((entry) =>
            entry.amountToppedUp > 0 &&
            entry.timestamp.isAfter(lastReadingDate))
        .toList();

    // Sum up all top-ups
    for (final topUp in topUpsAfterLastReading) {
      _topUpsSinceLastReading += topUp.amountToppedUp;
    }

    logger.d(
        'HomeController: Top-ups since last reading: $_topUpsSinceLastReading');
  }

  /// Calculate the cost of electric for a specific time period
  Future<double> calculateCost(DateTime startDate, DateTime endDate) async {
    logger.i(
        'HomeController: Calculating cost from ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');
    try {
      final cost = await _repository.calculateCost(startDate, endDate);
      logger.i('HomeController: Cost calculated: $cost');
      return cost;
    } catch (e) {
      logger.e('HomeController: Error calculating cost', details: e.toString());
      return 0.0;
    }
  }
}
