// File: lib/core/data/database/db_constants.dart
import '../../constants/db_constants.dart';

/// Database constants for the Lekky app
class DatabaseConstants {
  // Private constructor to prevent instantiation
  DatabaseConstants._();

  // SharedPreferences keys
  static const String keyMeterEntries = 'meter_entries';
  static const String keyNotifications = 'notifications';
  
  // Database name and version
  static const String databaseName = DBConstants.databaseName;
  static const int databaseVersion = DBConstants.databaseVersion;

  // Table names
  static const String tableMeterEntries = DBConstants.tableMeterEntries;
  static const String tableSettings = DBConstants.tableSettings;
  static const String tableNotifications = DBConstants.tableNotifications;

  // Column names for meter_entries table
  static const String columnId = DBConstants.columnId;
  static const String columnAmount = DBConstants.columnAmount;
  static const String columnTimestamp = DBConstants.columnTimestamp;
  static const String columnIsTopUp = DBConstants.columnIsTopUp;
  static const String columnNotes = DBConstants.columnNotes;

  // Column names for notifications table
  static const String columnNotificationId = DBConstants.columnNotificationId;
  static const String columnTitle = DBConstants.columnTitle;
  static const String columnMessage = DBConstants.columnMessage;
  static const String columnNotificationTimestamp = DBConstants.columnNotificationTimestamp;
  static const String columnIsRead = DBConstants.columnIsRead;
  static const String columnIsUrgent = DBConstants.columnIsUrgent;

  // SQL statements
  static const String createMeterEntriesTable = DBConstants.createMeterEntriesTable;
  static const String createNotificationsTable = DBConstants.createNotificationsTable;

  // Database optimization
  static const int optimizationThreshold = DBConstants.optimizationThreshold;
  static const int vacuumIntervalDays = DBConstants.vacuumIntervalDays;
}
