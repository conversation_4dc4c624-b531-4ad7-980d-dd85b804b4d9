{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-24:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33a23a1921cc9e261ad760b2275ca606\\transformed\\jetified-play-services-base-18.0.1\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,901,1062,1194,1311,1466,1601,1715,1965,2132,2245,2406,2539,2689,2846,2911,2983", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "896,1057,1189,1306,1461,1596,1710,1820,2127,2240,2401,2534,2684,2841,2906,2978,3065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\473c6549ebdad33ab68cbd325533c84f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1825", "endColumns": "139", "endOffsets": "1960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,3507", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,3603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3070,3180,3285,3398", "endColumns": "109,104,112,108", "endOffsets": "3175,3280,3393,3502"}}]}]}