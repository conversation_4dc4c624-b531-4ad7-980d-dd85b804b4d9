// File: lib/test_notification.dart
import 'package:flutter/material.dart';
import 'core/providers/notification_provider.dart';
import 'core/models/notification_model.dart';

/// A widget to test notifications
class TestNotificationWidget extends StatelessWidget {
  const TestNotificationWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Notifications'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                final provider = NotificationProvider();
                provider.showLowBalanceNotification(
                  meterUnit: '£',
                  balance: 5.0,
                  threshold: 10.0,
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Low Balance Notification Added'),
                  ),
                );
              },
              child: const Text('Add Low Balance Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final provider = NotificationProvider();
                provider.showTimeToTopUpNotification(
                  meterUnit: '£',
                  balance: 15.0,
                  daysRemaining: 7,
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Time to Top Up Notification Added'),
                  ),
                );
              },
              child: const Text('Add Time to Top Up Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final provider = NotificationProvider();
                provider.showInvalidRecordNotification(
                  message: 'Invalid meter reading detected. Please check your latest entry.',
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Invalid Record Notification Added'),
                  ),
                );
              },
              child: const Text('Add Invalid Record Notification'),
            ),
          ],
        ),
      ),
    );
  }
}
