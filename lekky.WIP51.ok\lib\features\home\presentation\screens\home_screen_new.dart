import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/widgets/ticker_tape.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../history/presentation/controllers/history_controller.dart';
import '../../../history/presentation/widgets/entry_edit_dialog.dart';
import '../controllers/home_controller.dart';
import '../widgets/meter_info_card.dart';
import '../widgets/meter_value_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Index for the current message to display
  int _currentMessageIndex = 0;

  // List of helpful messages to display in the ticker tape
  final List<String> _helpfulMessages = [
    'Tap + to add a new meter reading or top-up',
    'Swipe down to refresh your data',
    'View your usage history by tapping History in the menu',
    'Estimate your electricity costs with the Cost of Electric feature',
    'Set up notifications in Settings to be reminded when to top up',
  ];

  @override
  void initState() {
    super.initState();

    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeController>().init();
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % _helpfulMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<HomeController>(
        builder: (context, controller, _) {
          if (controller.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return RefreshIndicator(
            onRefresh: controller.refresh,
            child: Column(
              children: [
                // Fixed banner - exactly 96px high
                Container(
                  height: 96,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF0D47A1),
                        Color(0xFF1976D2)
                      ], // Lekky Meter banner blue
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Title text
                      const Positioned(
                        top: 20,
                        left: 20,
                        child: Text(
                          'Lekky',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 40,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // Notifications icon
                      Positioned(
                        top: 20,
                        right: 10,
                        child: IconButton(
                          icon: const Icon(
                            Icons.notifications,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            // TODO: Show notifications
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Message bar - directly below banner at y=96
                Container(
                  height: 32, // Appropriate height for single-line text
                  width: double.infinity,
                  color: Colors.grey[200],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                    child: Text(
                      _helpfulMessages[_currentMessageIndex],
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.black87,
                        height: 1.0,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                
                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MeterValueCard(
                            meterTotal: controller.formattedMeterTotal,
                            averageUsage: controller.formattedAverageUsage,
                            shortTermAverageUsage:
                                controller.formattedShortTermAverageUsage,
                            isLoading: controller.isLoading,
                          ),
                          const SizedBox(height: 16),
                          MeterInfoCard(
                            lastReadingDate: controller.formattedLastReadingDate,
                            dateToTopUp: controller.formattedDateToTopUp,
                            isLoading: controller.isLoading,
                          ),
                          const SizedBox(height: 24),
                          // Custom row layout to align buttons with card edges above
                          LayoutBuilder(
                            builder: (context, constraints) {
                              // Calculate button width based on card width
                              final buttonWidth = constraints.maxWidth * 0.42;
                              
                              return Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    // Cost of Electric button
                                    SizedBox(
                                      width: buttonWidth,
                                      child: GradientButton(
                                        text: 'Cost of\nElectric',
                                        icon: const Icon(
                                          Icons.attach_money,
                                          color: Colors.white,
                                        ),
                                        height: 80,
                                        gradientColors: const [
                                          Color(0xFFFF9800),
                                          Color(0xFFFFB74D)
                                        ],
                                        onPressed: () =>
                                            Navigator.of(context).pushNamed('/cost'),
                                        textStyle: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                          height: 1.2,
                                        ),
                                      ),
                                    ),
                                    // Add Entry button
                                    SizedBox(
                                      width: buttonWidth,
                                      child: GradientButton(
                                        text: 'Add\nEntry',
                                        icon: const Icon(
                                          Icons.add,
                                          color: Colors.white,
                                        ),
                                        height: 80,
                                        gradientColors: const [
                                          Color(0xFF43E97B),
                                          Color(0xFF38F9D7)
                                        ],
                                        onPressed: () => _showEntryEditDialog(null),
                                        textStyle: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                          height: 1.2,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          if (controller.error.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Text(
                                controller.error,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showEntryEditDialog(null),
        tooltip: 'Add Entry',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showEntryEditDialog(MeterEntry? entry) {
    final historyController = context.read<HistoryController>();
    EntryEditDialog.show(
      context: context,
      controller: historyController,
      entry: entry,
      onSave: (meterEntry) {
        historyController.addEntry(meterEntry);
      },
    );
  }
}
