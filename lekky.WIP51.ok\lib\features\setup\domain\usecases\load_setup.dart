// File: lib/features/setup/domain/usecases/load_setup.dart
import '../../../../core/constants/app_constants.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../models/setup_config.dart';

/// Use case for loading setup configuration
class LoadSetup {
  final SettingsRepository _repository;

  LoadSetup(this._repository);

  /// Execute the use case
  Future<SetupConfig> execute() async {
    // Load all settings at once
    final settings = await _repository.getMultipleSettings([
      AppConstants.keyMeterUnit,
      AppConstants.keyAlertThreshold,
      AppConstants.keyDaysInAdvance,
      AppConstants.keyDateFormat,
      AppConstants.keyDateInfo,
      AppConstants.keyNotificationsEnabled,
    ]);

    return SetupConfig(
      meterUnit: settings[AppConstants.keyMeterUnit] as String? ?? '£',
      alertThreshold: settings[AppConstants.keyAlertThreshold] as double? ?? 5.0,
      daysInAdvance: settings[AppConstants.keyDaysInAdvance] as int? ?? 2,
      dateFormat: settings[AppConstants.keyDateFormat] as String? ?? 'DD-MM-YYYY',
      dateInfo: settings[AppConstants.keyDateInfo] as String? ?? 'Date only',
      notificationsEnabled: settings[AppConstants.keyNotificationsEnabled] as bool? ?? true,
    );
  }

  /// Check if setup is completed
  Future<bool> isSetupCompleted() async {
    return await _repository.isSetupCompleted();
  }
}
