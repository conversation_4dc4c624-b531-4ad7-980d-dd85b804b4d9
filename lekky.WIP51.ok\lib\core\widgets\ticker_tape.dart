// File: lib/core/widgets/ticker_tape.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// A ticker tape style notification banner
class TickerTape extends StatelessWidget {
  final String message;
  final Color? backgroundColor;
  final Color? textColor;

  const TickerTape({
    Key? key,
    required this.message,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bgColor =
        backgroundColor ?? (isDarkMode ? Colors.grey[800]! : Colors.grey[200]!);
    final txtColor = textColor ?? (isDarkMode ? Colors.white : Colors.black87);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16), // Added vertical padding for taller height
      color: bgColor,
      child: Text(
        message,
        style: AppTextStyles.bodyMedium.copyWith(
          color: txtColor,
          height: 1.0, // Ensure single line height
          fontSize:
              16, // Increased font size for better readability in taller bar
        ),
        textAlign: TextAlign.center,
        maxLines: 1, // Limit to a single line
        overflow: TextOverflow.ellipsis, // Add ellipsis for overflow
      ),
    );
  }
}
