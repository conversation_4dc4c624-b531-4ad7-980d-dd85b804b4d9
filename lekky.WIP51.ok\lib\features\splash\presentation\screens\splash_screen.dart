// File: lib/features/splash/presentation/screens/splash_screen.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/theme/app_colors.dart';
import '../widgets/splash_animation.dart';

/// The splash screen of the app
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkSetupStatus();
  }

  Future<void> _checkSetupStatus() async {
    try {
      debugPrint('SplashScreen: Checking setup status...');
      // Simulate a delay for the splash screen
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) {
        debugPrint('SplashScreen: Widget not mounted, returning');
        return;
      }

      // First try to check directly from SharedPreferences
      bool isSetupCompleted = false;
      try {
        debugPrint('SplashScreen: Checking SharedPreferences directly');
        final prefs = await SharedPreferences.getInstance();
        isSetupCompleted =
            prefs.getBool(AppConstants.keySetupCompleted) ?? false;
        debugPrint(
            'SplashScreen: Direct SharedPreferences check: isSetupCompleted = $isSetupCompleted');
      } catch (e) {
        debugPrint(
            'SplashScreen: Error checking SharedPreferences directly: $e');
        // Fall back to repository method
      }

      // If direct check failed, use the repository
      if (!isSetupCompleted) {
        debugPrint('SplashScreen: Falling back to repository check');
        final settingsRepository = SettingsRepository();
        isSetupCompleted = await settingsRepository.isSetupCompleted();
        debugPrint(
            'SplashScreen: Repository check: isSetupCompleted = $isSetupCompleted');
      }

      if (!mounted) {
        debugPrint('SplashScreen: Widget not mounted after check, returning');
        return;
      }

      // Navigate to the appropriate screen
      if (isSetupCompleted) {
        debugPrint('SplashScreen: Navigating to Home');
        Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
      } else {
        debugPrint('SplashScreen: Navigating to Welcome');
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    } catch (e) {
      debugPrint('SplashScreen: Error checking setup status: $e');
      // Fallback to Welcome screen in case of error
      if (mounted) {
        debugPrint('SplashScreen: Navigating to Welcome due to error');
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SplashScreen: Building UI');
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.primaryGradient,
          ),
        ),
        child: Stack(
          children: [
            const Center(
              child: SplashAnimation(),
            ),
            // Debug overlay in debug mode only
            if (kDebugMode)
              Positioned(
                bottom: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Debug Build',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
