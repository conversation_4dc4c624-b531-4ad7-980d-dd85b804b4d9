// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_1_H
#define WINRT_Windows_UI_Xaml_Automation_1_H
#include "winrt/impl/Windows.UI.Xaml.Automation.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation
{
    struct __declspec(empty_bases) IAnnotationPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnnotationPatternIdentifiers>
    {
        IAnnotationPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IAnnotationPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnnotationPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnnotationPatternIdentifiersStatics>
    {
        IAnnotationPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IAnnotationPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationAnnotation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotation>
    {
        IAutomationAnnotation(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationAnnotationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotationFactory>
    {
        IAutomationAnnotationFactory(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationAnnotationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotationStatics>
    {
        IAutomationAnnotationStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiers>
    {
        IAutomationElementIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics>
    {
        IAutomationElementIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics2>
    {
        IAutomationElementIdentifiersStatics2(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics3>
    {
        IAutomationElementIdentifiersStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics4>
    {
        IAutomationElementIdentifiersStatics4(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics5>
    {
        IAutomationElementIdentifiersStatics5(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics6>
    {
        IAutomationElementIdentifiersStatics6(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics7>
    {
        IAutomationElementIdentifiersStatics7(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationElementIdentifiersStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics8>
    {
        IAutomationElementIdentifiersStatics8(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationProperties>
    {
        IAutomationProperties(std::nullptr_t = nullptr) noexcept {}
        IAutomationProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics>
    {
        IAutomationPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics2>
    {
        IAutomationPropertiesStatics2(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics3>
    {
        IAutomationPropertiesStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics4>
    {
        IAutomationPropertiesStatics4(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics5>
    {
        IAutomationPropertiesStatics5(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics6>
    {
        IAutomationPropertiesStatics6(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics7>
    {
        IAutomationPropertiesStatics7(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPropertiesStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics8>
    {
        IAutomationPropertiesStatics8(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationProperty :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationProperty>
    {
        IAutomationProperty(std::nullptr_t = nullptr) noexcept {}
        IAutomationProperty(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDockPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDockPatternIdentifiers>
    {
        IDockPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDockPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDockPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDockPatternIdentifiersStatics>
    {
        IDockPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDockPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragPatternIdentifiers>
    {
        IDragPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDragPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragPatternIdentifiersStatics>
    {
        IDragPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDragPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropTargetPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetPatternIdentifiers>
    {
        IDropTargetPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDropTargetPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropTargetPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetPatternIdentifiersStatics>
    {
        IDropTargetPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDropTargetPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExpandCollapsePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpandCollapsePatternIdentifiers>
    {
        IExpandCollapsePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IExpandCollapsePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExpandCollapsePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpandCollapsePatternIdentifiersStatics>
    {
        IExpandCollapsePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IExpandCollapsePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridItemPatternIdentifiers>
    {
        IGridItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IGridItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridItemPatternIdentifiersStatics>
    {
        IGridItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IGridItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridPatternIdentifiers>
    {
        IGridPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IGridPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridPatternIdentifiersStatics>
    {
        IGridPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IGridPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMultipleViewPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMultipleViewPatternIdentifiers>
    {
        IMultipleViewPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IMultipleViewPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMultipleViewPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMultipleViewPatternIdentifiersStatics>
    {
        IMultipleViewPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IMultipleViewPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeValuePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeValuePatternIdentifiers>
    {
        IRangeValuePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IRangeValuePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeValuePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeValuePatternIdentifiersStatics>
    {
        IRangeValuePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IRangeValuePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollPatternIdentifiers>
    {
        IScrollPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IScrollPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollPatternIdentifiersStatics>
    {
        IScrollPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IScrollPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionItemPatternIdentifiers>
    {
        ISelectionItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISelectionItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionItemPatternIdentifiersStatics>
    {
        ISelectionItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectionItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionPatternIdentifiers>
    {
        ISelectionPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISelectionPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionPatternIdentifiersStatics>
    {
        ISelectionPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectionPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpreadsheetItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetItemPatternIdentifiers>
    {
        ISpreadsheetItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpreadsheetItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetItemPatternIdentifiersStatics>
    {
        ISpreadsheetItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStylesPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStylesPatternIdentifiers>
    {
        IStylesPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IStylesPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStylesPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStylesPatternIdentifiersStatics>
    {
        IStylesPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IStylesPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITableItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableItemPatternIdentifiers>
    {
        ITableItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITableItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITableItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableItemPatternIdentifiersStatics>
    {
        ITableItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITableItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITablePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITablePatternIdentifiers>
    {
        ITablePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITablePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITablePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITablePatternIdentifiersStatics>
    {
        ITablePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITablePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITogglePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITogglePatternIdentifiers>
    {
        ITogglePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITogglePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITogglePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITogglePatternIdentifiersStatics>
    {
        ITogglePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITogglePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformPattern2Identifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPattern2Identifiers>
    {
        ITransformPattern2Identifiers(std::nullptr_t = nullptr) noexcept {}
        ITransformPattern2Identifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformPattern2IdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPattern2IdentifiersStatics>
    {
        ITransformPattern2IdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITransformPattern2IdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPatternIdentifiers>
    {
        ITransformPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITransformPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPatternIdentifiersStatics>
    {
        ITransformPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITransformPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IValuePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IValuePatternIdentifiers>
    {
        IValuePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IValuePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IValuePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IValuePatternIdentifiersStatics>
    {
        IValuePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IValuePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWindowPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowPatternIdentifiers>
    {
        IWindowPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IWindowPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWindowPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowPatternIdentifiersStatics>
    {
        IWindowPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
