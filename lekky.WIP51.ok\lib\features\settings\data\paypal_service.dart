// File: lib/features/settings/data/paypal_service.dart
import 'package:flutter/material.dart';
import 'package:flutter_paypal/flutter_paypal.dart';
import '../../../core/utils/logger.dart';

/// Service for handling PayPal donations
class PayPalService {
  /// PayPal Client ID
  static const String clientId = 'EJIOu0NyQ-v0gAvmYOshVBUCDQME_lVqjeEtF40mCkpqNCrnYWxXXgz8kMhbx61aPMNL2YiBWGHbHlFd';
  
  /// Display App Name
  static const String appName = 'Lekky';
  
  /// Whether to use sandbox mode
  static const bool sandboxMode = true;
  
  /// Return URL
  static const String returnURL = 'success.example.com';
  
  /// Cancel URL
  static const String cancelURL = 'cancel.example.com';
  
  /// Donation description
  static const String description = 'Support Lekky App';
  
  /// Available donation amounts
  static const List<String> donationAmounts = ['2.00', '5.00', '10.00', '20.00'];
  
  /// Process a donation
  static Future<bool> processDonation(BuildContext context, String amount, String currency) async {
    try {
      logger.i('PayPalService: Processing donation of $amount $currency');
      
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (BuildContext context) => UsePaypal(
            sandboxMode: sandboxMode,
            clientId: clientId,
            secretKey: '',
            returnURL: returnURL,
            cancelURL: cancelURL,
            transactions: [
              {
                'amount': {
                  'total': amount,
                  'currency': currency,
                },
                'description': description,
              }
            ],
            note: 'Thank you for your donation!',
            onSuccess: (Map params) {
              logger.i('PayPalService: Donation successful', details: params);
              _showThankYouDialog(context);
            },
            onError: (error) {
              logger.e('PayPalService: Donation error', details: error);
              _showErrorDialog(context, error.toString());
            },
            onCancel: (params) {
              logger.w('PayPalService: Donation cancelled', details: params);
            },
          ),
        ),
      );
      
      return true;
    } catch (e) {
      logger.e('PayPalService: Error processing donation', details: e.toString());
      _showErrorDialog(context, e.toString());
      return false;
    }
  }
  
  /// Show thank you dialog
  static void _showThankYouDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thank You!'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.favorite,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Thank you for your donation! Your support helps us continue to improve the app.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
  
  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Donation Error'),
        content: Text('There was an error processing your donation: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
