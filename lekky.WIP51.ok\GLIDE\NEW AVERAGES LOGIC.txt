NEW AVERAGES LOGIC

List<MeterEntryWithAverages> calculateAverages(List<MeterEntry> entries) {
  entries.sort((a, b) => a.date.compareTo(b.date));

  int totalEntryRows = entries.length;
  List<MeterEntryWithAverages> processedEntries = [];

  if (totalEntryRows == 0) {
    print("No Entries in History Table");
    return processedEntries;
  }

  double totalTopUpCount = 0;
  double lastMeterReadingValue = 0;
  DateTime? lastMeterReadingDate;
  double totalUsage = 0;
  DateTime? firstMeterReadingDate;

  for (int row = 0; row < totalEntryRows; row++) {
    MeterEntry currentEntry = entries[row];
    String? shortAverage;
    String? totalAverage;

    if (row == 0) {
      if (currentEntry.entryType == "MR") {
        lastMeterReadingValue = currentEntry.amount;
        lastMeterReadingDate = currentEntry.date;
        firstMeterReadingDate = currentEntry.date;
        shortAverage = "N/A";
        totalAverage = "N/A";
      } else {
        totalTopUpCount += currentEntry.amount;
        shortAverage = "N/A";
        totalAverage = "N/A";
      }
      processedEntries.add(MeterEntryWithAverages(
        date: currentEntry.date,
        entryType: currentEntry.entryType,
        amount: currentEntry.amount,
        shortAverage: shortAverage,
        totalAverage: totalAverage,
      ));
      continue;
    }

    MeterEntry lastEntry = entries[row - 1];
    String lastEntryType = lastEntry.entryType;

    if (lastEntryType == "MR") {
      lastMeterReadingValue = lastEntry.amount;
      lastMeterReadingDate = lastEntry.date;
      totalTopUpCount = 0;
    } else if (lastEntryType == "TU") {
      totalTopUpCount += lastEntry.amount;
    }

    String newEntryType = currentEntry.entryType;

    if (newEntryType == "TU") {
      shortAverage = "N/A";
      totalAverage = "N/A";
    } else if (newEntryType == "MR") {
      if (lastMeterReadingValue == 0 || lastMeterReadingDate == null) {
        shortAverage = "N/A";
        totalAverage = "N/A";
      } else {
        double newMeterReadingValue = currentEntry.amount;
        int daysBetweenReadings =
            dateOnly(currentEntry.date).difference(dateOnly(lastMeterReadingDate)).inDays;

        if (daysBetweenReadings == 0) daysBetweenReadings = 1;

        double usage = (lastMeterReadingValue + totalTopUpCount) - newMeterReadingValue;
        shortAverage = (usage / daysBetweenReadings).toStringAsFixed(5);

        totalUsage += usage;
        if (firstMeterReadingDate != null) {
          int totalDays =
              dateOnly(currentEntry.date).difference(dateOnly(firstMeterReadingDate)).inDays;
          if (totalDays == 0) totalDays = 1;
          totalAverage = (totalUsage / totalDays).toStringAsFixed(5);
          print(
              "Row ${row + 1}: Date=${formatDate(currentEntry.date)}, Usage=$usage, TotalUsage=$totalUsage, TotalDays=$totalDays, TotalAverage=$totalAverage");
        } else {
          totalAverage = "N/A";
        }

        lastMeterReadingValue = newMeterReadingValue;
        lastMeterReadingDate = currentEntry.date;
        if (firstMeterReadingDate == null) {
          firstMeterReadingDate = currentEntry.date;
        }
      }
    }

    processedEntries.add(MeterEntryWithAverages(
      date: currentEntry.date,
      entryType: currentEntry.entryType,
      amount: currentEntry.amount,
      shortAverage: shortAverage,
      totalAverage: totalAverage,
    ));
  }

  return processedEntries;
}