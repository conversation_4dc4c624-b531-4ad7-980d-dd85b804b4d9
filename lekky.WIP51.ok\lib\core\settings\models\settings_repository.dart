// File: lib/core/settings/models/settings_repository.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/constants/app_constants.dart';
import 'app_settings.dart';

/// A repository for managing app settings
class SettingsRepository {
  final SharedPreferences _prefs;

  SettingsRepository(this._prefs);

  /// Get the current app settings
  Future<AppSettings> getSettings() async {
    return AppSettings(
      meterUnit: _prefs.getString(AppConstants.keyMeterUnit) ?? '£',
      alertThreshold: _prefs.getDouble(AppConstants.keyAlertThreshold) ?? 5.0,
      daysInAdvance: _prefs.getInt(AppConstants.keyDaysInAdvance) ?? 2,
      dateFormat: _prefs.getString(AppConstants.keyDateFormat) ?? 'DD-MM-YYYY',
      dateInfo: _prefs.getString(AppConstants.keyDateInfo) ?? 'Date only',
      notificationsEnabled:
          _prefs.getBool(AppConstants.keyNotificationsEnabled) ?? true,
      initialMeterCredit: _prefs.getDouble(AppConstants.keyInitialCredit),
      themeMode: _getThemeMode(
          _prefs.getString(AppConstants.keyThemeMode) ?? 'system'),
    );
  }

  /// Save the app settings
  Future<bool> saveSettings(AppSettings settings) async {
    try {
      await _prefs.setString(AppConstants.keyMeterUnit, settings.meterUnit);
      await _prefs.setDouble(
          AppConstants.keyAlertThreshold, settings.alertThreshold);
      await _prefs.setInt(
          AppConstants.keyDaysInAdvance, settings.daysInAdvance);
      await _prefs.setString(AppConstants.keyDateFormat, settings.dateFormat);
      await _prefs.setString(AppConstants.keyDateInfo, settings.dateInfo);
      await _prefs.setBool(
          AppConstants.keyNotificationsEnabled, settings.notificationsEnabled);

      if (settings.initialMeterCredit != null) {
        await _prefs.setDouble(
            AppConstants.keyInitialCredit, settings.initialMeterCredit!);
      } else {
        await _prefs.remove(AppConstants.keyInitialCredit);
      }

      await _prefs.setString(
          AppConstants.keyThemeMode, _getThemeModeString(settings.themeMode));

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Convert a string to a ThemeMode
  ThemeMode _getThemeMode(String themeModeString) {
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      default:
        return ThemeMode.system;
    }
  }

  /// Convert a ThemeMode to a string
  String _getThemeModeString(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      default:
        return 'system';
    }
  }
}
