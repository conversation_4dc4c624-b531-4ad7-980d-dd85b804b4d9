// File: lib/core/shared_modules/appearance_selector.dart
import 'package:flutter/material.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class AppearanceSelector extends BaseSettingsWidget {
  final ThemeMode currentValue;
  final Function(ThemeMode) onChanged;
  final String? errorText;

  const AppearanceSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  // Get a user-friendly name for a theme mode
  String getThemeModeName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return 'System Default';
      case ThemeMode.light:
        return 'Light Mode';
      case ThemeMode.dark:
        return 'Dark Mode';
    }
  }

  // Get an icon for a theme mode
  IconData getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return Icons.settings_suggest;
      case ThemeMode.light:
        return Icons.wb_sunny;
      case ThemeMode.dark:
        return Icons.nightlight_round;
    }
  }

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Appearance'),

        if (showHelperText)
          buildHelperText(
              context, 'Choose the app theme that works best for you.'),

        const SizedBox(height: 16),

        // Theme mode options as cards
        Row(
          children: [
            for (final mode in ThemeMode.values)
              Expanded(
                child: GestureDetector(
                  onTap: () => onChanged(mode),
                  child: Card(
                    color: currentValue == mode
                        ? primaryColor.withOpacity(0.2)
                        : null,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: currentValue == mode
                            ? primaryColor
                            : Colors.grey.withOpacity(0.3),
                        width: currentValue == mode ? 2 : 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            getThemeModeIcon(mode),
                            size: 32,
                            color: currentValue == mode ? primaryColor : null,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            getThemeModeName(mode),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: currentValue == mode
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: currentValue == mode ? primaryColor : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 8),
        Text(
          'Tip: Dark mode can help reduce eye strain and save battery on OLED screens.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Appearance'),

        if (showTitle)
          Text(
            'Current: ${getThemeModeName(currentValue)}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (showHelperText)
          buildHelperText(context, 'Choose light or dark theme'),

        const SizedBox(height: 8),

        // Theme mode options as more compact radio buttons
        ...ThemeMode.values.map((mode) {
          return RadioListTile<ThemeMode>(
            title: Row(
              children: [
                Icon(getThemeModeIcon(mode), size: 16),
                const SizedBox(width: 8),
                Text(
                  getThemeModeName(mode),
                  style: const TextStyle(fontSize: 13),
                ),
              ],
            ),
            value: mode,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            activeColor: primaryColor,
          );
        }).toList(),

        buildErrorText(context, errorText),
      ],
    );
  }
}
