// File: lib/core/utils/event_bus.dart
import 'dart:async';

/// Event types for the application
enum EventType {
  /// Data has been updated (entry added, edited, or deleted)
  dataUpdated,
  
  /// Settings have been updated
  settingsUpdated,
}

/// A simple event bus for communication between different parts of the app
class EventBus {
  // Singleton pattern
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();

  // Stream controller for events
  final StreamController<EventType> _controller = StreamController<EventType>.broadcast();

  /// Get the stream of events
  Stream<EventType> get stream => _controller.stream;

  /// Fire an event
  void fire(EventType event) {
    _controller.add(event);
  }

  /// Dispose the event bus
  void dispose() {
    _controller.close();
  }
}
