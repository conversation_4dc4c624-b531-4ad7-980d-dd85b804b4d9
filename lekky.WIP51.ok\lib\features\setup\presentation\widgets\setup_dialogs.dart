// File: lib/features/setup/presentation/widgets/setup_dialogs.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_text_field.dart';
import '../../../../core/widgets/info_dialog.dart';
import '../../../../core/widgets/setting_dialog.dart';
import '../../../../core/utils/dialog_button_styles.dart';
import '../../../../core/settings/validators/settings_validator.dart';
import '../../../../core/settings/widgets/currency_selector.dart';
import '../../../../core/settings/widgets/date_format_selector.dart';
import '../../../../core/settings/widgets/date_info_selector.dart';
import '../../../../core/settings/widgets/alert_threshold_input.dart';
import '../../../../core/settings/widgets/days_advance_input.dart';
import '../../../../core/settings/widgets/notifications_toggle.dart';
import '../controllers/setup_controller.dart';
import 'initial_meter_credit_input.dart';

/// A utility class for showing setup dialogs
class SetupDialogs {
  /// Shows a dialog for selecting the currency
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;
          final FocusNode focusNode = FocusNode();

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.attach_money,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Currency',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: const Text(
                      'Select the currency for your meter readings',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  CurrencySelector(
                    currentValue: selectedValue,
                    onChanged: (value) {
                      setState(() {
                        selectedValue = value;
                      });
                    },
                    useDialog: false,
                    showCard: false,
                    focusNode: focusNode,
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onChanged(selectedValue);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: const Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting alert threshold and days in advance
  static Future<void> showAlertSettingsDialog(
    BuildContext context,
    double currentThreshold,
    int currentDaysInAdvance,
    String meterUnit,
    Function(double) onThresholdChanged,
    Function(int) onDaysInAdvanceChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          double threshold = currentThreshold;
          int daysInAdvance = currentDaysInAdvance;
          String? thresholdError;
          String? daysError;

          final thresholdController = TextEditingController(
            text: threshold.toString(),
          );

          final daysController = TextEditingController(
            text: daysInAdvance.toString(),
          );

          return SettingDialog(
            title: 'Alert Settings',
            subtitle:
                'Configure when you want to be notified about low meter balance',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Alert Threshold
                Text(
                  'Alert Threshold',
                  style: AppTextStyles.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(
                  'You will be notified when your meter balance falls below this amount',
                  style: AppTextStyles.bodySmall,
                ),
                const SizedBox(height: 8),
                AppTextField(
                  controller: thresholdController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
                  ],
                  prefixText: meterUnit,
                  errorText: thresholdError,
                  selectAllOnFocus: true,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateThreshold(value);
                    setState(() {
                      thresholdError = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                      if (validation['isValid']) {
                        threshold = double.tryParse(value) ?? threshold;
                      }
                    });
                  },
                ),
                const SizedBox(height: 4),
                Text(
                  'Recommended: ${meterUnit}5.00',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),

                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),

                // Days in Advance Alert
                Text(
                  'Days in Advance Alert',
                  style: AppTextStyles.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(
                  'How many days before you run out of credit should we notify you',
                  style: AppTextStyles.bodySmall,
                ),
                const SizedBox(height: 8),
                AppTextField(
                  controller: daysController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  errorText: daysError,
                  selectAllOnFocus: true,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateDaysInAdvance(value);
                    setState(() {
                      daysError = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                      if (validation['isValid']) {
                        daysInAdvance = int.tryParse(value) ?? daysInAdvance;
                      }
                    });
                  },
                ),
                const SizedBox(height: 4),
                Text(
                  'Recommended: 2 days',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              // Validate threshold
              final thresholdValidation =
                  SettingsValidator.validateThreshold(thresholdController.text);
              if (!thresholdValidation['isValid']) {
                setState(() {
                  thresholdError = thresholdValidation['errorMessage'];
                });
                return;
              }

              // Validate days in advance
              final daysValidation =
                  SettingsValidator.validateDaysInAdvance(daysController.text);
              if (!daysValidation['isValid']) {
                setState(() {
                  daysError = daysValidation['errorMessage'];
                });
                return;
              }

              // Save values
              onThresholdChanged(threshold);
              onDaysInAdvanceChanged(daysInAdvance);
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting the first meter reading
  static Future<void> showFirstMeterReadingDialog(
    BuildContext context,
    double? currentInitialCredit,
    String meterUnit,
    Function(double?) onInitialCreditChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          double? initialCredit = currentInitialCredit;
          String? errorText;

          final controller = TextEditingController(
            text: initialCredit?.toString() ?? '',
          );

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.speed,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'First Meter Reading',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: const Text(
                      'Enter your initial meter credit (optional)',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  AppTextField(
                    controller: controller,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
                    ],
                    prefixText: meterUnit,
                    errorText: errorText,
                    selectAllOnFocus: true,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        setState(() {
                          initialCredit = null;
                          errorText = null;
                        });
                        return;
                      }

                      final validation =
                          SettingsValidator.validateInitialCredit(value);
                      setState(() {
                        errorText = validation['isValid']
                            ? null
                            : validation['errorMessage'];
                        if (validation['isValid']) {
                          initialCredit = double.tryParse(value);
                        }
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Leave blank if you don\'t want to track your initial credit',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        // Validate if not empty
                        if (controller.text.isNotEmpty) {
                          final validation =
                              SettingsValidator.validateInitialCredit(
                                  controller.text);
                          if (!validation['isValid']) {
                            setState(() {
                              errorText = validation['errorMessage'];
                            });
                            return;
                          }
                        }

                        // Save value
                        onInitialCreditChanged(initialCredit);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: const Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting display settings (date format and date info)
  static Future<void> showDisplaySettingsDialog(
    BuildContext context,
    String currentDateFormat,
    String currentDateInfo,
    Function(String) onDateFormatChanged,
    Function(String) onDateInfoChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String dateFormat = currentDateFormat;
          String dateInfo = currentDateInfo;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.palette_outlined,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Display Settings',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: const Text(
                      'Configure how dates are displayed in the app',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  Flexible(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date Format
                          const Text(
                            'Date Format',
                            style: AppTextStyles.titleSmall,
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'Choose how dates are formatted',
                            style: AppTextStyles.bodySmall,
                          ),
                          const SizedBox(height: 8),
                          DateFormatSelector(
                            currentValue: dateFormat,
                            onChanged: (value) {
                              setState(() {
                                dateFormat = value;
                              });
                            },
                            useDialog: false,
                            showCard: false,
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Date Info
                          const Text(
                            'Date Information',
                            style: AppTextStyles.titleSmall,
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'Choose what date information to display',
                            style: AppTextStyles.bodySmall,
                          ),
                          const SizedBox(height: 8),
                          DateInfoSelector(
                            currentValue: dateInfo,
                            onChanged: (value) {
                              setState(() {
                                dateInfo = value;
                              });
                            },
                            useDialog: false,
                            showCard: false,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onDateFormatChanged(dateFormat);
                        onDateInfoChanged(dateInfo);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: const Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting notification preferences
  static Future<void> showNotificationsDialog(
    BuildContext context,
    bool currentNotificationsEnabled,
    Function(bool) onNotificationsEnabledChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          bool notificationsEnabled = currentNotificationsEnabled;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.notifications,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Notifications',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: const Text(
                      'Configure notification preferences',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  SwitchListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: Text(
                      notificationsEnabled
                          ? 'You will receive notifications about low meter balance'
                          : 'You will not receive any notifications',
                    ),
                    value: notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        notificationsEnabled = value;
                      });
                    },
                    dense: true,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Notifications will be sent when your meter balance falls below the alert threshold',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onNotificationsEnabledChanged(notificationsEnabled);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: const Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
