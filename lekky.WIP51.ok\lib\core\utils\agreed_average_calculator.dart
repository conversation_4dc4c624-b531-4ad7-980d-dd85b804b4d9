// File: lib/core/utils/agreed_average_calculator.dart
import '../models/meter_entry.dart';
import '../models/meter_entry_with_averages.dart';
import 'date_time_utils.dart';

/// Utility class for calculating electricity usage averages using the agreed logic
class AgreedAverageCalculator {
  /// Calculates averages for meter entries using the agreed logic
  static List<MeterEntryWithAverages> calculateAverages(
      List<MeterEntry> entries) {
    // Convert MeterEntry objects to the format expected by the new logic
    List<MeterEntryWithAverages> processedEntries = [];

    // Sort entries by timestamp
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    int totalEntryRows = entries.length;

    if (totalEntryRows == 0) {
      // No entries to process
      return processedEntries;
    }

    // Find all meter readings (not top-ups)
    final meterReadings = entries.where((e) => e.amountToppedUp == 0).toList();

    // If we have fewer than 2 meter readings, we can't calculate any averages
    if (meterReadings.length < 2) {
      // Just convert entries to MeterEntryWithAverages without averages
      for (final entry in entries) {
        processedEntries.add(MeterEntryWithAverages(
          date: entry.timestamp,
          entryType: entry.amountToppedUp > 0 ? "TU" : "MR",
          amount:
              entry.amountToppedUp > 0 ? entry.amountToppedUp : entry.reading,
          shortAverage: "N/A",
          totalAverage: "N/A",
        ));
      }
      return processedEntries;
    }

    // First, add all entries to processedEntries without averages
    for (final entry in entries) {
      processedEntries.add(MeterEntryWithAverages(
        date: entry.timestamp,
        entryType: entry.amountToppedUp > 0 ? "TU" : "MR",
        amount: entry.amountToppedUp > 0 ? entry.amountToppedUp : entry.reading,
        shortAverage: "N/A",
        totalAverage: "N/A",
      ));
    }

    // First meter reading gets zeros for both averages
    final firstMeterReadingIndex = processedEntries.indexWhere((e) =>
        e.date.isAtSameMomentAs(meterReadings[0].timestamp) &&
        e.entryType == "MR");

    if (firstMeterReadingIndex >= 0) {
      processedEntries[firstMeterReadingIndex] =
          processedEntries[firstMeterReadingIndex].copyWith(
        shortAverage: "0.00",
        totalAverage: "0.00",
      );
    }

    // ===== IMPLEMENT SHORT AVERAGES LOGIC =====
    // Initialize variables for the short averages algorithm
    double lastMeterReadingValue = 0.0;
    DateTime? lastMeterReadingDate;
    double totalTopUpCount = 0.0;

    // Process all entries in chronological order for short averages
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];

      // Skip the first entry - it will always have N/A for short average (except first meter reading which is 0.00)
      if (i == 0) {
        // If the first entry is a meter reading, set its values
        if (entry.amountToppedUp == 0) {
          lastMeterReadingValue = entry.reading;
          lastMeterReadingDate = entry.timestamp;
          totalTopUpCount = 0.0;
        }
        continue;
      }

      // Process based on the previous entry type
      final lastEntryType = entries[i - 1].amountToppedUp > 0 ? "TU" : "MR";

      if (lastEntryType == "MR") {
        lastMeterReadingValue = entries[i - 1].reading;
        lastMeterReadingDate = entries[i - 1].timestamp;
        totalTopUpCount = 0.0;
      } else if (lastEntryType == "TU") {
        totalTopUpCount += entries[i - 1].amountToppedUp;
      }

      // Process based on the current entry type
      final newEntryType = entry.amountToppedUp > 0 ? "TU" : "MR";

      // Find the index of this entry in the processedEntries list
      final processedEntryIndex = processedEntries.indexWhere((e) =>
          e.date.isAtSameMomentAs(entry.timestamp) &&
          e.entryType == newEntryType);

      if (processedEntryIndex < 0) continue; // Skip if not found

      // If it's a top-up, set short average to N/A
      if (newEntryType == "TU") {
        processedEntries[processedEntryIndex] =
            processedEntries[processedEntryIndex].copyWith(
          shortAverage: "N/A",
        );
        continue;
      }

      // If it's a meter reading but we don't have a previous reading, set to N/A
      if (newEntryType == "MR" &&
          (lastMeterReadingValue == 0 || lastMeterReadingDate == null)) {
        processedEntries[processedEntryIndex] =
            processedEntries[processedEntryIndex].copyWith(
          shortAverage: "N/A",
        );
        continue;
      }

      // Calculate short average for meter readings
      if (newEntryType == "MR") {
        final newMeterReadingValue = entry.reading;
        final daysBetweenReadings =
            entry.timestamp.difference(lastMeterReadingDate!).inDays;

        // If days is zero (same day readings), use "Cannot calculate average yet."
        if (daysBetweenReadings <= 0) {
          processedEntries[processedEntryIndex] =
              processedEntries[processedEntryIndex].copyWith(
            shortAverage: "Cannot calculate average yet.",
          );
          continue;
        }

        // Calculate usage: Previous Reading + Sum of Top-Ups - Current Reading
        final usage =
            lastMeterReadingValue + totalTopUpCount - newMeterReadingValue;

        // Calculate daily average (or 0.0 if usage is negative)
        final dailyAverage = usage > 0 ? usage / daysBetweenReadings : 0.0;

        // Update the short average for the current meter reading
        processedEntries[processedEntryIndex] =
            processedEntries[processedEntryIndex].copyWith(
          shortAverage: dailyAverage.toStringAsFixed(2),
        );
      }
    }

    // ===== IMPLEMENT TOTAL AVERAGES LOGIC =====
    // For total averages, we need the first meter reading and all top-ups
    final firstMeterReading = meterReadings[0];

    // Process all meter readings (except the first one) for total averages
    for (int i = 1; i < meterReadings.length; i++) {
      final currentReading = meterReadings[i];

      // Find all top-ups before this meter reading
      double allTopUpsBefore = 0.0;
      for (final entry in entries) {
        if (entry.amountToppedUp > 0 &&
            entry.timestamp.isBefore(currentReading.timestamp)) {
          allTopUpsBefore += entry.amountToppedUp;
        }
      }

      // Calculate days since first reading
      final daysSinceFirst = currentReading.timestamp
          .difference(firstMeterReading.timestamp)
          .inDays;

      // If days is zero (same day readings), use "Cannot calculate average yet."
      if (daysSinceFirst <= 0) {
        final processedEntryIndex = processedEntries.indexWhere((e) =>
            e.date.isAtSameMomentAs(currentReading.timestamp) &&
            e.entryType == "MR");

        if (processedEntryIndex >= 0) {
          processedEntries[processedEntryIndex] =
              processedEntries[processedEntryIndex].copyWith(
            totalAverage: "Cannot calculate average yet.",
          );
        }
        continue;
      }

      // Calculate total usage: (First Reading + All Top-Ups Before) - Current Reading
      final totalUsage = (firstMeterReading.reading + allTopUpsBefore) -
          currentReading.reading;

      // Calculate total average
      final totalAverage = totalUsage > 0 ? totalUsage / daysSinceFirst : 0.0;

      // Find the index of this meter reading in the processedEntries list
      final processedEntryIndex = processedEntries.indexWhere((e) =>
          e.date.isAtSameMomentAs(currentReading.timestamp) &&
          e.entryType == "MR");

      if (processedEntryIndex >= 0) {
        processedEntries[processedEntryIndex] =
            processedEntries[processedEntryIndex].copyWith(
          totalAverage: totalAverage.toStringAsFixed(2),
        );
      }
    }

    return processedEntries;
  }

  /// Convert processed entries back to MeterEntry objects
  static List<MeterEntry> convertToMeterEntries(
      List<MeterEntryWithAverages> processedEntries,
      List<MeterEntry> originalEntries) {
    if (processedEntries.isEmpty || originalEntries.isEmpty) {
      return originalEntries;
    }

    // Create a copy of the original entries
    List<MeterEntry> updatedEntries = List<MeterEntry>.from(originalEntries);

    // Create a map of processed entries by date and type for faster lookup
    final Map<String, MeterEntryWithAverages> processedEntriesByDateAndType =
        {};
    for (final entry in processedEntries) {
      // Use a more precise key that includes the time to handle multiple entries on the same day
      final dateKey = "${entry.date.toIso8601String()}_${entry.entryType}";
      processedEntriesByDateAndType[dateKey] = entry;
    }

    // Update each original entry with the calculated averages
    for (int i = 0; i < updatedEntries.length; i++) {
      final originalEntry = updatedEntries[i];
      final entryType = originalEntry.amountToppedUp > 0 ? "TU" : "MR";

      // Use a more precise key that includes the time
      final dateKey = "${originalEntry.timestamp.toIso8601String()}_$entryType";

      // Find the corresponding processed entry
      final processedEntry = processedEntriesByDateAndType[dateKey];

      if (processedEntry != null) {
        // Convert string averages to double or null
        double? shortAverage = processedEntry.shortAverage == "N/A"
            ? null
            : double.tryParse(processedEntry.shortAverage ?? "");

        double? totalAverage = processedEntry.totalAverage == "N/A"
            ? null
            : double.tryParse(processedEntry.totalAverage ?? "");

        // Update the entry
        updatedEntries[i] = originalEntry.copyWith(
          shortAverageAfterTopUp: shortAverage,
          totalAverageUpToThisPoint: totalAverage,
        );
      } else {
        // Try a fallback approach using just the date part (without time)
        final fallbackDateKey =
            "${DateTimeUtils.formatDateDefault(originalEntry.timestamp)}_$entryType";
        final fallbackEntry = processedEntries.firstWhere(
          (e) =>
              "${DateTimeUtils.formatDateDefault(e.date)}_${e.entryType}" ==
              fallbackDateKey,
          orElse: () => MeterEntryWithAverages(
            date: DateTime(1970), // Dummy date
            entryType: "",
            amount: 0,
            shortAverage: "N/A",
            totalAverage: "N/A",
          ),
        );

        if (fallbackEntry.entryType.isNotEmpty) {
          // Convert string averages to double or null
          double? shortAverage = fallbackEntry.shortAverage == "N/A"
              ? null
              : double.tryParse(fallbackEntry.shortAverage ?? "");

          double? totalAverage = fallbackEntry.totalAverage == "N/A"
              ? null
              : double.tryParse(fallbackEntry.totalAverage ?? "");

          // Update the entry
          updatedEntries[i] = originalEntry.copyWith(
            shortAverageAfterTopUp: shortAverage,
            totalAverageUpToThisPoint: totalAverage,
          );
        }
      }
    }

    return updatedEntries;
  }
}
