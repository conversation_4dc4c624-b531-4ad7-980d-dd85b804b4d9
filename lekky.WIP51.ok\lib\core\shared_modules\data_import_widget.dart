// File: lib/core/shared_modules/data_import_widget.dart
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../features/backup/backup_service.dart';
import '../models/meter_entry.dart';
import '../utils/result.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

/// Import mode determines how the file is selected
enum ImportMode {
  /// Automatically look for the backup file in the standard location
  automatic,

  /// Let the user select a file
  userSelected
}

class DataImportWidget extends BaseSettingsWidget {
  final Function(List<MeterEntry>) onImportSuccess;
  final Function(String) onImportError;
  final bool isImporting;
  final String? importResult;
  final String? errorText;
  final ImportMode importMode;
  final bool clearExistingData;
  final BackupService _backupService;

  DataImportWidget({
    Key? key,
    required this.onImportSuccess,
    required this.onImportError,
    this.isImporting = false,
    this.importResult,
    this.errorText,
    this.importMode = ImportMode.userSelected,
    this.clearExistingData = false,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
  })  : _backupService = BackupService(),
        super(
            key: key, displayMode: displayMode, showHelperText: showHelperText);

  /// Handle the import process based on the selected mode
  Future<void> _handleImport(BuildContext context) async {
    try {
      // Call the appropriate import method based on the mode
      if (importMode == ImportMode.automatic) {
        await _importFromBackupFile(context);
      } else {
        await _importFromUserSelectedFile(context);
      }
    } catch (e) {
      onImportError('Failed to import data: $e');
    }
  }

  /// Import data from the standard backup file location
  Future<void> _importFromBackupFile(BuildContext context) async {
    // Check if backup file exists
    if (!await _backupService.backupFileExists()) {
      onImportError('No backup file found');
      return;
    }

    // Get the backup file path
    final path = await _backupService.getBackupFilePath();
    if (path == null) {
      onImportError('Could not access backup file path');
      return;
    }

    final file = File(path);

    // Import entries using the backup service
    final result = await _backupService.importMeterEntries(file);

    result.fold(
      onSuccess: (entries) {
        onImportSuccess(entries);
      },
      onFailure: (error) {
        onImportError(error.message);
      },
    );
  }

  /// Import data from a user-selected file
  Future<void> _importFromUserSelectedFile(BuildContext context) async {
    // Pick a file
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: false,
      dialogTitle: 'Select CSV file',
      withData: true,
    );

    if (result == null || result.files.isEmpty) {
      onImportError('No file selected');
      return;
    }

    File csvFile;
    // Check if we have file bytes (for Android)
    if (result.files.first.bytes != null) {
      // Create a temporary file from bytes
      final tempDir = await getTemporaryDirectory();
      csvFile = File('${tempDir.path}/temp_import.csv');
      await csvFile.writeAsBytes(result.files.first.bytes!);
    }
    // Otherwise try to read from path (for iOS)
    else if (result.files.first.path != null) {
      csvFile = File(result.files.first.path!);
    } else {
      onImportError('Could not read file data');
      return;
    }

    // Import entries using the backup service
    final importResult = await _backupService.importMeterEntries(csvFile);

    importResult.fold(
      onSuccess: (entries) {
        onImportSuccess(entries);
      },
      onFailure: (error) {
        onImportError(error.message);
      },
    );
  }

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionTitle(context, 'Import Data'),

        buildHelperText(context,
            'Import your data from a backup file to restore your meter readings and settings.'),

        const SizedBox(height: 16),

        // Import button
        ElevatedButton.icon(
          onPressed: isImporting ? null : () => _handleImport(context),
          icon: isImporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.file_upload),
          label: Text(isImporting
              ? 'Importing...'
              : importMode == ImportMode.automatic
                  ? 'Restore from Backup'
                  : 'Select Backup File'),
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),

        if (importResult != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: importResult!.contains('success')
                  ? Colors.green.withOpacity(0.1)
                  : Colors.amber.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: importResult!.contains('success')
                    ? Colors.green
                    : Colors.amber,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  importResult!.contains('success')
                      ? Icons.check_circle
                      : Icons.info,
                  color: importResult!.contains('success')
                      ? Colors.green
                      : Colors.amber[800],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(importResult!),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),
        Text(
          clearExistingData
              ? 'Note: Importing data will replace your current data.'
              : 'Note: Importing data will add to your current data.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildLabel(context, 'Import Data'),

        const Text(
          'Import your data from a backup file',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),

        buildHelperText(context, 'Restore your meter readings and settings'),

        const SizedBox(height: 8),

        // Import button - more compact
        ElevatedButton.icon(
          onPressed: isImporting ? null : () => _handleImport(context),
          icon: isImporting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.file_upload, size: 16),
          label: Text(
            isImporting
                ? 'Importing...'
                : importMode == ImportMode.automatic
                    ? 'Restore'
                    : 'Select File',
            style: const TextStyle(fontSize: 13),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            minimumSize: const Size(120, 32),
          ),
        ),

        if (importResult != null) ...[
          const SizedBox(height: 8),
          Text(
            importResult!,
            style: TextStyle(
              fontSize: 12,
              color: importResult!.contains('success')
                  ? Colors.green
                  : Colors.amber[800],
            ),
          ),
        ],

        buildErrorText(context, errorText),
      ],
    );
  }
}
