// File: lib/features/home/<USER>/widgets/add_reading_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A dialog for adding a meter reading
class AddReadingDialog extends StatefulWidget {
  final String meterUnit;
  final double currentReading;

  const AddReadingDialog({
    super.key,
    required this.meterUnit,
    required this.currentReading,
  });

  /// Show the dialog
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    required String meterUnit,
    required double currentReading,
  }) async {
    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AddReadingDialog(
        meterUnit: meterUnit,
        currentReading: currentReading,
      ),
    );
  }

  @override
  State<AddReadingDialog> createState() => _AddReadingDialogState();
}

class _AddReadingDialogState extends State<AddReadingDialog> {
  final _formKey = GlobalKey<FormState>();
  final _readingController = TextEditingController();
  DateTime _selectedDate = DateTime.now();
  String? _errorMessage;

  @override
  void dispose() {
    _readingController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      final reading = double.parse(_readingController.text);
      
      // For prepaid meters, readings should decrease over time
      if (reading > widget.currentReading && widget.currentReading > 0) {
        setState(() {
          _errorMessage = 'New reading should be less than the current reading (${widget.currentReading})';
        });
        return;
      }

      Navigator.of(context).pop({
        'reading': reading,
        'date': _selectedDate,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Meter Reading'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: _readingController,
              labelText: 'Meter Reading',
              hintText: 'e.g. 25.5',
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              prefixText: widget.meterUnit,
              validator: (value) {
                final result = InputValidator.validateMeterReading(value ?? '');
                if (!result['isValid']) {
                  return result['errorMessage'];
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Date',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(DateTimeUtils.formatDateDefault(_selectedDate)),
                    const Icon(Icons.calendar_today),
                  ],
                ),
              ),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(
                  color: AppColors.error,
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
