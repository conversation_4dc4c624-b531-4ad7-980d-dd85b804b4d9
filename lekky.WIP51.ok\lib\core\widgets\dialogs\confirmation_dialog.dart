// File: lib/core/widgets/dialogs/confirmation_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/dialog_button_styles.dart';

/// A specialized dialog for confirming user actions, especially irreversible ones.
///
/// This dialog presents a clear title, concise message, and two buttons:
/// - Confirm (primary action)
/// - Cancel (secondary action)
class ConfirmationDialog {
  /// Shows a confirmation dialog with the specified title and message.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the action.
  /// - [message]: A concise message explaining the consequences of the action.
  /// - [confirmText]: The text for the confirm button (default: "Confirm").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [isDestructive]: Whether the action is destructive (default: false).
  ///   If true, the confirm button will be styled with error colors.
  /// - [icon]: An optional icon to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
    IconData? icon,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Create the content widget with an optional icon
    Widget content = Text(
      message,
      style: AppTextStyles.bodyMedium.copyWith(
        color: isDarkMode
            ? AppColors.onSurfaceDark.withOpacity(0.8)
            : AppColors.onSurface.withOpacity(0.8),
      ),
      textAlign: TextAlign.center,
    );

    if (icon != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 48,
            color: isDestructive
                ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
          ),
          const SizedBox(height: 16),
          content,
        ],
      );
    }

    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: AppTextStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          content: content,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actions: [
            // Cancel button with blue outline
            DialogButtonStyles.createCancelButton(
              context: context,
              onPressed: () => Navigator.of(context).pop(false),
              text: cancelText,
            ),

            // Confirm button
            isDestructive
                ? DialogButtonStyles.createDestructiveButton(
                    context: context,
                    onPressed: () => Navigator.of(context).pop(true),
                    text: confirmText,
                  )
                : DialogButtonStyles.createSaveButton(
                    context: context,
                    onPressed: () => Navigator.of(context).pop(true),
                    text: confirmText,
                  ),
          ],
        );
      },
    );
  }
}
