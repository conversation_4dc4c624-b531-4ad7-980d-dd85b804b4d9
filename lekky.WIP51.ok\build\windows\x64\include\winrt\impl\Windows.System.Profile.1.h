// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_System_Profile_1_H
#define WINRT_Windows_System_Profile_1_H
#include "winrt/impl/Windows.System.Profile.0.h"
WINRT_EXPORT namespace winrt::Windows::System::Profile
{
    struct __declspec(empty_bases) IAnalyticsInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsInfoStatics>
    {
        IAnalyticsInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnalyticsInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsInfoStatics2>
    {
        IAnalyticsInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnalyticsVersionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsVersionInfo>
    {
        IAnalyticsVersionInfo(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsVersionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppApplicabilityStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppApplicabilityStatics>
    {
        IAppApplicabilityStatics(std::nullptr_t = nullptr) noexcept {}
        IAppApplicabilityStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEducationSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEducationSettingsStatics>
    {
        IEducationSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IEducationSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHardwareIdentificationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHardwareIdentificationStatics>
    {
        IHardwareIdentificationStatics(std::nullptr_t = nullptr) noexcept {}
        IHardwareIdentificationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHardwareToken :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHardwareToken>
    {
        IHardwareToken(std::nullptr_t = nullptr) noexcept {}
        IHardwareToken(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlatformDiagnosticsAndUsageDataSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlatformDiagnosticsAndUsageDataSettingsStatics>
    {
        IPlatformDiagnosticsAndUsageDataSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IPlatformDiagnosticsAndUsageDataSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISharedModeSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISharedModeSettingsStatics>
    {
        ISharedModeSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        ISharedModeSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISharedModeSettingsStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISharedModeSettingsStatics2>
    {
        ISharedModeSettingsStatics2(std::nullptr_t = nullptr) noexcept {}
        ISharedModeSettingsStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemIdentificationInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemIdentificationInfo>
    {
        ISystemIdentificationInfo(std::nullptr_t = nullptr) noexcept {}
        ISystemIdentificationInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemIdentificationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemIdentificationStatics>
    {
        ISystemIdentificationStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemIdentificationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemSetupInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemSetupInfoStatics>
    {
        ISystemSetupInfoStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemSetupInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUnsupportedAppRequirement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnsupportedAppRequirement>
    {
        IUnsupportedAppRequirement(std::nullptr_t = nullptr) noexcept {}
        IUnsupportedAppRequirement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWindowsIntegrityPolicyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsIntegrityPolicyStatics>
    {
        IWindowsIntegrityPolicyStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowsIntegrityPolicyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
