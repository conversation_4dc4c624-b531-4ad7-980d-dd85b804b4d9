# Lekky App Notification System

## Overview

The notification system in the Lekky app serves as a critical component for alerting users about their electric meter balance status. It helps users proactively manage their electricity usage by providing timely alerts when their meter balance is approaching or has fallen below a predefined threshold. This document outlines the implementation details, configuration options, and best practices for the notification system.

## Technologies Used

- **flutter_local_notifications**: Primary package for handling local notifications
- **Android Notification Channels**: Used to categorize and prioritize notifications
- **BigTextStyleInformation**: Used to display expanded notification content
- **SharedPreferences**: Stores user-defined notification settings (thresholds, days in advance)

## Notification Types

### 1. Low Meter Balance Alerts

These notifications are triggered when the meter reading falls below the user-defined alert threshold.

#### Triggering Logic

```dart
Future<void> _scheduleLowBalanceAlert(double reading) async {
  final prefs = await SharedPreferences.getInstance();
  final alertThreshold = prefs.getDouble(AppConstants.keyAlertThreshold) ?? 5.0;
  final formattedThreshold = alertThreshold.toStringAsFixed(2);

  if (reading < alertThreshold) {
    // Create a big text style for expanded notifications
    final String notificationMessage =
        'Your meter reading of $_meterUnit${reading.toStringAsFixed(2)} is below the alert threshold of $_meterUnit$formattedThreshold!';

    final BigTextStyleInformation bigTextStyleInformation =
        BigTextStyleInformation(
      notificationMessage,
      htmlFormatBigText: false,
      contentTitle: 'Time to Top Up! ⚠️',
      htmlFormatContentTitle: false,
      summaryText: 'Lekky Alert',
      htmlFormatSummaryText: false,
    );
    
    // Create notification details
    final androidDetails = AndroidNotificationDetails(
      'lekky_channel',
      'Lekky Alerts',
      importance: Importance.max,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      color: AppColors.error, // Red color for urgency
      styleInformation: bigTextStyleInformation,
      autoCancel: true,
      fullScreenIntent: true,
      category: AndroidNotificationCategory.alarm,
    );
    
    final notificationDetails = NotificationDetails(android: androidDetails);

    // Show notification
    await FlutterLocalNotificationsPlugin().show(
      0,
      'Time to Top Up! ⚠️',
      notificationMessage,
      notificationDetails,
    );
  }
}
```

### 2. Predictive Balance Alerts

These notifications are triggered when the app predicts that the meter balance will fall below the threshold within the user-defined number of days, based on the average usage rate.

#### Triggering Logic

```dart
Future<void> _checkForAlerts() async {
  if (_daysInAdvance <= 0 || meterEntries.length < 2) return;

  final avgUsagePerDay = _calculateAverageUsage();
  if (avgUsagePerDay <= 0) return; // Avoid division by zero

  final daysUntilThreshold = (_meterTotal - _alertThreshold) / avgUsagePerDay;
  final formattedThreshold = _alertThreshold.toStringAsFixed(2);

  // If current balance is already below threshold
  if (_meterTotal < _alertThreshold) {
    await _showNotification(
      title: 'Time to Top Up Now! ⚠️',
      message:
          'Your current balance of $_meterUnit${_meterTotal.toStringAsFixed(2)} is already below the alert threshold of $_meterUnit$formattedThreshold!',
      urgent: true,
    );
  }
  // If balance will reach threshold within the days in advance setting
  else if (daysUntilThreshold <= _daysInAdvance && daysUntilThreshold > 0) {
    final daysText = daysUntilThreshold < 1
        ? 'less than a day'
        : daysUntilThreshold < 2
            ? 'about 1 day'
            : 'about ${daysUntilThreshold.round()} days';

    await _showNotification(
      title: 'Time to Top Up Soon! ⚠️',
      message:
          'Your meter will reach the alert threshold of $_meterUnit$formattedThreshold in $daysText at current usage rate.',
      urgent: false,
    );
  }
}
```

### 3. Error and Success Messages

While not traditional notifications, the app uses SnackbarUtil to display in-app messages for errors, successes, and information.

#### Implementation

```dart
// Show an error snackbar
static void showError(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 3),
  SnackBarAction? action,
}) {
  _showSnackbar(
    context,
    message: message,
    backgroundColor: AppColors.error,
    icon: Icons.error,
    duration: duration,
    action: action,
  );
}

// Show a success snackbar
static void showSuccess(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 3),
  SnackBarAction? action,
}) {
  _showSnackbar(
    context,
    message: message,
    backgroundColor: AppColors.success,
    icon: Icons.check_circle,
    duration: duration,
    action: action,
  );
}
```

## Notification Style and Formatting

### Android Notification Styling

The app uses `BigTextStyleInformation` to ensure that the full notification content is visible, even when the description extends onto more than one line:

```dart
final BigTextStyleInformation bigTextStyleInformation = BigTextStyleInformation(
  message,
  htmlFormatBigText: false,
  contentTitle: title,
  htmlFormatContentTitle: false,
  summaryText: 'Lekky Alert',
  htmlFormatSummaryText: false,
);
```

### Notification Appearance

- **Icons**: Uses the app's launcher icon for notifications
- **Colors**: 
  - Urgent alerts: Red (`AppColors.error`)
  - Warning alerts: Orange (`AppColors.tertiary`)
- **Sound and Vibration**: Enabled for all notifications to ensure user attention
- **Priority**: Set to high to ensure visibility
- **Category**: Uses `AndroidNotificationCategory.alarm` for higher visibility

### Platform Differences

- **Android**: Full implementation with expanded text, colors, and categories
- **iOS**: Basic implementation with standard notification appearance (future enhancement)

## Notification Channel Configuration

The app initializes a single notification channel for all alerts:

```dart
const AndroidInitializationSettings initializationSettingsAndroid =
    AndroidInitializationSettings('@mipmap/ic_launcher');
const InitializationSettings initializationSettings = InitializationSettings(
  android: initializationSettingsAndroid,
);
await flutterLocalNotificationsPlugin.initialize(initializationSettings);

await flutterLocalNotificationsPlugin
    .resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>()
    ?.requestNotificationsPermission();
```

## User Configuration Options

Users can customize notification behavior through the Settings screen:

1. **Alert Threshold**: The meter balance value that triggers alerts (default: 5.0)
2. **Days in Advance**: How many days before reaching the threshold to show predictive alerts (default: 2)

These settings are stored in SharedPreferences and accessed throughout the app:

```dart
// Reading settings
final prefs = await SharedPreferences.getInstance();
final alertThreshold = prefs.getDouble(AppConstants.keyAlertThreshold) ?? 5.0;
final daysInAdvance = prefs.getInt(AppConstants.keyDaysInAdvance) ?? 2;

// Updating settings
await prefs.setDouble(AppConstants.keyAlertThreshold, newValue);
await prefs.setInt(AppConstants.keyDaysInAdvance, newValue);
```

## Rules and Exceptions

- **Validation**: Both Alert Threshold and Days in Advance must be greater than 0
- **Minimum Data Requirements**: Predictive alerts require at least 2 meter entries to calculate average usage
- **Deduplication**: Different notification IDs (0 for urgent, 1 for warnings) prevent overwriting
- **Calculation Protection**: Checks for division by zero when calculating days until threshold

## User Experience Best Practices

### Notification Content

1. **Clear Titles**: Use action-oriented titles with emoji indicators (e.g., "Time to Top Up! ⚠️")
2. **Detailed Messages**: Include specific values (current balance, threshold) with proper formatting
3. **Time Indicators**: Use natural language for time periods ("less than a day", "about 2 days")
4. **Units**: Always include the currency/unit symbol with values

### Notification Timing

1. **Immediate Alerts**: Show alerts immediately when a new meter reading falls below threshold
2. **Predictive Alerts**: Calculate and show when the app predicts reaching the threshold
3. **App Resume**: Check for alerts when the app is opened or resumed

### Visual Hierarchy

1. **Urgent vs Warning**: Use different colors to distinguish between immediate needs and future warnings
2. **Expanded Content**: Ensure full message visibility with BigTextStyleInformation
3. **Auto-dismiss**: Allow notifications to be dismissed by tapping (autoCancel: true)

## Implementation Examples

### Showing a Standard Notification

```dart
await _showNotification(
  title: 'Time to Top Up Soon! ⚠️',
  message: 'Your meter will reach the alert threshold of $_meterUnit$formattedThreshold in $daysText at current usage rate.',
  urgent: false,
);
```

### Showing an Urgent Notification

```dart
await _showNotification(
  title: 'Time to Top Up Now! ⚠️',
  message: 'Your current balance of $_meterUnit${_meterTotal.toStringAsFixed(2)} is already below the alert threshold of $_meterUnit$formattedThreshold!',
  urgent: true,
);
```

### Displaying In-App Error Messages

```dart
SnackbarUtil.showError(
  context,
  message: 'Alert threshold must be greater than 0',
);
```

## Future Enhancements

1. **Notification History**: Store and display a history of past notifications
2. **Scheduled Notifications**: Allow users to schedule regular balance check reminders
3. **iOS-Specific Styling**: Enhance iOS notification appearance
4. **Notification Actions**: Add direct actions to notifications (e.g., "Add Top-Up")
5. **Notification Grouping**: Group multiple notifications when appropriate
6. **User Preferences**: Allow users to customize notification sounds and vibration patterns
