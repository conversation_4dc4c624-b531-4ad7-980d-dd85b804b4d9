// File: test/average_calculator_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/utils/average_calculator.dart';
import 'package:lekky/models/meter_entry.dart';

void main() {
  group('AverageCalculator Tests', () {
    // Test data
    final testEntries = [
      MeterEntry(
        id: 1,
        reading: 200.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 1),
      ),
      MeterEntry(
        id: 2,
        reading: 0.0,
        amountToppedUp: 50.0,
        timestamp: DateTime(2023, 1, 5),
      ),
      MeterEntry(
        id: 3,
        reading: 180.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 10),
      ),
      MeterEntry(
        id: 4,
        reading: 160.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 20),
      ),
      MeterEntry(
        id: 5,
        reading: 0.0,
        amountToppedUp: 100.0,
        timestamp: DateTime(2023, 1, 25),
      ),
      MeterEntry(
        id: 6,
        reading: 220.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 30),
      ),
    ];

    test('calculateShortAverage returns correct value for valid entries', () {
      // Test short average between entries 3 and 4
      final shortAvg = AverageCalculator.calculateShortAverage(testEntries, 3);

      // Expected: (180 - 160) / 10 days = 2.0 units per day
      expect(shortAvg, 2.0);
    });

    test('calculateShortAverage returns 0 for top-up entries', () {
      // Test short average for a top-up entry
      final shortAvg = AverageCalculator.calculateShortAverage(testEntries, 1);

      expect(shortAvg, 0.0);
    });

    test('calculateShortAverage returns 0 for first entry', () {
      // Test short average for the first entry
      final shortAvg = AverageCalculator.calculateShortAverage(testEntries, 0);

      expect(shortAvg, 0.0);
    });

    test('calculateShortAverage handles invalid indices', () {
      // Test with invalid index
      final shortAvg = AverageCalculator.calculateShortAverage(testEntries, -1);

      expect(shortAvg, 0.0);
    });

    test('calculateTotalAverage returns correct value for valid entries', () {
      // Test total average up to entry 4
      final totalAvg = AverageCalculator.calculateTotalAverage(testEntries, 3);

      // Expected: (200 - 160) / 19 days = 2.11 units per day
      expect(totalAvg, closeTo(2.11, 0.01));
    });

    test('calculateTotalAverage returns 0 for insufficient entries', () {
      // Create a list with only one entry
      final singleEntry = [testEntries.first];

      final totalAvg = AverageCalculator.calculateTotalAverage(singleEntry, 0);

      expect(totalAvg, 0.0);
    });

    test('calculateTotalAverage handles empty list', () {
      // Test with empty list
      final totalAvg = AverageCalculator.calculateTotalAverage([], 0);

      expect(totalAvg, 0.0);
    });

    test('calculateTotalAverage handles invalid indices', () {
      // Test with invalid index
      final totalAvg = AverageCalculator.calculateTotalAverage(testEntries, 10);

      expect(totalAvg, 0.0);
    });

    test('calculateTotalAverage correctly skips top-up entries', () {
      // Test total average up to entry 5 (which is a top-up)
      final totalAvg = AverageCalculator.calculateTotalAverage(testEntries, 4);

      // Should be the same as up to entry 4 since entry 5 is a top-up
      expect(totalAvg, closeTo(2.11, 0.01));
    });

    test('calculateTotalAverage handles entries after top-ups', () {
      // Test total average up to entry 6 (after a top-up)
      final totalAvg = AverageCalculator.calculateTotalAverage(testEntries, 5);

      // Expected: ((200 - 160) + (160 + 100 - 220)) / 29 days = 2.76 units per day
      expect(totalAvg, closeTo(2.76, 0.01));
    });
  });
}
