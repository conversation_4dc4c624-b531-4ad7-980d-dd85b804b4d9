// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Media_Imaging_1_H
#define WINRT_Windows_UI_Xaml_Media_Imaging_1_H
#include "winrt/impl/Windows.UI.Xaml.Media.Imaging.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media::Imaging
{
    struct __declspec(empty_bases) IBitmapImage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage>
    {
        IBitmapImage(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage2>
    {
        IBitmapImage2(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImage3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage3>
    {
        IBitmapImage3(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageFactory>
    {
        IBitmapImageFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics>
    {
        IBitmapImageStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImageStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics2>
    {
        IBitmapImageStatics2(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapImageStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics3>
    {
        IBitmapImageStatics3(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSource>
    {
        IBitmapSource(std::nullptr_t = nullptr) noexcept {}
        IBitmapSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSourceFactory>
    {
        IBitmapSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSourceStatics>
    {
        IBitmapSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDownloadProgressEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadProgressEventArgs>
    {
        IDownloadProgressEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDownloadProgressEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRenderTargetBitmap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderTargetBitmap>
    {
        IRenderTargetBitmap(std::nullptr_t = nullptr) noexcept {}
        IRenderTargetBitmap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRenderTargetBitmapStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderTargetBitmapStatics>
    {
        IRenderTargetBitmapStatics(std::nullptr_t = nullptr) noexcept {}
        IRenderTargetBitmapStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISoftwareBitmapSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISoftwareBitmapSource>
    {
        ISoftwareBitmapSource(std::nullptr_t = nullptr) noexcept {}
        ISoftwareBitmapSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISurfaceImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISurfaceImageSource>
    {
        ISurfaceImageSource(std::nullptr_t = nullptr) noexcept {}
        ISurfaceImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISurfaceImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISurfaceImageSourceFactory>
    {
        ISurfaceImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ISurfaceImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISvgImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSource>
    {
        ISvgImageSource(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISvgImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceFactory>
    {
        ISvgImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISvgImageSourceFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceFailedEventArgs>
    {
        ISvgImageSourceFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISvgImageSourceOpenedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceOpenedEventArgs>
    {
        ISvgImageSourceOpenedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceOpenedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISvgImageSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceStatics>
    {
        ISvgImageSourceStatics(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualSurfaceImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualSurfaceImageSource>
    {
        IVirtualSurfaceImageSource(std::nullptr_t = nullptr) noexcept {}
        IVirtualSurfaceImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualSurfaceImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualSurfaceImageSourceFactory>
    {
        IVirtualSurfaceImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IVirtualSurfaceImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWriteableBitmap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWriteableBitmap>
    {
        IWriteableBitmap(std::nullptr_t = nullptr) noexcept {}
        IWriteableBitmap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWriteableBitmapFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWriteableBitmapFactory>
    {
        IWriteableBitmapFactory(std::nullptr_t = nullptr) noexcept {}
        IWriteableBitmapFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlRenderingBackgroundTask :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTask>
    {
        IXamlRenderingBackgroundTask(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTask(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlRenderingBackgroundTaskFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTaskFactory>
    {
        IXamlRenderingBackgroundTaskFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTaskFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlRenderingBackgroundTaskOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTaskOverrides>
    {
        IXamlRenderingBackgroundTaskOverrides(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTaskOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
