# Lekky Key Design Elements

## 📂 Project Structure

*   `lib/`: Contains the core application code.
    *   `main.dart`: The entry point of the application.
    *   `app_scaffold.dart`: Defines the main app scaffold with bottom navigation.
    *   `core/`: Contains core functionalities and constants.
    *   `features/`: Contains feature-specific modules.
    *   `theme/`: Contains theme-related files such as colors and text styles.
    *   `providers/`: Contains providers for state management.

## 🧩 Core Modules & Responsibilities

*   `main.dart`: Initializes the app, sets up providers, configures theme, and defines routes.
*   `app_scaffold.dart`: Provides the main app layout with a bottom navigation bar, switching between different screens.
*   `features/home/<USER>
*   `features/history/`: Manages the history screen and related functionalities.
*   `features/cost/`: Manages the cost calculation screen and related functionalities.
*   `features/settings/`: Manages the settings screen and related functionalities.
*   `features/setup/`: Manages the initial setup screen and related functionalities.

## 🎨 Theme & Styles

*   Color palette:
    *   primary: 0xFF003087 (Deep blue)
    *   secondary: 0xFF43E97B (Green)
    *   tertiary: 0xFFFF9800 (Orange)
    *   error: 0xFFBA1A1A (Red)
    *   background: 0xFFF8F9FA (Light gray)
    *   surface: 0xFFFFFFFF (White)
*   Typography:
    *   fontFamily: Roboto
    *   displayLarge: fontSize: 57, fontWeight: w400
    *   displayMedium: fontSize: 45, fontWeight: w400
    *   displaySmall: fontSize: 36, fontWeight: w400
    *   headlineLarge: fontSize: 32, fontWeight: w700
    *   (and more...)

## 🔲 Reusable Components

*   AppBottomNavBar:
    *   Props: `currentIndex` (int), `onTap` (Function(int))
    *   Usage: Displays the bottom navigation bar with tabs for Home, History, Cost, and Settings.
*   AppCard:
    *   Props: `child` (Widget), `padding` (EdgeInsetsGeometry), `margin` (EdgeInsetsGeometry), `elevation` (double), `backgroundColor` (Color), `borderRadius` (BorderRadius), `hasShadow` (bool), `onTap` (VoidCallback), `isGradient` (bool), `gradientColors` (List<Color>), `gradientBegin` (Alignment), `gradientEnd` (Alignment)
    *   Usage: Provides a styled card container with customizable appearance and optional tap action.
*   AppTextField:
    *   Props: `controller` (TextEditingController), `labelText` (String), `hintText` (String), `errorText` (String), `obscureText` (bool), `keyboardType` (TextInputType), `textInputAction` (TextInputAction), `maxLines` (int), `onChanged` (ValueChanged<String>), `validator` (String? Function(String?)?) and many more.
    *   Usage: Offers a customizable text input field with labels, hints, validation, and styling options.

## ⚙️ State Management

*   Approach: Provider
*   Controllers/Providers:
    *   HomeController: Manages the state for the home screen.
    *   HistoryController: Manages the state for the history screen.
    *   CostController: Manages the state for the cost screen.
    *   SettingsController: Manages the state for the settings screen.
    *   SetupController: Manages the state for the setup screen.
    *   NotificationProvider: Manages the state for notifications.

## 🗄 Data & Storage

*   Models:
    *   MeterEntry: Represents a meter reading entry.
*   Persistence:
    *   SharedPreferences: Used to store app settings (e.g., theme mode, meter unit, alert threshold).
    *   Local Database (likely SQLite via DBHelper): Used to store meter entry data.

## 🔔 Notifications & Alerts

*   Types:
    *   Low balance alerts
    *   Time to top up alerts
    *   Invalid record alerts
    *   Meter reading reminders
*   Triggers:
    *   Low balance: When the meter balance falls below a certain threshold.
    *   Time to top up: When the meter balance is low and there are few days remaining.
    *   Invalid record: When an invalid meter reading record is detected.
    *   Meter reading reminder: Scheduled periodically to remind the user to submit a meter reading.

## 🔗 Navigation & Routing

*   Route names:
    *   `/home`: Home screen
    *   `/history`: History screen
    *   `/cost`: Cost screen
    *   `/settings`: Settings screen
    *   `/setup`: Setup screen
    *   `/welcome`: Welcome screen
*   Transitions: (Not explicitly defined, using default Flutter transitions)

## 📐 Layout Guidelines

*   Responsive Layout: Adapts to different screen sizes (mobile, tablet, desktop, large desktop) using `ResponsiveLayout` utility.
*   Breakpoints:
    *   Mobile: < 600
    *   Tablet: 600 - 900
    *   Desktop: 900 - 1200
    *   Large Desktop: >= 1200

## 🔒 Security & Permissions

*   Permissions:
    *   Requires permission for notifications.
*   Security Considerations:
    *   The app uses an ErrorHandler which defines Authentication and Permission error types, which may indicate that it is prepared to handle related security events.

## ✅ Testing Strategy

*   Unit Tests: For core logic and utility functions (e.g., `AverageCalculator`).
*   Widget Tests: For testing UI components and interactions.
*   Key coverage areas:
    *   Average calculation logic
    *   Widget rendering and interaction

## 🚀 Build & Deployment

*   Android:
    *   Uses Gradle build system.
    *   Version code and name are read from `local.properties`.
    *   Signing configuration is defined in `keystore.properties`.
    *   Release builds are configured with shrinking, minification, and ProGuard.
*   iOS:
    *   Uses Xcode build system.
    *   Metadata is defined in `Info.plist`.
    *   Build configurations: Debug, Release, Profile.
    *   Swift version: 5.0
    *   Deployment target: iOS 12.0
*   Web:
    *   Uses standard web technologies (HTML, CSS, JavaScript).
    *   `index.html`: Main HTML file.
    *   `manifest.json`: Web app manifest for PWA features.
*   Windows:
    *   Uses CMake build system.
    *   Version information and application icon are defined in `Runner.rc`.

## 🛠️ Error Handling & Logging

*   Centralized error handling using `ErrorHandler`.
*   Logging using `Logger` with support for console and file logging.
*   Error severity levels: low, medium, high.
*   Error types: network, database, validation, authentication, permission, unknown.
