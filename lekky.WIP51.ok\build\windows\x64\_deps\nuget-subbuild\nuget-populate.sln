﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{2E0FC14A-7E00-3D47-B7F9-00116F8B4DEF}"
	ProjectSection(ProjectDependencies) = postProject
		{B9E7D337-64C8-37A7-8847-6147875D61EA} = {B9E7D337-64C8-37A7-8847-6147875D61EA}
		{396ED338-265B-3705-B4C0-2BCE15FB5C89} = {396ED338-265B-3705-B4C0-2BCE15FB5C89}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{B9E7D337-64C8-37A7-8847-6147875D61EA}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "nuget-populate", "nuget-populate.vcxproj", "{396ED338-265B-3705-B4C0-2BCE15FB5C89}"
	ProjectSection(ProjectDependencies) = postProject
		{B9E7D337-64C8-37A7-8847-6147875D61EA} = {B9E7D337-64C8-37A7-8847-6147875D61EA}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2E0FC14A-7E00-3D47-B7F9-00116F8B4DEF}.Debug|x64.ActiveCfg = Debug|x64
		{2E0FC14A-7E00-3D47-B7F9-00116F8B4DEF}.Release|x64.ActiveCfg = Release|x64
		{2E0FC14A-7E00-3D47-B7F9-00116F8B4DEF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2E0FC14A-7E00-3D47-B7F9-00116F8B4DEF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.Debug|x64.ActiveCfg = Debug|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.Debug|x64.Build.0 = Debug|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.Release|x64.ActiveCfg = Release|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.Release|x64.Build.0 = Release|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B9E7D337-64C8-37A7-8847-6147875D61EA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.Debug|x64.ActiveCfg = Debug|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.Debug|x64.Build.0 = Debug|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.Release|x64.ActiveCfg = Release|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.Release|x64.Build.0 = Release|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{396ED338-265B-3705-B4C0-2BCE15FB5C89}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {656EE16E-A261-3D28-9777-DFD400C76CF8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
