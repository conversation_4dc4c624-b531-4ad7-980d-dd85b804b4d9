// File: lib/core/shared_modules/days_in_advance_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_text_field.dart';
import 'settings_model.dart';

class DaysInAdvanceSelector extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onChanged;
  final String? errorText;
  final bool hasTotalAverage;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  // Common day options
  static const List<int> dayOptions = [1, 2, 3, 5, 7, 14];

  const DaysInAdvanceSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    this.hasTotalAverage = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  @override
  State<DaysInAdvanceSelector> createState() => _DaysInAdvanceSelectorState();
}

class _DaysInAdvanceSelectorState extends State<DaysInAdvanceSelector> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toString(),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant DaysInAdvanceSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus && widget.currentValue != oldWidget.currentValue) {
      _controller.text = widget.currentValue.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.displayMode == SettingsDisplayMode.expanded
        ? _buildExpandedView(context)
        : _buildCompactView(context);
  }

  Widget _buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildSectionTitle(context, 'Days in Advance'),

        if (widget.showHelperText)
          _buildHelperText(context,
              'How many days in advance should we notify you about low balance?'),

        if (!widget.hasTotalAverage) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber[800], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.amber[800],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Days options as chips
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: DaysInAdvanceSelector.dayOptions.map((days) {
            return ChoiceChip(
              label: Text(days == 1 ? '1 day' : '$days days'),
              selected: widget.currentValue == days,
              selectedColor: primaryColor.withOpacity(0.2),
              labelStyle: TextStyle(
                color: widget.currentValue == days
                    ? primaryColor
                    : isDarkMode
                        ? Colors.white
                        : Colors.black,
                fontWeight: widget.currentValue == days
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
              onSelected: (selected) {
                if (selected) {
                  widget.onChanged(days);
                }
              },
            );
          }).toList(),
        ),

        const SizedBox(height: 16),

        // Custom input field
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          labelText: 'Custom Days',
          helperText: 'Enter a specific number of days',
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null && parsedValue > 0) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        const SizedBox(height: 8),
        Text(
          'Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }

  Widget _buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildLabel(context, 'Days in Advance'),

        if (widget.showTitle)
          Text(
            'Current: ${widget.currentValue == 1 ? "1 day" : "${widget.currentValue} days"}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (!widget.hasTotalAverage) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[800], size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Requires two meter readings',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.amber[800] : Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ],

        if (widget.showHelperText)
          _buildHelperText(
              context, 'Set how many days before you run out to be notified'),

        const SizedBox(height: 8),

        // Days options as chips - more compact
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: DaysInAdvanceSelector.dayOptions.map((days) {
            return ChoiceChip(
              label: Text('$days'),
              selected: widget.currentValue == days,
              selectedColor: primaryColor.withOpacity(0.2),
              labelStyle: TextStyle(
                color: widget.currentValue == days
                    ? primaryColor
                    : isDarkMode
                        ? Colors.white
                        : Colors.black,
                fontWeight: widget.currentValue == days
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
              onSelected: (selected) {
                if (selected) {
                  widget.onChanged(days);
                }
              },
            );
          }).toList(),
        ),

        const SizedBox(height: 8),

        // Custom input field - more compact
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          labelText: 'Custom',
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null && parsedValue > 0) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLabel(BuildContext context, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildHelperText(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }
}
