// File: lib/core/shared_modules/language_selector.dart
import 'package:flutter/material.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class LanguageSelector extends BaseSettingsWidget {
  final String currentValue;
  final Function(String) onChanged;
  final String? errorText;
  
  const LanguageSelector({
    super.key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    super.displayMode = SettingsDisplayMode.compact,
    super.showHelperText = false,
    super.showTitle = true,
  });
  
  // Language options
  static final List<Map<String, String>> languages = [
    {'code': 'en', 'name': 'English'},
    {'code': 'es', 'name': 'Spanish'},
    {'code': 'fr', 'name': 'French'},
    {'code': 'de', 'name': 'German'},
    {'code': 'it', 'name': 'Italian'},
    {'code': 'pt', 'name': 'Portuguese'},
    {'code': 'ru', 'name': 'Russian'},
    {'code': 'zh', 'name': 'Chinese'},
    {'code': 'ja', 'name': 'Japanese'},
  ];
  
  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Language'),
        
        if (showHelperText) buildHelperText(
          context, 
          'Select your preferred language for the app interface.'
        ),
        
        const SizedBox(height: 16),
        
        // Language options as radio buttons
        ...languages.map((language) {
          return RadioListTile<String>(
            title: Text(language['name']!),
            value: language['code']!,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            activeColor: primaryColor,
          );
        }),
        
        buildErrorText(context, errorText),
      ],
    );
  }
  
  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Language'),
        
        if (showTitle) Text(
          'Current: ${languages.firstWhere((l) => l['code'] == currentValue, orElse: () => {'name': 'English'})['name']}',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        
        if (showHelperText) buildHelperText(
          context, 
          'Select your preferred language'
        ),
        
        const SizedBox(height: 8),
        
        // Language options as more compact radio buttons
        ...languages.map((language) {
          return RadioListTile<String>(
            title: Text(language['name']!),
            value: language['code']!,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            activeColor: primaryColor,
          );
        }),
        
        buildErrorText(context, errorText),
      ],
    );
  }
}
