// File: lib/core/utils/responsive_layout.dart
import 'package:flutter/material.dart';

/// Utility class for responsive layouts
class ResponsiveLayout {
  // Private constructor to prevent instantiation
  ResponsiveLayout._();

  /// Screen size breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Returns true if the screen width is less than the mobile breakpoint
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Returns true if the screen width is between the mobile and tablet breakpoints
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  /// Returns true if the screen width is between the tablet and desktop breakpoints
  static bool isDesktop(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= tabletBreakpoint && width < desktopBreakpoint;
  }

  /// Returns true if the screen width is greater than or equal to the desktop breakpoint
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Returns the appropriate value based on the screen size
  static T getValueForScreenType<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    if (isLargeDesktop(context)) {
      return largeDesktop ?? desktop ?? tablet ?? mobile;
    }
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    }
    if (isTablet(context)) {
      return tablet ?? mobile;
    }
    return mobile;
  }

  /// Returns the appropriate padding based on the screen size
  static EdgeInsets getPaddingForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: const EdgeInsets.all(16),
      tablet: const EdgeInsets.all(24),
      desktop: const EdgeInsets.all(32),
      largeDesktop: const EdgeInsets.all(48),
    );
  }

  /// Returns the appropriate margin based on the screen size
  static EdgeInsets getMarginForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: const EdgeInsets.all(8),
      tablet: const EdgeInsets.all(16),
      desktop: const EdgeInsets.all(24),
      largeDesktop: const EdgeInsets.all(32),
    );
  }

  /// Returns the appropriate font size based on the screen size
  static double getFontSizeForScreenType(
    BuildContext context, {
    required double baseFontSize,
    double? tabletFontSizeMultiplier,
    double? desktopFontSizeMultiplier,
    double? largeDesktopFontSizeMultiplier,
  }) {
    return getValueForScreenType(
      context: context,
      mobile: baseFontSize,
      tablet: baseFontSize * (tabletFontSizeMultiplier ?? 1.1),
      desktop: baseFontSize * (desktopFontSizeMultiplier ?? 1.2),
      largeDesktop: baseFontSize * (largeDesktopFontSizeMultiplier ?? 1.3),
    );
  }

  /// Returns the appropriate icon size based on the screen size
  static double getIconSizeForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: 24,
      tablet: 28,
      desktop: 32,
      largeDesktop: 36,
    );
  }

  /// Returns the appropriate button height based on the screen size
  static double getButtonHeightForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: 48,
      tablet: 56,
      desktop: 64,
      largeDesktop: 72,
    );
  }

  /// Returns the appropriate card elevation based on the screen size
  static double getCardElevationForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: 2,
      tablet: 3,
      desktop: 4,
      largeDesktop: 5,
    );
  }

  /// Returns the appropriate border radius based on the screen size
  static double getBorderRadiusForScreenType(BuildContext context) {
    return getValueForScreenType(
      context: context,
      mobile: 8,
      tablet: 12,
      desktop: 16,
      largeDesktop: 20,
    );
  }
}
