// File: lib/core/shared_modules/base_settings_widget.dart
import 'package:flutter/material.dart';
import 'settings_model.dart';

/// Base class for all settings widgets
abstract class BaseSettingsWidget extends StatelessWidget {
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const BaseSettingsWidget({
    Key? key,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  /// Build the widget based on display mode
  @override
  Widget build(BuildContext context) {
    return displayMode == SettingsDisplayMode.expanded
        ? buildExpandedView(context)
        : buildCompactView(context);
  }

  /// Build the expanded view (for Setup page)
  Widget buildExpandedView(BuildContext context);

  /// Build the compact view (for Settings page)
  Widget buildCompactView(BuildContext context);

  /// Helper method to build a standard label
  Widget buildLabel(BuildContext context, String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: displayMode == SettingsDisplayMode.expanded ? 16 : 14,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Helper method to build a standard helper text
  Widget buildHelperText(BuildContext context, String text) {
    if (!showHelperText) return const SizedBox.shrink();

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: displayMode == SettingsDisplayMode.expanded ? 14 : 12,
          color: isDarkMode ? Colors.grey : Colors.black,
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }

  /// Helper method to build a standard error text
  Widget buildErrorText(BuildContext context, String? error) {
    if (error == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Text(
        error,
        style: TextStyle(
          fontSize: displayMode == SettingsDisplayMode.expanded ? 14 : 12,
          color: Theme.of(context).colorScheme.error,
        ),
      ),
    );
  }

  /// Helper method to build a standard section title
  Widget buildSectionTitle(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: displayMode == SettingsDisplayMode.expanded ? 18 : 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
}
