// File: lib/features/history/domain/models/related_validation_result.dart

import 'validation_result.dart';

/// Result of validating a meter entry with information about related invalid entries
class RelatedValidationResult extends ValidationResult {
  final int? relatedEntryId;
  final DateTime? relatedEntryDate;
  final double? relatedEntryReading;
  final bool isEarlierEntry; // Whether this entry is earlier than the related entry

  const RelatedValidationResult({
    required bool isValid,
    String? errorMessage,
    String severity = 'none',
    this.relatedEntryId,
    this.relatedEntryDate,
    this.relatedEntryReading,
    this.isEarlierEntry = false,
  }) : super(
          isValid: isValid,
          errorMessage: errorMessage,
          severity: severity,
        );

  /// Creates a valid result
  factory RelatedValidationResult.valid() {
    return const RelatedValidationResult(
      isValid: true,
      severity: 'none',
    );
  }

  /// Creates an error result for an earlier entry
  factory RelatedValidationResult.earlierEntryError({
    required String message,
    required int relatedEntryId,
    required DateTime relatedEntryDate,
    required double relatedEntryReading,
  }) {
    return RelatedValidationResult(
      isValid: false,
      errorMessage: message,
      severity: 'error',
      relatedEntryId: relatedEntryId,
      relatedEntryDate: relatedEntryDate,
      relatedEntryReading: relatedEntryReading,
      isEarlierEntry: true,
    );
  }

  /// Creates an error result for a later entry
  factory RelatedValidationResult.laterEntryError({
    required String message,
    required int relatedEntryId,
    required DateTime relatedEntryDate,
    required double relatedEntryReading,
  }) {
    return RelatedValidationResult(
      isValid: false,
      errorMessage: message,
      severity: 'error',
      relatedEntryId: relatedEntryId,
      relatedEntryDate: relatedEntryDate,
      relatedEntryReading: relatedEntryReading,
      isEarlierEntry: false,
    );
  }

  /// Creates a result from a map
  factory RelatedValidationResult.fromMap(Map<String, dynamic> map) {
    return RelatedValidationResult(
      isValid: map['isValid'] ?? false,
      errorMessage: map['errorMessage'],
      severity: map['severity'] ?? 'none',
      relatedEntryId: map['relatedEntryId'],
      relatedEntryDate: map['relatedEntryDate'] != null
          ? DateTime.parse(map['relatedEntryDate'])
          : null,
      relatedEntryReading: map['relatedEntryReading'],
      isEarlierEntry: map['isEarlierEntry'] ?? false,
    );
  }

  /// Converts this result to a map
  @override
  Map<String, dynamic> toMap() {
    return {
      'isValid': isValid,
      'errorMessage': errorMessage,
      'severity': severity,
      'relatedEntryId': relatedEntryId,
      'relatedEntryDate': relatedEntryDate?.toIso8601String(),
      'relatedEntryReading': relatedEntryReading,
      'isEarlierEntry': isEarlierEntry,
    };
  }

  /// Returns a formatted error message with the related entry information
  String getDetailedErrorMessage() {
    if (isValid || relatedEntryDate == null) {
      return errorMessage ?? '';
    }

    final formattedDate =
        '${relatedEntryDate!.day}/${relatedEntryDate!.month}/${relatedEntryDate!.year}';
    final formattedReading = relatedEntryReading?.toStringAsFixed(2) ?? '0.00';

    if (isEarlierEntry) {
      return 'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
    } else {
      return 'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
    }
  }

  /// Returns a short error message for add/edit dialogs
  String getShortErrorMessage() {
    if (isValid || relatedEntryReading == null) {
      return errorMessage ?? '';
    }

    final formattedReading = relatedEntryReading!.toStringAsFixed(2);
    
    if (isEarlierEntry) {
      return 'That is less than £$formattedReading. Check the date and amount.';
    } else {
      return 'That is more than £$formattedReading. Check the date and amount.';
    }
  }
}
