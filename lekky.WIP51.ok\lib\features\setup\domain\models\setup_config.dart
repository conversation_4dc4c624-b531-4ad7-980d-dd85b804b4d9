// File: lib/features/setup/domain/models/setup_config.dart

/// Configuration for the setup screen
class SetupConfig {
  final String meterUnit;
  final double alertThreshold;
  final int daysInAdvance;
  final String dateFormat;
  final String dateInfo;
  final bool notificationsEnabled;
  final double? initialMeterCredit;
  final String language;

  const SetupConfig({
    required this.meterUnit,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.dateFormat,
    required this.dateInfo,
    required this.notificationsEnabled,
    this.initialMeterCredit,
    this.language = 'en',
  });

  /// Creates a copy of this SetupConfig with the given fields replaced with the new values
  SetupConfig copyWith({
    String? meterUnit,
    double? alertThreshold,
    int? daysInAdvance,
    String? dateFormat,
    String? dateInfo,
    bool? notificationsEnabled,
    double? initialMeterCredit,
    String? language,
  }) {
    return SetupConfig(
      meterUnit: meterUnit ?? this.meterUnit,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      daysInAdvance: daysInAdvance ?? this.daysInAdvance,
      dateFormat: dateFormat ?? this.dateFormat,
      dateInfo: dateInfo ?? this.dateInfo,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      initialMeterCredit: initialMeterCredit ?? this.initialMeterCredit,
      language: language ?? this.language,
    );
  }

  /// Default configuration
  factory SetupConfig.defaultConfig() {
    return const SetupConfig(
      meterUnit: '£',
      alertThreshold: 5.0,
      daysInAdvance: 2,
      dateFormat: 'DD-MM-YYYY',
      dateInfo: 'Date only',
      notificationsEnabled: true,
      initialMeterCredit: null,
      language: 'en',
    );
  }

  /// Creates a SetupConfig from a map
  factory SetupConfig.fromMap(Map<String, dynamic> map) {
    return SetupConfig(
      meterUnit: map['meterUnit'] ?? '£',
      alertThreshold: map['alertThreshold']?.toDouble() ?? 5.0,
      daysInAdvance: map['daysInAdvance'] ?? 2,
      dateFormat: map['dateFormat'] ?? 'DD-MM-YYYY',
      dateInfo: map['dateInfo'] ?? 'Date only',
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      initialMeterCredit: map['initialMeterCredit']?.toDouble(),
      language: map['language'] ?? 'en',
    );
  }

  /// Converts this SetupConfig to a map
  Map<String, dynamic> toMap() {
    return {
      'meterUnit': meterUnit,
      'alertThreshold': alertThreshold,
      'daysInAdvance': daysInAdvance,
      'dateFormat': dateFormat,
      'dateInfo': dateInfo,
      'notificationsEnabled': notificationsEnabled,
      'initialMeterCredit': initialMeterCredit,
      'language': language,
    };
  }
}
