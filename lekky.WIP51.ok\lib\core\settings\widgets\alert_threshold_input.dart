// File: lib/core/settings/widgets/alert_threshold_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';
import '../../widgets/app_text_field.dart';
import '../../widgets/setting_dialog.dart';
import '../validators/settings_validator.dart';

/// A shared widget for setting the alert threshold
/// Can be used in both Setup and Settings screens
class AlertThresholdInput extends StatefulWidget {
  final double currentValue;
  final String meterUnit;
  final Function(double) onChanged;
  final bool useDialog;
  final bool showCard;

  const AlertThresholdInput({
    Key? key,
    required this.currentValue,
    required this.meterUnit,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  @override
  State<AlertThresholdInput> createState() => _AlertThresholdInputState();
}

class _AlertThresholdInputState extends State<AlertThresholdInput> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toStringAsFixed(2),
    );
  }

  @override
  void didUpdateWidget(AlertThresholdInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentValue != widget.currentValue) {
      _controller.text = widget.currentValue.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useDialog) {
      return ListTile(
        title: const Text('Alert Threshold'),
        subtitle: Text('${widget.currentValue} ${widget.meterUnit}'),
        leading: const Icon(Icons.warning),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showAlertThresholdDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Alert Threshold',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'You will be notified when your meter balance falls below this amount',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        AppTextField(
          controller: _controller,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
          ],
          prefixText: widget.meterUnit,
          errorText: _errorText,
          onChanged: _validateAndUpdate,
          autofocus: false, // Set to false to prevent stealing focus
        ),
        const SizedBox(height: 8),
        Text(
          'Recommended: ${widget.meterUnit}5.00',
          style: AppTextStyles.bodySmall.copyWith(
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );

    if (widget.showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  void _validateAndUpdate(String value) {
    final validation = SettingsValidator.validateThreshold(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (validation['isValid']) {
      final threshold = double.tryParse(value) ?? widget.currentValue;
      if (threshold >= 0) {
        widget.onChanged(threshold);
      }
    }
  }

  /// Show a dialog for setting the alert threshold
  void _showAlertThresholdDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String? errorText;
          final controller = TextEditingController(
            text: widget.currentValue.toStringAsFixed(2),
          );

          return SettingDialog(
            title: 'Alert Threshold',
            subtitle:
                'You will be notified when your meter balance falls below this amount',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextField(
                  controller: controller,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
                  ],
                  prefixText: widget.meterUnit,
                  errorText: errorText,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateThreshold(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                  autofocus: true,
                  selectAllOnFocus: true,
                ),
                const SizedBox(height: 8),
                Text(
                  'Recommended: ${widget.meterUnit}5.00',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateThreshold(controller.text);
              if (validation['isValid']) {
                final val = double.tryParse(controller.text);
                if (val != null && val >= 0.0) {
                  // Round to 2 decimal places
                  final roundedValue = double.parse(val.toStringAsFixed(2));
                  widget.onChanged(roundedValue);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }

  /// Static method to show an alert threshold dialog
  static Future<void> showAlertThresholdDialog(
    BuildContext context,
    double currentValue,
    String meterUnit,
    Function(double) onChanged,
  ) async {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String? errorText;
          final controller = TextEditingController(
            text: currentValue.toStringAsFixed(2),
          );

          return SettingDialog(
            title: 'Alert Threshold',
            subtitle:
                'You will be notified when your meter balance falls below this amount',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextField(
                  controller: controller,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
                  ],
                  prefixText: meterUnit,
                  errorText: errorText,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateThreshold(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                  autofocus: true,
                  selectAllOnFocus: true,
                ),
                const SizedBox(height: 8),
                Text(
                  'Recommended: ${meterUnit}5.00',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateThreshold(controller.text);
              if (validation['isValid']) {
                final val = double.tryParse(controller.text);
                if (val != null && val >= 0.0) {
                  // Round to 2 decimal places
                  final roundedValue = double.parse(val.toStringAsFixed(2));
                  onChanged(roundedValue);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }
}
