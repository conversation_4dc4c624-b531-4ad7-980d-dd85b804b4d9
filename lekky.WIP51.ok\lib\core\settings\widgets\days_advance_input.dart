// File: lib/core/settings/widgets/days_advance_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';
import '../../widgets/app_text_field.dart';
import '../../widgets/setting_dialog.dart';
import '../validators/settings_validator.dart';

/// A shared widget for setting the days in advance
/// Can be used in both Setup and Settings screens
class DaysAdvanceInput extends StatefulWidget {
  final int currentValue;
  final Function(int) onChanged;
  final bool useDialog;
  final bool showCard;

  const DaysAdvanceInput({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  @override
  State<DaysAdvanceInput> createState() => _DaysAdvanceInputState();
}

class _DaysAdvanceInputState extends State<DaysAdvanceInput> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toString(),
    );
  }

  @override
  void didUpdateWidget(DaysAdvanceInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentValue != widget.currentValue) {
      _controller.text = widget.currentValue.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useDialog) {
      return ListTile(
        title: const Text('Days in Advance'),
        subtitle: Text('${widget.currentValue} days'),
        leading: const Icon(Icons.calendar_today),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showDaysInAdvanceDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Days in Advance',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'How many days in advance would you like to be notified about topping up?',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        AppTextField(
          controller: _controller,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          errorText: _errorText,
          onChanged: _validateAndUpdate,
          autofocus: true,
        ),
        const SizedBox(height: 8),
        Text(
          'Recommended: 2 days',
          style: AppTextStyles.bodySmall.copyWith(
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );

    if (widget.showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  void _validateAndUpdate(String value) {
    final validation = SettingsValidator.validateDaysInAdvance(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (validation['isValid']) {
      final days = int.tryParse(value) ?? widget.currentValue;
      if (days > 0 && days <= 300) {
        widget.onChanged(days);
      }
    }
  }

  /// Show a dialog for setting the days in advance
  void _showDaysInAdvanceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String? errorText;
          final controller = TextEditingController(
            text: widget.currentValue.toString(),
          );

          return SettingDialog(
            title: 'Days in Advance',
            subtitle:
                'How many days in advance would you like to be notified about topping up?',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  errorText: errorText,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateDaysInAdvance(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                  autofocus: true,
                  selectAllOnFocus: true,
                ),
                const SizedBox(height: 8),
                Text(
                  'Recommended: 2 days',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateDaysInAdvance(controller.text);
              if (validation['isValid']) {
                final val = int.tryParse(controller.text);
                if (val != null && val > 0 && val <= 300) {
                  widget.onChanged(val);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }

  /// Static method to show a days in advance dialog
  static Future<void> showDaysInAdvanceDialog(
    BuildContext context,
    int currentValue,
    Function(int) onChanged,
  ) async {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String? errorText;
          final controller = TextEditingController(
            text: currentValue.toString(),
          );

          return SettingDialog(
            title: 'Days in Advance',
            subtitle:
                'How many days in advance would you like to be notified about topping up?',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  errorText: errorText,
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateDaysInAdvance(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                  autofocus: true,
                  selectAllOnFocus: true,
                ),
                const SizedBox(height: 8),
                Text(
                  'Recommended: 2 days',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateDaysInAdvance(controller.text);
              if (validation['isValid']) {
                final val = int.tryParse(controller.text);
                if (val != null && val > 0 && val <= 300) {
                  onChanged(val);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }
}
