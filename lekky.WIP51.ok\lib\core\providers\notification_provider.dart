// File: lib/core/providers/notification_provider.dart
import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import '../utils/logger.dart' show logger;

/// Provider for managing notifications in the app
class NotificationProvider extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;

  /// Get all notifications
  List<NotificationModel> get notifications =>
      List.unmodifiable(_notifications);

  /// Get unread notifications count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Check if there are any unread notifications
  bool get hasUnread => _notifications.any((n) => !n.isRead);

  /// Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _notificationService.initialize();
      await _loadNotifications();
      _isInitialized = true;
    } catch (e) {
      logger.e('Error initializing notification provider: $e');
    }
  }

  /// Load notifications from the service
  Future<void> _loadNotifications() async {
    try {
      _notifications = await _notificationService.getNotifications();
      notifyListeners();
    } catch (e) {
      logger.e('Error loading notifications: $e');
    }
  }

  /// Add a notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    try {
      await initialize();

      await _notificationService.addNotification(
        title: title,
        message: message,
        priority: priority,
        actionType: actionType,
      );

      await _loadNotifications();
    } catch (e) {
      logger.e('Error adding notification: $e');
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    try {
      await initialize();
      await _notificationService.markAsRead(id);
      await _loadNotifications();
    } catch (e) {
      logger.e('Error marking notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await initialize();
      await _notificationService.markAllAsRead();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error marking all notifications as read: $e');
    }
  }

  /// Remove a notification
  Future<void> removeNotification(String id) async {
    try {
      await initialize();
      await _notificationService.removeNotification(id);
      await _loadNotifications();
    } catch (e) {
      logger.e('Error removing notification: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    try {
      await initialize();
      await _notificationService.clearAll();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error clearing all notifications: $e');
    }
  }

  /// Refresh notifications
  Future<void> refresh() async {
    try {
      await _loadNotifications();
    } catch (e) {
      logger.e('Error refreshing notifications: $e');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      return await _notificationService.areNotificationsEnabled();
    } catch (e) {
      logger.e('Error checking if notifications are enabled: $e');
      return false;
    }
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    try {
      await _notificationService.setNotificationsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting notifications enabled: $e');
    }
  }

  /// Check if low balance alerts are enabled
  Future<bool> areLowBalanceAlertsEnabled() async {
    try {
      return await _notificationService.areLowBalanceAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if low balance alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable low balance alerts
  Future<void> setLowBalanceAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setLowBalanceAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting low balance alerts enabled: $e');
    }
  }

  /// Check if top-up alerts are enabled
  Future<bool> areTopUpAlertsEnabled() async {
    try {
      return await _notificationService.areTopUpAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if top-up alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable top-up alerts
  Future<void> setTopUpAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setTopUpAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting top-up alerts enabled: $e');
    }
  }

  /// Check if invalid record alerts are enabled
  Future<bool> areInvalidRecordAlertsEnabled() async {
    try {
      return await _notificationService.areInvalidRecordAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if invalid record alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable invalid record alerts
  Future<void> setInvalidRecordAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setInvalidRecordAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting invalid record alerts enabled: $e');
    }
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required String meterUnit,
    required double balance,
    required double threshold,
  }) async {
    try {
      await initialize();
      await _notificationService.showLowBalanceNotification(
        meterUnit: meterUnit,
        balance: balance,
        threshold: threshold,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing low balance notification: $e');
    }
  }

  /// Show a time to top up notification
  Future<void> showTimeToTopUpNotification({
    required String meterUnit,
    required double balance,
    required int daysRemaining,
  }) async {
    try {
      await initialize();
      await _notificationService.showTimeToTopUpNotification(
        meterUnit: meterUnit,
        balance: balance,
        daysRemaining: daysRemaining,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing time to top up notification: $e');
    }
  }

  /// Show an invalid record notification
  Future<void> showInvalidRecordNotification({
    required String message,
  }) async {
    try {
      await initialize();
      await _notificationService.showInvalidRecordNotification(
        message: message,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing invalid record notification: $e');
    }
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    try {
      return await _notificationService.areMeterReadingRemindersEnabled();
    } catch (e) {
      logger.e('Error checking if meter reading reminders are enabled: $e');
      return false;
    }
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    try {
      await _notificationService.setMeterReadingRemindersEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting meter reading reminders enabled: $e');
    }
  }

  /// Check if frequency options are enabled
  Future<bool> isFrequencyOptionsEnabled() async {
    try {
      return await _notificationService.isFrequencyOptionsEnabled();
    } catch (e) {
      logger.e('Error checking if frequency options are enabled: $e');
      return true; // Default to enabled
    }
  }

  /// Enable or disable frequency options
  Future<void> setFrequencyOptionsEnabled(bool enabled) async {
    try {
      await _notificationService.setFrequencyOptionsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting frequency options enabled: $e');
    }
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    try {
      return await _notificationService.getMeterReadingReminderFrequency();
    } catch (e) {
      logger.e('Error getting meter reading reminder frequency: $e');
      return 7; // Default to weekly
    }
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    try {
      await _notificationService.setMeterReadingReminderFrequency(days);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting meter reading reminder frequency: $e');
    }
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    try {
      return await _notificationService.getLastMeterReadingReminderDate();
    } catch (e) {
      logger.e('Error getting last meter reading reminder date: $e');
      return null;
    }
  }

  /// Schedule a meter reading reminder based on frequency
  Future<void> scheduleMeterReadingReminder() async {
    try {
      await initialize();
      final frequency = await getMeterReadingReminderFrequency();
      await _notificationService.scheduleMeterReadingReminder(frequency);
    } catch (e) {
      logger.e('Error scheduling meter reading reminder: $e');
    }
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    try {
      await initialize();
      await _notificationService.showMeterReadingReminderNotification();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing meter reading reminder notification: $e');
    }
  }
}
