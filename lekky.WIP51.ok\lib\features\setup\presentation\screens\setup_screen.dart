// File: lib/features/setup/presentation/screens/setup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/app_text_field.dart';
import '../../../../core/widgets/dialogs/confirmation_dialog.dart';
import '../../../../core/widgets/dialogs/progress_dialog.dart';
import '../../../../core/settings/validators/settings_validator.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/shared_modules/shared_modules.dart' as shared;
import '../../../settings/presentation/widgets/notification_settings.dart';
import '../controllers/setup_controller.dart';
import '../widgets/index.dart';

/// The setup screen of the app
class SetupScreen extends StatefulWidget {
  final bool isInitialSetup;

  const SetupScreen({
    super.key,
    this.isInitialSetup = true,
  });

  @override
  State<SetupScreen> createState() => _SetupScreenState();
}

class _SetupScreenState extends State<SetupScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SetupController>().init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<SetupController>(
      builder: (context, controller, _) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return CustomScrollView(
          slivers: [
            _buildAppBar(widget.isInitialSetup),
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  // Region Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.public,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Region Settings',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppColors.valueTextDark
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Configure your regional preferences',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: 16),

                          // Language Selector
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.LanguageSelector(
                                currentValue: controller.config.language,
                                onChanged: (value) {
                                  controller.updateLanguage(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Currency Selector
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.CurrencySelector(
                                currentValue: controller.config.meterUnit,
                                onChanged: (value) {
                                  controller.updateMeterUnit(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Select your currency symbol from the list',
                                style: AppTextStyles.bodySmall.copyWith(
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // First Meter Reading Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.speed,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'First Meter Reading',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppColors.valueTextDark
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Enter your initial meter credit (optional)',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: 16),

                          // Initial Meter Credit Input
                          StatefulBuilder(
                            builder: (context, setState) {
                              // Create a persistent controller that won't be recreated on each build
                              // Use a static or class-level variable to maintain the controller
                              final creditController = TextEditingController(
                                text: controller.config.initialMeterCredit
                                        ?.toString() ??
                                    '',
                              );
                              String? errorText;

                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AppTextField(
                                    controller: creditController,
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\d*\.?\d*$')),
                                    ],
                                    prefixText: controller.config.meterUnit,
                                    errorText: errorText,
                                    selectAllOnFocus: true,
                                    autofocus:
                                        false, // Don't autofocus, but select all when focused
                                    onChanged: (value) {
                                      if (value.isEmpty) {
                                        setState(() {
                                          errorText = null;
                                          controller
                                              .updateInitialMeterCredit(null);
                                        });
                                        return;
                                      }

                                      final validation = SettingsValidator
                                          .validateInitialCredit(value);
                                      setState(() {
                                        errorText = validation['isValid']
                                            ? null
                                            : validation['errorMessage'];
                                        if (validation['isValid']) {
                                          final initialCredit =
                                              double.tryParse(value);
                                          controller.updateInitialMeterCredit(
                                              initialCredit);
                                        }
                                      });
                                    },
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Leave blank if you don\'t want to track your initial credit',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Alert Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Alert Settings',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppColors.valueTextDark
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Configure when you want to be notified about low meter balance',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: 16),

                          // Alert Threshold
                          shared.AlertThresholdSelector(
                            currentValue: controller.config.alertThreshold,
                            onChanged: (value) {
                              controller.updateAlertThreshold(value);
                            },
                            currencySymbol: controller.config.meterUnit,
                            hasMeterReadings: false, // First-time setup
                            displayMode: shared.SettingsDisplayMode.expanded,
                            showHelperText: true,
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Days in Advance
                          shared.DaysInAdvanceSelector(
                            currentValue: controller.config.daysInAdvance,
                            onChanged: (value) {
                              controller.updateDaysInAdvance(value);
                            },
                            hasTotalAverage: false, // First-time setup
                            displayMode: shared.SettingsDisplayMode.expanded,
                            showHelperText: true,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Display Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.palette_outlined,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Display Settings',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppColors.valueTextDark
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Configure how dates are displayed in the app',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: 16),

                          // Date Format
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.DateFormatSelector(
                                currentValue: controller.config.dateFormat,
                                onChanged: (value) {
                                  controller.updateDateFormat(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Date Info
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.DateInfoSelector(
                                currentValue: controller.config.dateInfo,
                                onChanged: (value) {
                                  controller.updateDateInfo(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Appearance
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.AppearanceSelector(
                                currentValue: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? ThemeMode.dark
                                    : ThemeMode.light,
                                onChanged: (mode) {
                                  // Update theme mode
                                  final themeProvider =
                                      Provider.of<ThemeProvider>(context,
                                          listen: false);
                                  themeProvider.setThemeMode(mode);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Notifications Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.notifications,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Notifications',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppColors.valueTextDark
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Configure notification preferences',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: 16),

                          // Notifications Settings
                          const NotificationSettings(),
                        ],
                      ),
                    ),
                  ),

                  // Actions
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: SetupActions(
                      onSave: _saveSettings,
                      onReset: controller.resetToDefault,
                      onLoad: widget.isInitialSetup ? _showLoadDialog : null,
                      isSaving: controller.isSaving,
                      isInitialSetup: widget.isInitialSetup,
                    ),
                  ),

                  // Error message if any
                  if (controller.error.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Text(
                        controller.error,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ]),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAppBar(bool isInitialSetup) {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px to match Homepage and Settings
      pinned: true,
      automaticallyImplyLeading: false, // Never show back button
      // Use fixed height Stack instead of FlexibleSpaceBar
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.setupGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - matching Home screen style and position
            Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Home screen
              child: Text(
                isInitialSetup ? 'Setup' : 'Settings', // Banner text
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Matching Home screen font size
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1.0, 1.0),
                      blurRadius: 3.0,
                      color: Color.fromARGB(128, 0, 0, 0),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    final controller = context.read<SetupController>();
    final success = await controller.saveSetup();

    if (success && widget.isInitialSetup) {
      // Navigate to home screen if this is the initial setup
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }

  /// Shows a dialog to confirm loading meter data
  Future<void> _showLoadDialog() async {
    if (!mounted) return;

    final bool? confirmed = await ConfirmationDialog.show(
      context: context,
      title: 'Load Meter Data',
      message: 'This will load your previously saved meter data.',
      confirmText: 'Load',
      cancelText: 'Cancel',
      icon: Icons.cloud_download,
    );

    if (confirmed == true && mounted) {
      try {
        // Get the controller before the async operation
        final controller = Provider.of<SetupController>(context, listen: false);

        // Show loading indicator
        if (!mounted) return;

        await ProgressDialog.show(
          context: context,
          title: 'Loading Data',
          message: 'Please wait while we load your meter data...',
          barrierDismissible: false,
        );

        // Load the meter data
        final success = await controller.loadMeterData();

        // Check if the widget is still mounted
        if (!mounted) return;

        // Close loading dialog
        Navigator.of(context).pop();

        if (success) {
          // Show success message
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Meter data loaded successfully'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to home screen if this is the initial setup
          if (widget.isInitialSetup && mounted) {
            Navigator.of(context).pushReplacementNamed('/home');
          }
        } else {
          // Show error message
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to load meter data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        // Handle any errors
        if (!mounted) return;

        // Close loading dialog if it's still open
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading meter data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
