﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5566f7297e66283e2c722f908afc4a7c\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\34dd916e3179565bab3135e6ad25cbcd\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\69c1f89f5afa846445abe3c1ffff3ab7\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\000.Workspace\lekky\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{E5E4A590-E7EC-3BC7-A208-6020D5CB28B6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
