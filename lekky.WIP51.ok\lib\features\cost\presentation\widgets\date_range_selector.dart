// File: lib/features/cost/presentation/widgets/date_range_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../domain/models/date_range.dart';

/// A widget for selecting a date range for cost calculation
class DateRangeSelector extends StatelessWidget {
  /// The current date range
  final DateRange dateRange;

  /// Callback when the start date is changed
  final ValueChanged<DateTime?> onStartDateChanged;

  /// Callback when the end date is changed
  final ValueChanged<DateTime?> onEndDateChanged;

  /// The earliest allowed date
  final DateTime? earliestDate;

  /// The latest allowed date
  final DateTime? latestDate;

  /// Error message to display
  final String? errorMessage;

  const DateRangeSelector({
    Key? key,
    required this.dateRange,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    this.earliestDate,
    this.latestDate,
    this.errorMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date fields in a more compact layout
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                context,
                'From',
                dateRange.startDate,
                onStartDateChanged,
                true,
              ),
            ),
            const SizedBox(width: 12), // Reduced spacing
            Expanded(
              child: _buildDateField(
                context,
                'To',
                dateRange.endDate,
                onEndDateChanged,
                false,
              ),
            ),
          ],
        ),

        // Date range info and error messages in a more compact layout
        if ((dateRange.startDate != null &&
                dateRange.endDate != null &&
                dateRange.isValid) ||
            (errorMessage != null && errorMessage!.isNotEmpty))
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                // Show either info or error icon
                Icon(
                  errorMessage != null && errorMessage!.isNotEmpty
                      ? Icons.error_outline
                      : Icons.info_outline,
                  size: 16,
                  color: errorMessage != null && errorMessage!.isNotEmpty
                      ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                      : (isDarkMode ? Colors.blue[300] : Colors.blue[700]),
                ),
                const SizedBox(width: 8),

                // Show either range info or error message
                Expanded(
                  child: Text(
                    errorMessage != null && errorMessage!.isNotEmpty
                        ? errorMessage!
                        : 'Range: ${dateRange.days} days',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: errorMessage != null && errorMessage!.isNotEmpty
                          ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                          : (isDarkMode ? Colors.blue[300] : Colors.blue[700]),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildDateField(
    BuildContext context,
    String label,
    DateTime? date,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? AppColors.primaryDark : AppColors.primary;
    final labelColor = isDarkMode ? Colors.white70 : Colors.black87;
    final backgroundColor = isDarkMode ? AppColors.surfaceDark : Colors.white;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            fontWeight: FontWeight.w600,
            color: labelColor,
          ),
        ),
        const SizedBox(height: 4), // Reduced spacing

        // Date field with more compact design
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _selectDate(
              context,
              date,
              onDateChanged,
              isStartDate,
            ),
            borderRadius: BorderRadius.circular(6), // Smaller radius
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8, // Reduced padding
                vertical: 8, // Reduced padding
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                border: Border.all(
                  color: date != null
                      ? primaryColor.withOpacity(0.7)
                      : isDarkMode
                          ? AppColors.outlineDark
                          : AppColors.outline,
                  width: date != null ? 1.5 : 1.0,
                ),
                borderRadius: BorderRadius.circular(6), // Smaller radius
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Date text
                  Expanded(
                    child: Text(
                      date != null
                          ? DateTimeUtils.formatDateDefault(date)
                          : 'Select date',
                      style: AppTextStyles.bodySmall.copyWith(
                        // Smaller text
                        color: date != null
                            ? isDarkMode
                                ? Colors.white
                                : Colors.black87
                            : isDarkMode
                                ? Colors.white70
                                : Colors.black54,
                        fontWeight:
                            date != null ? FontWeight.w500 : FontWeight.normal,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Calendar icon
                  Icon(
                    Icons.calendar_today,
                    size: 16, // Smaller icon
                    color: date != null
                        ? primaryColor
                        : isDarkMode
                            ? Colors.white70
                            : Colors.black54,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentDate,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
  ) async {
    // Get today and yesterday for default values
    final today = DateTime.now();
    final yesterday = today.subtract(const Duration(days: 1));

    // Determine the valid date range for this field
    // For "From" date: Use earliestDate as the minimum, and one day before "To" date as the maximum
    // For "To" date: Use one day after "From" date as the minimum, and latestDate as the maximum
    DateTime firstDate;
    DateTime lastDate;

    if (isStartDate) {
      // "From" date constraints
      firstDate = earliestDate ?? DateTime(2000);
      lastDate = dateRange.endDate != null
          ? dateRange.endDate!.subtract(const Duration(days: 1))
          : (latestDate ?? today);
    } else {
      // "To" date constraints
      firstDate = dateRange.startDate != null
          ? dateRange.startDate!.add(const Duration(days: 1))
          : (earliestDate != null
              ? earliestDate!.add(const Duration(days: 1))
              : DateTime(2000).add(const Duration(days: 1)));
      lastDate = latestDate ?? today.add(const Duration(days: 365));
    }

    // Set default initial date if none provided
    DateTime initialDate;
    if (currentDate != null) {
      initialDate = currentDate;
    } else {
      initialDate = isStartDate ? yesterday : today;
    }

    // Ensure the initialDate is within the valid range
    if (initialDate.isBefore(firstDate)) {
      initialDate = firstDate;
    } else if (initialDate.isAfter(lastDate)) {
      initialDate = lastDate;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      // No need for a selectableDayPredicate since we're already setting firstDate and lastDate
      selectableDayPredicate: null,
      builder: (context, child) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: isDarkMode
                ? const ColorScheme.dark(
                    primary: AppColors.primaryDark,
                    onPrimary: AppColors.onPrimaryDark,
                    surface: AppColors.surfaceDark,
                    onSurface: AppColors.onSurfaceDark,
                    secondary: AppColors.secondaryDark,
                    background: AppColors.backgroundDark,
                  )
                : const ColorScheme.light(
                    primary: AppColors.primary,
                    onPrimary: Colors.white,
                    surface: AppColors.surface,
                    onSurface: AppColors.onSurface,
                    secondary: AppColors.secondary,
                    background: AppColors.background,
                  ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    isDarkMode ? AppColors.primaryDark : AppColors.primary,
              ),
            ),
            dialogBackgroundColor:
                isDarkMode ? AppColors.surfaceDark : AppColors.surface,
            dialogTheme: DialogTheme(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              elevation: 4,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      onDateChanged(picked);
    }
  }
}
