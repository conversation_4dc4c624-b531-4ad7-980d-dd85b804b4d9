// File: theme/colors.dart
import 'package:flutter/material.dart';

/// App color palette
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Light Theme Colors
  static const Color primary = Color(0xFF003087); // Deep blue
  static const Color secondary = Color(0xFF43E97B); // Green
  static const Color tertiary = Color(0xFFFF9800); // Orange
  static const Color error = Color(0xFFBA1A1A); // Red
  static const Color background = Color(0xFFF8F9FA); // Light gray
  static const Color surface = Color(0xFFFFFFFF); // White
  static const Color surfaceVariant = Color(0xFFE7E0EC);
  static const Color onPrimary = Color(0xFFFFFFFF); // White
  static const Color onSecondary = Color(0xFF000000); // Black
  static const Color onTertiary = Color(0xFF000000); // Black
  static const Color onError = Color(0xFFFFFFFF); // White
  static const Color onBackground = Color(0xFF1C1B1F); // Dark gray
  static const Color onSurface = Color(0xFF1C1B1F); // Dark gray
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color outline = Color(0xFF79747E);

  // Tab Colors
  static const Color homeTab = Color(0xFF49D941); // Green
  static const Color meterTab = Color(0xFF36D1DC); // Blue (same as settings)
  static const Color topUpTab = Color(0xFFFF9800); // Orange
  static const Color historyTab =
      Color(0xFFC5A8E7); // Lighter Purple (from #a484d1)
  static const Color settingsTab =
      Color(0xFFD8DEDB); // Lighter Grey (from #bcc4c1)

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF003087),
    Color(0xFF0057B8)
  ];
  static const List<Color> secondaryGradient = [
    Color(0xFF43E97B),
    Color(0xFF38F9D7)
  ];
  static const List<Color> tertiaryGradient = [
    Color(0xFFFF9800),
    Color(0xFFFFB74D)
  ];
  static const List<Color> meterGradient = [
    Color(0xFF36D1DC),
    Color(0xFF5B86E5)
  ]; // Same as settingsGradient
  static const List<Color> topUpGradient = [
    Color(0xFFFF9800),
    Color(0xFFFFB74D)
  ];
  static const List<Color> historyGradient = [
    Color(0xFFC5A8E7), // Lighter Purple
    Color(0xFFD4BFF0) // Even lighter Purple
  ];
  static const List<Color> settingsGradient = [
    Color(0xFFD8DEDB), // Lighter Grey
    Color(0xFFE5E9E7) // Even lighter Grey
  ];

  // Dark Theme Colors
  static const Color primaryDark = Color(0xFF4D82D6); // Lighter blue
  static const Color secondaryDark = Color(0xFF43E97B); // Green
  static const Color tertiaryDark = Color(0xFFFFB74D); // Lighter orange
  static const Color errorDark = Color(0xFFFFB4AB); // Light red
  static const Color backgroundDark = Color(0xFF1C1B1F); // Dark gray
  static const Color surfaceDark = Color(0xFF2D2D2D); // Dark surface
  static const Color surfaceVariantDark = Color(0xFF49454F);
  static const Color onPrimaryDark = Color(0xFF000000); // Black
  static const Color onSecondaryDark = Color(0xFF000000); // Black
  static const Color onTertiaryDark = Color(0xFF000000); // Black
  static const Color onErrorDark = Color(0xFF000000); // Black
  static const Color onBackgroundDark = Color(0xFFE6E1E5); // Light gray
  static const Color onSurfaceDark = Color(0xFFE6E1E5); // Light gray
  static const Color onSurfaceVariantDark = Color(0xFFCAC4D0);
  static const Color outlineDark = Color(0xFF938F99);

  // Adaptive Text Colors for Dark Mode
  static const Color lastReadingLabelDark =
      Color(0xFF90CAF9); // Light blue for better contrast
  static const Color meterTotalLabelDark =
      Color(0xFFFFD54F); // Light amber for better contrast
  static const Color valueTextDark =
      Color(0xFFE1F5FE); // Very light blue for values
  static const Color primaryTextDark =
      Color(0xFF81D4FA); // Light blue for primary text

  // Semantic Colors
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color warning = Color(0xFFFFC107); // Amber
  static const Color info = Color(0xFF2196F3); // Blue

  // Transparent Colors
  static Color semiTransparentWhite = Colors.white.withOpacity(0.7);
  static Color semiTransparentBlack = Colors.black.withOpacity(0.7);

  // Card Background Colors
  static Color cardBackground = Colors.white.withOpacity(0.85);
  static Color cardBackgroundDark = const Color(0xFF2D2D2D).withOpacity(0.85);

  // Container Colors
  static const Color surfaceContainer = Color(0xFFEAE0EC);
  static const Color surfaceContainerDark = Color(0xFF3A3A3A);
}
