// File: lib/features/cost/presentation/widgets/date_calendar_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../domain/models/date_range.dart';

/// A widget for selecting a date range using calendar views
class DateCalendarSelector extends StatefulWidget {
  /// The current date range
  final DateRange dateRange;

  /// Callback when the start date is changed
  final ValueChanged<DateTime?> onStartDateChanged;

  /// Callback when the end date is changed
  final ValueChanged<DateTime?> onEndDateChanged;

  /// The earliest allowed date
  final DateTime? earliestDate;

  /// The latest allowed date
  final DateTime? latestDate;

  /// Error message to display
  final String? errorMessage;

  const DateCalendarSelector({
    Key? key,
    required this.dateRange,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    this.earliestDate,
    this.latestDate,
    this.errorMessage,
  }) : super(key: key);

  @override
  State<DateCalendarSelector> createState() => _DateCalendarSelectorState();
}

class _DateCalendarSelectorState extends State<DateCalendarSelector> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab bar for switching between From and To calendars
        TabBar(
          controller: _tabController,
          labelColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          unselectedLabelColor: isDarkMode ? Colors.white70 : Colors.black54,
          indicatorColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          tabs: const [
            Tab(text: 'From Date'),
            Tab(text: 'To Date'),
          ],
        ),
        
        // Calendar views
        SizedBox(
          height: 300, // Fixed height for the calendar
          child: TabBarView(
            controller: _tabController,
            children: [
              // From date calendar
              _buildCalendar(
                context,
                widget.dateRange.startDate,
                widget.onStartDateChanged,
                true,
              ),
              
              // To date calendar
              _buildCalendar(
                context,
                widget.dateRange.endDate,
                widget.onEndDateChanged,
                false,
              ),
            ],
          ),
        ),
        
        // Date range info and error messages
        if ((widget.dateRange.startDate != null &&
                widget.dateRange.endDate != null &&
                widget.dateRange.isValid) ||
            (widget.errorMessage != null && widget.errorMessage!.isNotEmpty))
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                // Show either info or error icon
                Icon(
                  widget.errorMessage != null && widget.errorMessage!.isNotEmpty
                      ? Icons.error_outline
                      : Icons.info_outline,
                  size: 16,
                  color: widget.errorMessage != null && widget.errorMessage!.isNotEmpty
                      ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                      : (isDarkMode ? Colors.blue[300] : Colors.blue[700]),
                ),
                const SizedBox(width: 8),
                
                // Show either range info or error message
                Expanded(
                  child: Text(
                    widget.errorMessage != null && widget.errorMessage!.isNotEmpty
                        ? widget.errorMessage!
                        : 'Range: ${widget.dateRange.days} days',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: widget.errorMessage != null && widget.errorMessage!.isNotEmpty
                          ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                          : (isDarkMode ? Colors.blue[300] : Colors.blue[700]),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCalendar(
    BuildContext context,
    DateTime? selectedDate,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Determine the valid date range for this field
    DateTime firstDate;
    DateTime lastDate;

    if (isStartDate) {
      // "From" date constraints
      firstDate = widget.earliestDate ?? DateTime(2000);
      lastDate = widget.dateRange.endDate != null
          ? widget.dateRange.endDate!.subtract(const Duration(days: 1))
          : (widget.latestDate ?? DateTime.now());
    } else {
      // "To" date constraints
      firstDate = widget.dateRange.startDate != null
          ? widget.dateRange.startDate!.add(const Duration(days: 1))
          : (widget.earliestDate != null
              ? widget.earliestDate!.add(const Duration(days: 1))
              : DateTime(2000).add(const Duration(days: 1)));
      lastDate = widget.latestDate ?? DateTime.now().add(const Duration(days: 365));
    }
    
    // Create a simple calendar using a GridView
    // This is a simplified version since we're not using the table_calendar package
    return CalendarDatePicker(
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: firstDate,
      lastDate: lastDate,
      onDateChanged: onDateChanged,
      currentDate: DateTime.now(),
    );
  }
}
