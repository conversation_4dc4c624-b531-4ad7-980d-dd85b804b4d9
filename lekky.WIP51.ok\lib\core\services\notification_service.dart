// File: lib/core/services/notification_service.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification_model.dart';
import '../utils/notification_helper.dart';
import '../utils/logger.dart' show logger;

/// Service class for managing notifications
class NotificationService {
  // Singleton instance
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Key for storing notifications in SharedPreferences
  static const String _notificationsKey = 'lekky_notifications';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _lowBalanceAlertsEnabledKey =
      'low_balance_alerts_enabled';
  static const String _topUpAlertsEnabledKey = 'top_up_alerts_enabled';
  static const String _invalidRecordAlertsEnabledKey =
      'invalid_record_alerts_enabled';
  static const String _meterReadingReminderEnabledKey =
      'meter_reading_reminder_enabled';
  static const String _meterReadingReminderFrequencyKey =
      'meter_reading_reminder_frequency';
  static const String _meterReadingReminderFrequencyEnabledKey =
      'meter_reading_reminder_frequency_enabled';
  static const String _meterReadingReminderLastDateKey =
      'meter_reading_reminder_last_date';

  // Notification IDs
  static const int lowBalanceNotificationId = 1;
  static const int topUpReminderNotificationId = 2;
  static const int invalidRecordNotificationId = 3;
  static const int meterReadingReminderNotificationId = 4;

  // In-memory cache of notifications
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;
  final NotificationHelper _notificationHelper = NotificationHelper();

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadNotifications();
    await NotificationHelper.initialize();
    _isInitialized = true;
  }

  /// Load notifications from SharedPreferences
  Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      _notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Sort notifications by timestamp (newest first)
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      logger.e('Error loading notifications: $e');
      _notifications = [];
    }
  }

  /// Save notifications to SharedPreferences
  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = _notifications.map((notification) {
        return jsonEncode(notification.toMap());
      }).toList();

      await prefs.setStringList(_notificationsKey, notificationsJson);
    } catch (e) {
      logger.e('Error saving notifications: $e');
    }
  }

  /// Add a new notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    await initialize();

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      timestamp: DateTime.now(),
      priority: priority,
      actionType: actionType,
    );

    _notifications.insert(0, notification); // Add to the beginning of the list
    await _saveNotifications();
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    await initialize();

    final index =
        _notifications.indexWhere((notification) => notification.id == id);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _saveNotifications();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await initialize();

    _notifications = _notifications
        .map((notification) => notification.copyWith(isRead: true))
        .toList();

    await _saveNotifications();
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    await initialize();

    _notifications.clear();
    await _saveNotifications();
  }

  /// Remove a specific notification by ID
  Future<void> removeNotification(String id) async {
    await initialize();

    _notifications.removeWhere((notification) => notification.id == id);
    await _saveNotifications();
  }

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    await initialize();
    return List.unmodifiable(_notifications);
  }

  /// Check if there are any unread notifications
  Future<bool> hasUnreadNotifications() async {
    await initialize();
    return _notifications.any((notification) => !notification.isRead);
  }

  /// Get the count of unread notifications
  Future<int> getUnreadCount() async {
    await initialize();
    return _notifications.where((notification) => !notification.isRead).length;
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? true;
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelAllNotifications();
    }
  }

  /// Check if low balance alerts are enabled
  Future<bool> areLowBalanceAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_lowBalanceAlertsEnabledKey) ?? true;
  }

  /// Enable or disable low balance alerts
  Future<void> setLowBalanceAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_lowBalanceAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(lowBalanceNotificationId);
    }
  }

  /// Check if top-up alerts are enabled
  Future<bool> areTopUpAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_topUpAlertsEnabledKey) ?? true;
  }

  /// Enable or disable top-up alerts
  Future<void> setTopUpAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_topUpAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(topUpReminderNotificationId);
    }
  }

  /// Check if invalid record alerts are enabled
  Future<bool> areInvalidRecordAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_invalidRecordAlertsEnabledKey) ?? true;
  }

  /// Enable or disable invalid record alerts
  Future<void> setInvalidRecordAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_invalidRecordAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(invalidRecordNotificationId);
    }
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required String meterUnit,
    required double balance,
    required double threshold,
  }) async {
    await initialize();

    // Check if notifications and low balance alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final lowBalanceAlertsEnabled = await areLowBalanceAlertsEnabled();

    if (!notificationsEnabled || !lowBalanceAlertsEnabled) {
      return;
    }

    final title = 'Low Balance Alert';
    final message =
        'Your balance is low: $meterUnit${balance.toStringAsFixed(2)}. '
        'You should top up soon as you are below your alert threshold of $meterUnit${threshold.toStringAsFixed(2)}.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.alert,
      actionType: 'low_balance',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: lowBalanceNotificationId,
      title: title,
      body: message,
      payload: 'low_balance:$balance:$threshold',
    );
  }

  /// Show a time to top up notification
  Future<void> showTimeToTopUpNotification({
    required String meterUnit,
    required double balance,
    required int daysRemaining,
  }) async {
    await initialize();

    // Check if notifications and top-up alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final topUpAlertsEnabled = await areTopUpAlertsEnabled();

    if (!notificationsEnabled || !topUpAlertsEnabled) {
      return;
    }

    final title = 'Time to Top Up';
    final message =
        'Based on your usage, you should top up in $daysRemaining ${daysRemaining == 1 ? 'day' : 'days'}. '
        'Your estimated balance is $meterUnit${balance.toStringAsFixed(2)}.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'top_up',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: topUpReminderNotificationId,
      title: title,
      body: message,
      payload: 'top_up:$balance:$daysRemaining',
    );
  }

  /// Show an invalid record notification
  Future<void> showInvalidRecordNotification({
    required String message,
  }) async {
    await initialize();

    // Check if notifications and invalid record alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final invalidRecordAlertsEnabled = await areInvalidRecordAlertsEnabled();

    if (!notificationsEnabled || !invalidRecordAlertsEnabled) {
      return;
    }

    final title = 'Invalid Record Detected';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'invalid_record',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: invalidRecordNotificationId,
      title: title,
      body: message,
      payload: 'invalid_record',
    );
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_meterReadingReminderEnabledKey) ?? false;
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_meterReadingReminderEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper
          .cancelNotification(meterReadingReminderNotificationId);
    } else {
      // Schedule the next reminder based on frequency
      final frequency = await getMeterReadingReminderFrequency();
      await scheduleMeterReadingReminder(frequency);
    }
  }

  /// Check if frequency options are enabled
  Future<bool> isFrequencyOptionsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_meterReadingReminderFrequencyEnabledKey) ?? true;
  }

  /// Enable or disable frequency options
  Future<void> setFrequencyOptionsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_meterReadingReminderFrequencyEnabledKey, enabled);
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_meterReadingReminderFrequencyKey) ??
        7; // Default to weekly
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_meterReadingReminderFrequencyKey, days);

    // Reschedule the reminder with the new frequency if enabled
    final enabled = await areMeterReadingRemindersEnabled();
    if (enabled) {
      await scheduleMeterReadingReminder(days);
    }
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_meterReadingReminderLastDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _meterReadingReminderLastDateKey, date.toIso8601String());
  }

  /// Schedule a meter reading reminder based on frequency
  Future<void> scheduleMeterReadingReminder(int frequencyDays) async {
    await initialize();

    // Check if notifications and meter reading reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      return;
    }

    // Cancel any existing reminder
    await _notificationHelper
        .cancelNotification(meterReadingReminderNotificationId);

    // Schedule the next reminder
    final now = DateTime.now();
    final nextReminderDate = now.add(Duration(days: frequencyDays));

    // Update the last reminder date
    await setLastMeterReadingReminderDate(now);

    // Schedule the notification
    await _notificationHelper.scheduleNotification(
      id: meterReadingReminderNotificationId,
      title: 'Meter Reading Reminder',
      body:
          'It\'s time to record your meter reading. This helps track your usage accurately.',
      scheduledDate: nextReminderDate,
      payload: 'meter_reading_reminder:$frequencyDays',
    );
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    await initialize();

    // Check if notifications and meter reading reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      return;
    }

    const title = 'Meter Reading Reminder';
    const message =
        'It\'s time to record your meter reading. This helps track your usage accurately.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.info,
      actionType: 'meter_reading_reminder',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: meterReadingReminderNotificationId,
      title: title,
      body: message,
      payload: 'meter_reading_reminder',
    );

    // Update the last reminder date
    await setLastMeterReadingReminderDate(DateTime.now());

    // Schedule the next reminder
    final frequency = await getMeterReadingReminderFrequency();
    await scheduleMeterReadingReminder(frequency);
  }
}
