// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Composition_1_H
#define WINRT_Windows_UI_Composition_1_H
#include "winrt/impl/Windows.UI.Composition.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Composition
{
    struct __declspec(empty_bases) IAmbientLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAmbientLight>
    {
        IAmbientLight(std::nullptr_t = nullptr) noexcept {}
        IAmbientLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAmbientLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAmbientLight2>
    {
        IAmbientLight2(std::nullptr_t = nullptr) noexcept {}
        IAmbientLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnimationController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationController>
    {
        IAnimationController(std::nullptr_t = nullptr) noexcept {}
        IAnimationController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnimationControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationControllerStatics>
    {
        IAnimationControllerStatics(std::nullptr_t = nullptr) noexcept {}
        IAnimationControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnimationObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationObject>
    {
        IAnimationObject(std::nullptr_t = nullptr) noexcept {}
        IAnimationObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAnimationPropertyInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationPropertyInfo>
    {
        IAnimationPropertyInfo(std::nullptr_t = nullptr) noexcept {}
        IAnimationPropertyInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBooleanKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBooleanKeyFrameAnimation>
    {
        IBooleanKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IBooleanKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBounceScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceScalarNaturalMotionAnimation>
    {
        IBounceScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBounceVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceVector2NaturalMotionAnimation>
    {
        IBounceVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBounceVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceVector3NaturalMotionAnimation>
    {
        IBounceVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorKeyFrameAnimation>
    {
        IColorKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IColorKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation>
    {
        ICompositionAnimation(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation2>
    {
        ICompositionAnimation2(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation3>
    {
        ICompositionAnimation3(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimation4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation4>
    {
        ICompositionAnimation4(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimationBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationBase>
    {
        ICompositionAnimationBase(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationFactory>
    {
        ICompositionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionAnimationGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationGroup>
    {
        ICompositionAnimationGroup(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionBackdropBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBackdropBrush>
    {
        ICompositionBackdropBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionBackdropBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionBatchCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBatchCompletedEventArgs>
    {
        ICompositionBatchCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICompositionBatchCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBrush>
    {
        ICompositionBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBrushFactory>
    {
        ICompositionBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCapabilities>
    {
        ICompositionCapabilities(std::nullptr_t = nullptr) noexcept {}
        ICompositionCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionCapabilitiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCapabilitiesStatics>
    {
        ICompositionCapabilitiesStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionCapabilitiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClip>
    {
        ICompositionClip(std::nullptr_t = nullptr) noexcept {}
        ICompositionClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionClip2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClip2>
    {
        ICompositionClip2(std::nullptr_t = nullptr) noexcept {}
        ICompositionClip2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionClipFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClipFactory>
    {
        ICompositionClipFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionClipFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionColorBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorBrush>
    {
        ICompositionColorBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionColorGradientStop :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorGradientStop>
    {
        ICompositionColorGradientStop(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorGradientStop(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionColorGradientStopCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorGradientStopCollection>
    {
        ICompositionColorGradientStopCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorGradientStopCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionCommitBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCommitBatch>
    {
        ICompositionCommitBatch(std::nullptr_t = nullptr) noexcept {}
        ICompositionCommitBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionContainerShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionContainerShape>
    {
        ICompositionContainerShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionContainerShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionDrawingSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurface>
    {
        ICompositionDrawingSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionDrawingSurface2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurface2>
    {
        ICompositionDrawingSurface2(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurface2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionDrawingSurfaceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurfaceFactory>
    {
        ICompositionDrawingSurfaceFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurfaceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEasingFunction>
    {
        ICompositionEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ICompositionEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEasingFunctionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEasingFunctionFactory>
    {
        ICompositionEasingFunctionFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEasingFunctionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEffectBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectBrush>
    {
        ICompositionEffectBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectFactory>
    {
        ICompositionEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEffectSourceParameter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectSourceParameter>
    {
        ICompositionEffectSourceParameter(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectSourceParameter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEffectSourceParameterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectSourceParameterFactory>
    {
        ICompositionEffectSourceParameterFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectSourceParameterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionEllipseGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEllipseGeometry>
    {
        ICompositionEllipseGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionEllipseGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGeometricClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometricClip>
    {
        ICompositionGeometricClip(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometricClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometry>
    {
        ICompositionGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGeometryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometryFactory>
    {
        ICompositionGeometryFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrush>
    {
        ICompositionGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGradientBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrush2>
    {
        ICompositionGradientBrush2(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGradientBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrushFactory>
    {
        ICompositionGradientBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGraphicsDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice>
    {
        ICompositionGraphicsDevice(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGraphicsDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice2>
    {
        ICompositionGraphicsDevice2(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionGraphicsDevice3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice3>
    {
        ICompositionGraphicsDevice3(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight>
    {
        ICompositionLight(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight2>
    {
        ICompositionLight2(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight3>
    {
        ICompositionLight3(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLightFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLightFactory>
    {
        ICompositionLightFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionLightFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLineGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLineGeometry>
    {
        ICompositionLineGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionLineGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionLinearGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLinearGradientBrush>
    {
        ICompositionLinearGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionLinearGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionMaskBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionMaskBrush>
    {
        ICompositionMaskBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionMaskBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionMipmapSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionMipmapSurface>
    {
        ICompositionMipmapSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionMipmapSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionNineGridBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionNineGridBrush>
    {
        ICompositionNineGridBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionNineGridBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject>
    {
        ICompositionObject(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObject2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject2>
    {
        ICompositionObject2(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObject3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject3>
    {
        ICompositionObject3(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObject4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject4>
    {
        ICompositionObject4(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObjectFactory>
    {
        ICompositionObjectFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionObjectStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObjectStatics>
    {
        ICompositionObjectStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionObjectStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPath>
    {
        ICompositionPath(std::nullptr_t = nullptr) noexcept {}
        ICompositionPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionPathFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPathFactory>
    {
        ICompositionPathFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionPathFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionPathGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPathGeometry>
    {
        ICompositionPathGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionPathGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadow>
    {
        ICompositionProjectedShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadowCaster :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCaster>
    {
        ICompositionProjectedShadowCaster(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCaster(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadowCasterCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCasterCollection>
    {
        ICompositionProjectedShadowCasterCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCasterCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadowCasterCollectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCasterCollectionStatics>
    {
        ICompositionProjectedShadowCasterCollectionStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCasterCollectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadowReceiver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowReceiver>
    {
        ICompositionProjectedShadowReceiver(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowReceiver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionProjectedShadowReceiverUnorderedCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowReceiverUnorderedCollection>
    {
        ICompositionProjectedShadowReceiverUnorderedCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowReceiverUnorderedCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionPropertySet :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPropertySet>
    {
        ICompositionPropertySet(std::nullptr_t = nullptr) noexcept {}
        ICompositionPropertySet(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionPropertySet2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPropertySet2>
    {
        ICompositionPropertySet2(std::nullptr_t = nullptr) noexcept {}
        ICompositionPropertySet2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionRadialGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRadialGradientBrush>
    {
        ICompositionRadialGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionRadialGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionRectangleGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRectangleGeometry>
    {
        ICompositionRectangleGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionRectangleGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionRoundedRectangleGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRoundedRectangleGeometry>
    {
        ICompositionRoundedRectangleGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionRoundedRectangleGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionScopedBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionScopedBatch>
    {
        ICompositionScopedBatch(std::nullptr_t = nullptr) noexcept {}
        ICompositionScopedBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShadow>
    {
        ICompositionShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositionShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionShadowFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShadowFactory>
    {
        ICompositionShadowFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionShadowFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShape>
    {
        ICompositionShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionShapeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShapeFactory>
    {
        ICompositionShapeFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionShapeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionSpriteShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSpriteShape>
    {
        ICompositionSpriteShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionSpriteShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurface>
    {
        ICompositionSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionSurfaceBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush>
    {
        ICompositionSurfaceBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionSurfaceBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush2>
    {
        ICompositionSurfaceBrush2(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionSurfaceBrush3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush3>
    {
        ICompositionSurfaceBrush3(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTarget>
    {
        ICompositionTarget(std::nullptr_t = nullptr) noexcept {}
        ICompositionTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTargetFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTargetFactory>
    {
        ICompositionTargetFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionTargetFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTransform>
    {
        ICompositionTransform(std::nullptr_t = nullptr) noexcept {}
        ICompositionTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTransformFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTransformFactory>
    {
        ICompositionTransformFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionTransformFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionViewBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionViewBox>
    {
        ICompositionViewBox(std::nullptr_t = nullptr) noexcept {}
        ICompositionViewBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionVirtualDrawingSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVirtualDrawingSurface>
    {
        ICompositionVirtualDrawingSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionVirtualDrawingSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionVirtualDrawingSurfaceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVirtualDrawingSurfaceFactory>
    {
        ICompositionVirtualDrawingSurfaceFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionVirtualDrawingSurfaceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionVisualSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVisualSurface>
    {
        ICompositionVisualSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionVisualSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor>
    {
        ICompositor(std::nullptr_t = nullptr) noexcept {}
        ICompositor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor2>
    {
        ICompositor2(std::nullptr_t = nullptr) noexcept {}
        ICompositor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor3>
    {
        ICompositor3(std::nullptr_t = nullptr) noexcept {}
        ICompositor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor4>
    {
        ICompositor4(std::nullptr_t = nullptr) noexcept {}
        ICompositor4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor5>
    {
        ICompositor5(std::nullptr_t = nullptr) noexcept {}
        ICompositor5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositor6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor6>
    {
        ICompositor6(std::nullptr_t = nullptr) noexcept {}
        ICompositor6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorStatics>
    {
        ICompositorStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositorWithProjectedShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithProjectedShadow>
    {
        ICompositorWithProjectedShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithProjectedShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositorWithRadialGradient :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithRadialGradient>
    {
        ICompositorWithRadialGradient(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithRadialGradient(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositorWithVisualSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithVisualSurface>
    {
        ICompositorWithVisualSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithVisualSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContainerVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContainerVisual>
    {
        IContainerVisual(std::nullptr_t = nullptr) noexcept {}
        IContainerVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContainerVisualFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContainerVisualFactory>
    {
        IContainerVisualFactory(std::nullptr_t = nullptr) noexcept {}
        IContainerVisualFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICubicBezierEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICubicBezierEasingFunction>
    {
        ICubicBezierEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ICubicBezierEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDistantLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDistantLight>
    {
        IDistantLight(std::nullptr_t = nullptr) noexcept {}
        IDistantLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDistantLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDistantLight2>
    {
        IDistantLight2(std::nullptr_t = nullptr) noexcept {}
        IDistantLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropShadow>
    {
        IDropShadow(std::nullptr_t = nullptr) noexcept {}
        IDropShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropShadow2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropShadow2>
    {
        IDropShadow2(std::nullptr_t = nullptr) noexcept {}
        IDropShadow2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExpressionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpressionAnimation>
    {
        IExpressionAnimation(std::nullptr_t = nullptr) noexcept {}
        IExpressionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImplicitAnimationCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImplicitAnimationCollection>
    {
        IImplicitAnimationCollection(std::nullptr_t = nullptr) noexcept {}
        IImplicitAnimationCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInsetClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInsetClip>
    {
        IInsetClip(std::nullptr_t = nullptr) noexcept {}
        IInsetClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation>
    {
        IKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyFrameAnimation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation2>
    {
        IKeyFrameAnimation2(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyFrameAnimation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation3>
    {
        IKeyFrameAnimation3(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyFrameAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimationFactory>
    {
        IKeyFrameAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILayerVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayerVisual>
    {
        ILayerVisual(std::nullptr_t = nullptr) noexcept {}
        ILayerVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILayerVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayerVisual2>
    {
        ILayerVisual2(std::nullptr_t = nullptr) noexcept {}
        ILayerVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILinearEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearEasingFunction>
    {
        ILinearEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ILinearEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INaturalMotionAnimation>
    {
        INaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        INaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INaturalMotionAnimationFactory>
    {
        INaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        INaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathKeyFrameAnimation>
    {
        IPathKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IPathKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPointLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight>
    {
        IPointLight(std::nullptr_t = nullptr) noexcept {}
        IPointLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPointLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight2>
    {
        IPointLight2(std::nullptr_t = nullptr) noexcept {}
        IPointLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPointLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight3>
    {
        IPointLight3(std::nullptr_t = nullptr) noexcept {}
        IPointLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IQuaternionKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuaternionKeyFrameAnimation>
    {
        IQuaternionKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IQuaternionKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRedirectVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedirectVisual>
    {
        IRedirectVisual(std::nullptr_t = nullptr) noexcept {}
        IRedirectVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRenderingDeviceReplacedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderingDeviceReplacedEventArgs>
    {
        IRenderingDeviceReplacedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRenderingDeviceReplacedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScalarKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarKeyFrameAnimation>
    {
        IScalarKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IScalarKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarNaturalMotionAnimation>
    {
        IScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScalarNaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarNaturalMotionAnimationFactory>
    {
        IScalarNaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IScalarNaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShapeVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShapeVisual>
    {
        IShapeVisual(std::nullptr_t = nullptr) noexcept {}
        IShapeVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpotLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight>
    {
        ISpotLight(std::nullptr_t = nullptr) noexcept {}
        ISpotLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpotLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight2>
    {
        ISpotLight2(std::nullptr_t = nullptr) noexcept {}
        ISpotLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpotLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight3>
    {
        ISpotLight3(std::nullptr_t = nullptr) noexcept {}
        ISpotLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpringScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringScalarNaturalMotionAnimation>
    {
        ISpringScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpringVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringVector2NaturalMotionAnimation>
    {
        ISpringVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpringVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringVector3NaturalMotionAnimation>
    {
        ISpringVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpriteVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpriteVisual>
    {
        ISpriteVisual(std::nullptr_t = nullptr) noexcept {}
        ISpriteVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpriteVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpriteVisual2>
    {
        ISpriteVisual2(std::nullptr_t = nullptr) noexcept {}
        ISpriteVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStepEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStepEasingFunction>
    {
        IStepEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IStepEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector2KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2KeyFrameAnimation>
    {
        IVector2KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector2KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2NaturalMotionAnimation>
    {
        IVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector2NaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2NaturalMotionAnimationFactory>
    {
        IVector2NaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IVector2NaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector3KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3KeyFrameAnimation>
    {
        IVector3KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector3KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3NaturalMotionAnimation>
    {
        IVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector3NaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3NaturalMotionAnimationFactory>
    {
        IVector3NaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IVector3NaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVector4KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector4KeyFrameAnimation>
    {
        IVector4KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector4KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual>
    {
        IVisual(std::nullptr_t = nullptr) noexcept {}
        IVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual2>
    {
        IVisual2(std::nullptr_t = nullptr) noexcept {}
        IVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisual3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual3>
    {
        IVisual3(std::nullptr_t = nullptr) noexcept {}
        IVisual3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualCollection>
    {
        IVisualCollection(std::nullptr_t = nullptr) noexcept {}
        IVisualCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualElement>
    {
        IVisualElement(std::nullptr_t = nullptr) noexcept {}
        IVisualElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualFactory>
    {
        IVisualFactory(std::nullptr_t = nullptr) noexcept {}
        IVisualFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualUnorderedCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualUnorderedCollection>
    {
        IVisualUnorderedCollection(std::nullptr_t = nullptr) noexcept {}
        IVisualUnorderedCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
