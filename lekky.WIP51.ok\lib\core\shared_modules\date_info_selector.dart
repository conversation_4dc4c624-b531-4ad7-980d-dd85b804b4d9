// File: lib/core/shared_modules/date_info_selector.dart
import 'package:flutter/material.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class DateInfoSelector extends BaseSettingsWidget {
  final String currentValue;
  final Function(String) onChanged;
  final String? errorText;

  const DateInfoSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  // Date info options
  static const List<String> dateInfoOptions = [
    'Date only',
    'Date and Time',
  ];

  // Get a formatted example for a date info option
  String getFormattedExample(String option, String dateFormat) {
    final now = DateTime.now();
    final day = now.day.toString().padLeft(2, '0');
    final month = now.month.toString().padLeft(2, '0');
    final year = now.year.toString();
    final hour = now.hour.toString().padLeft(2, '0');
    final minute = now.minute.toString().padLeft(2, '0');

    String dateStr;
    switch (dateFormat) {
      case 'DD-MM-YYYY':
        dateStr = '$day-$month-$year';
        break;
      case 'MM-DD-YYYY':
        dateStr = '$month-$day-$year';
        break;
      case 'YYYY-MM-DD':
        dateStr = '$year-$month-$day';
        break;
      case 'DD/MM/YYYY':
        dateStr = '$day/$month/$year';
        break;
      case 'MM/DD/YYYY':
        dateStr = '$month/$day/$year';
        break;
      case 'YYYY/MM/DD':
        dateStr = '$year/$month/$day';
        break;
      default:
        dateStr = '$day-$month-$year';
    }

    if (option == 'Date and Time') {
      return '$dateStr $hour:$minute';
    } else {
      return dateStr;
    }
  }

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Date Information'),

        if (showHelperText)
          buildHelperText(context,
              'Choose whether to show just the date or both date and time for your meter readings.'),

        const SizedBox(height: 16),

        // Date info options as radio buttons
        ...dateInfoOptions.map((option) {
          return RadioListTile<String>(
            title: Text(option),
            subtitle: Text(
              'Example: ${getFormattedExample(option, 'DD-MM-YYYY')}',
              style: const TextStyle(fontSize: 12),
            ),
            value: option,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            activeColor: primaryColor,
          );
        }).toList(),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Date Information'),

        if (showTitle)
          Text(
            'Current: $currentValue',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (showHelperText)
          buildHelperText(context, 'Show date only or date and time'),

        const SizedBox(height: 8),

        // Date info options as more compact radio buttons
        ...dateInfoOptions.map((option) {
          return RadioListTile<String>(
            title: Text(
              option,
              style: const TextStyle(fontSize: 13),
            ),
            subtitle: Text(
              'Example: ${getFormattedExample(option, 'DD-MM-YYYY')}',
              style: const TextStyle(fontSize: 11),
            ),
            value: option,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            activeColor: primaryColor,
          );
        }).toList(),

        buildErrorText(context, errorText),
      ],
    );
  }
}
