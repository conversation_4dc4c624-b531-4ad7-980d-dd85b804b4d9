// File: lib/core/widgets/home_notification_button.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../theme/app_colors.dart';
import 'notification_dialog.dart';

/// A larger notification button with badge specifically for the HomePage
class HomeNotificationButton extends StatelessWidget {
  const HomeNotificationButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final hasUnread = notificationProvider.hasUnread;
    final unreadCount = notificationProvider.unreadCount;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        IconButton(
          icon: const Icon(
            Icons.notifications_outlined,
            size: 30, // Larger icon size for HomePage
          ),
          onPressed: () => NotificationDialog.show(context),
          color: Colors.white,
          tooltip: 'Notifications',
          padding: const EdgeInsets.all(12), // Adjusted padding for larger icon
        ),
        if (hasUnread)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: EdgeInsets.all(unreadCount > 9 ? 4 : 5),
              decoration: BoxDecoration(
                color: AppColors.error,
                shape: unreadCount > 9 ? BoxShape.rectangle : BoxShape.circle,
                borderRadius: unreadCount > 9 ? BorderRadius.circular(10) : null,
                border: Border.all(
                  color: Colors.white,
                  width: 1.5,
                ),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: unreadCount > 0
                  ? Center(
                      child: Text(
                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: unreadCount > 9 ? 8 : 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
      ],
    );
  }
}
