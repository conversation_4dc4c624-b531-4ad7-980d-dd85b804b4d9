// File: lib/features/setup/presentation/widgets/notifications_toggle.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A widget for toggling notifications
class NotificationsToggle extends StatelessWidget {
  final bool notificationsEnabled;
  final Function(bool) onToggle;

  const NotificationsToggle({
    Key? key,
    required this.notificationsEnabled,
    required this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notifications',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Enable notifications to receive alerts when your meter balance is low',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Enable Notifications'),
            subtitle: Text(
              notificationsEnabled
                  ? 'You will receive alerts when your meter balance is low'
                  : 'You will not receive any alerts',
            ),
            value: notificationsEnabled,
            onChanged: onToggle,
            dense: true,
          ),
          if (notificationsEnabled)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'Note: You may need to grant permission for notifications in your device settings',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
