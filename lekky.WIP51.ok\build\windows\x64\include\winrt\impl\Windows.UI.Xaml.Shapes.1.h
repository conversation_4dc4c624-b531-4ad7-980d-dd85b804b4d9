// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Shapes_1_H
#define WINRT_Windows_UI_Xaml_Shapes_1_H
#include "winrt/impl/Windows.UI.Xaml.Shapes.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Shapes
{
    struct __declspec(empty_bases) IEllipse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEllipse>
    {
        IEllipse(std::nullptr_t = nullptr) noexcept {}
        IEllipse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILine :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILine>
    {
        ILine(std::nullptr_t = nullptr) noexcept {}
        ILine(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineStatics>
    {
        ILineStatics(std::nullptr_t = nullptr) noexcept {}
        ILineStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPath>
    {
        IPath(std::nullptr_t = nullptr) noexcept {}
        IPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathFactory>
    {
        IPathFactory(std::nullptr_t = nullptr) noexcept {}
        IPathFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathStatics>
    {
        IPathStatics(std::nullptr_t = nullptr) noexcept {}
        IPathStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolygon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolygon>
    {
        IPolygon(std::nullptr_t = nullptr) noexcept {}
        IPolygon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolygonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolygonStatics>
    {
        IPolygonStatics(std::nullptr_t = nullptr) noexcept {}
        IPolygonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyline>
    {
        IPolyline(std::nullptr_t = nullptr) noexcept {}
        IPolyline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolylineStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolylineStatics>
    {
        IPolylineStatics(std::nullptr_t = nullptr) noexcept {}
        IPolylineStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRectangle :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectangle>
    {
        IRectangle(std::nullptr_t = nullptr) noexcept {}
        IRectangle(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRectangleStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectangleStatics>
    {
        IRectangleStatics(std::nullptr_t = nullptr) noexcept {}
        IRectangleStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShape>
    {
        IShape(std::nullptr_t = nullptr) noexcept {}
        IShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShape2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShape2>
    {
        IShape2(std::nullptr_t = nullptr) noexcept {}
        IShape2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShapeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShapeFactory>
    {
        IShapeFactory(std::nullptr_t = nullptr) noexcept {}
        IShapeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShapeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShapeStatics>
    {
        IShapeStatics(std::nullptr_t = nullptr) noexcept {}
        IShapeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
