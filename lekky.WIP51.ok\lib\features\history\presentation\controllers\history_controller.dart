// File: lib/features/history/presentation/controllers/history_controller.dart
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/event_bus.dart';
import '../../data/history_repository.dart';
import '../../domain/models/history_filter.dart';
import '../../domain/models/validation_result.dart';
import '../../domain/models/related_validation_result.dart';

/// Controller for the history screen
class HistoryController extends ChangeNotifier {
  final HistoryRepository _repository;

  // State variables
  List<MeterEntry> _allEntries = [];
  List<MeterEntry> _filteredEntries = [];
  HistoryFilter _filter = const HistoryFilter();
  Map<int, ValidationResult> _validationResults = {};
  String _meterUnit = '£';
  String _dateFormat = 'DD-MM-YYYY';
  bool _isLoading = true;
  bool _isEditMode = false;
  String _error = '';
  int _selectedEntryId = -1;
  int _currentPage = 0;
  int _itemsPerPage = 15;

  // Getters
  List<MeterEntry> get allEntries => _allEntries;
  List<MeterEntry> get filteredEntries => _filteredEntries;
  HistoryFilter get filter => _filter;
  Map<int, ValidationResult> get validationResults => _validationResults;

  /// Debug method to print validation results
  void debugPrintValidationResults() {
    print('DEBUG: Validation Results:');
    for (final entry in _validationResults.entries) {
      print(
          'Entry ID: ${entry.key}, Valid: ${entry.value.isValid}, Message: ${entry.value.errorMessage}');
      if (entry.value is RelatedValidationResult) {
        final relatedResult = entry.value as RelatedValidationResult;
        print('  Related Entry ID: ${relatedResult.relatedEntryId}');
      }
    }
  }

  String get meterUnit => _meterUnit;
  String get dateFormat => _dateFormat;
  bool get isLoading => _isLoading;
  bool get isEditMode => _isEditMode;
  String get error => _error;
  int get selectedEntryId => _selectedEntryId;
  int get currentPage => _currentPage;
  int get itemsPerPage => _itemsPerPage;

  // Computed getter for total pages
  int get totalPages => (_filteredEntries.length / _itemsPerPage).ceil() > 0
      ? (_filteredEntries.length / _itemsPerPage).ceil()
      : 1; // At least one page even if empty

  // Computed getters
  bool get hasEntries => _allEntries.isNotEmpty;
  bool get hasFilteredEntries => _filteredEntries.isNotEmpty;
  int get entryCount => _filteredEntries.length;
  bool get hasValidationErrors =>
      _validationResults.values.any((result) => !result.isValid);

  /// Count of invalid entries, including both directly invalid entries and
  /// entries that are related to invalid entries
  int get invalidEntryCount {
    // First count directly invalid entries
    final directlyInvalidCount =
        _validationResults.values.where((result) => !result.isValid).length;

    // Then count entries that are related to invalid entries
    final Set<int> relatedEntryIds = {};
    for (final result in _validationResults.values) {
      if (!result.isValid &&
          result is RelatedValidationResult &&
          result.relatedEntryId != null) {
        relatedEntryIds.add(result.relatedEntryId!);
      }
    }

    // Return the total count
    return directlyInvalidCount + relatedEntryIds.length;
  }

  /// Check if there are enough meter readings to show date range filter
  /// We need at least 2 meter readings to show date range filter
  bool get hasEnoughReadingsForDateRange {
    // Filter to only include meter readings (not top-ups)
    final readings = _allEntries
        .where((e) => e.reading > 0 && e.amountToppedUp == 0)
        .toList();
    // Need at least 2 readings for date range
    return readings.length >= 2;
  }

  HistoryController(this._repository);

  /// Initialize the controller
  Future<void> init() async {
    _isLoading = true;
    _isEditMode = false; // Ensure edit mode is off by default
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load data: $e';
      notifyListeners();
    }
  }

  /// Refresh the data
  Future<void> refresh() async {
    _isLoading = true;
    _isEditMode = false; // Reset edit mode on refresh
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to refresh data: $e';
      notifyListeners();
    }
  }

  /// Load all data
  Future<void> _loadData() async {
    // Load data in parallel
    final results = await Future.wait([
      _repository.getAllEntries(),
      _repository.getMeterUnit(),
      _repository.getDateFormat(),
    ]);

    _allEntries = results[0] as List<MeterEntry>;
    _meterUnit = results[1] as String;
    _dateFormat = results[2] as String;

    // Calculate averages
    print("DEBUG: Before calculating averages: ${_allEntries.length} entries");
    for (final entry in _allEntries) {
      if (entry.amountToppedUp == 0) {
        // Only check meter readings
        print("DEBUG: Before: Entry ${entry.id}: Date=${DateTimeUtils.formatDateDefault(entry.timestamp)}, " +
            "Reading=${entry.reading}, ShortAvg=${entry.shortAverageAfterTopUp}, TotalAvg=${entry.totalAverageUpToThisPoint}");
      }
    }

    _allEntries = _repository.calculateAverages(_allEntries);

    print("DEBUG: After calculating averages: ${_allEntries.length} entries");
    for (final entry in _allEntries) {
      if (entry.amountToppedUp == 0) {
        // Only check meter readings
        print("DEBUG: After: Entry ${entry.id}: Date=${DateTimeUtils.formatDateDefault(entry.timestamp)}, " +
            "Reading=${entry.reading}, ShortAvg=${entry.shortAverageAfterTopUp}, TotalAvg=${entry.totalAverageUpToThisPoint}");
      }
    }

    // Apply filter
    _applyFilter();

    // Validate entries
    await _validateEntries();
  }

  /// Apply the current filter
  Future<void> _applyFilter() async {
    _filteredEntries = await _repository.getFilteredEntries(_filter);
    notifyListeners();
  }

  /// Update the filter
  Future<void> updateFilter(HistoryFilter filter) async {
    _filter = filter;
    await _applyFilter();
  }

  /// Reset the filter
  Future<void> resetFilter() async {
    _filter = _filter.reset();
    await _applyFilter();
  }

  /// Validate all entries
  Future<void> _validateEntries() async {
    _validationResults = await _repository.validateEntries(_allEntries);

    // Debug validation results
    debugPrintValidationResults();

    notifyListeners();
  }

  /// Toggle edit mode
  void toggleEditMode() {
    _isEditMode = !_isEditMode;
    notifyListeners();
  }

  /// Select an entry
  void selectEntry(int id) {
    _selectedEntryId = id;
    notifyListeners();
  }

  /// Clear selection
  void clearSelection() {
    _selectedEntryId = -1;
    notifyListeners();
  }

  /// Add a new entry
  Future<void> addEntry(MeterEntry entry) async {
    try {
      await _repository.addEntry(entry);
      await refresh();

      // Notify other parts of the app that data has changed
      EventBus().fire(EventType.dataUpdated);
    } catch (e) {
      _error = 'Failed to add entry: $e';
      notifyListeners();
    }
  }

  /// Delete an entry
  Future<void> deleteEntry(int id) async {
    try {
      await _repository.deleteEntry(id);
      await refresh();

      // Notify other parts of the app that data has changed
      EventBus().fire(EventType.dataUpdated);
    } catch (e) {
      _error = 'Failed to delete entry: $e';
      notifyListeners();
    }
  }

  /// Format a date according to the current date format
  String formatDate(DateTime date) {
    switch (_dateFormat) {
      case 'DD-MM-YYYY':
        return DateTimeUtils.formatDateDefault(date);
      case 'MM-DD-YYYY':
        return DateTimeUtils.formatDate(date, 'MM-dd-yyyy');
      case 'YYYY-MM-DD':
        return DateTimeUtils.formatDate(date, 'yyyy-MM-dd');
      case 'DD MMM YYYY':
        return DateTimeUtils.formatDateWithMonthName(date);
      case 'MMM DD, YYYY':
        return DateTimeUtils.formatDate(date, 'MMM dd, yyyy');
      default:
        return DateTimeUtils.formatDateDefault(date);
    }
  }

  /// Get the current date info setting
  Future<String> getDateInfo() async {
    return await _repository.getDateInfo();
  }

  /// Get the validation result for an entry
  ValidationResult getValidationResult(int id) {
    return _validationResults[id] ?? ValidationResult.valid();
  }

  /// Filter to show only invalid entries
  Future<void> filterInvalidEntries(HistoryFilter filter) async {
    // First update the filter state with the isInvalidFilter flag set to true
    _filter = filter.copyWith(isInvalidFilter: true);

    // Get all entries
    final allEntries = await _repository.getAllEntries();

    // Find IDs of entries related to invalid entries
    final Set<int> relatedEntryIds = {};
    for (final result in _validationResults.values) {
      if (!result.isValid &&
          result is RelatedValidationResult &&
          result.relatedEntryId != null) {
        relatedEntryIds.add(result.relatedEntryId!);
      }
    }

    // Filter to include both directly invalid entries and related entries
    _filteredEntries = allEntries.where((entry) {
      if (entry.id == null) return false;

      // Check if the entry is directly invalid
      final result = _validationResults[entry.id!];
      final isDirectlyInvalid = result != null && !result.isValid;

      // Check if the entry is related to an invalid entry
      final isRelatedToInvalidEntry = relatedEntryIds.contains(entry.id);

      return isDirectlyInvalid || isRelatedToInvalidEntry;
    }).toList();

    // Sort by date (newest first)
    _filteredEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Even if there are no invalid entries, keep the filter applied
    // This will show an empty list with the filter chip still active
    notifyListeners();
  }

  /// Check if an entry is valid
  bool isEntryValid(int id) {
    // First check if the entry itself is invalid
    if (_validationResults[id]?.isValid == false) {
      print("DEBUG: Entry $id is directly invalid");
      return false;
    }

    // Then check if this entry is related to any invalid entry
    // (i.e., it was used to validate against an invalid entry)
    for (final result in _validationResults.values) {
      // Skip valid results
      if (result.isValid) continue;

      // Check if this is a RelatedValidationResult with a relatedEntryId
      if (result is RelatedValidationResult && result.relatedEntryId == id) {
        print("DEBUG: Entry $id is related to an invalid entry");
        return false; // This entry is related to an invalid entry
      }
    }

    return true;
  }

  /// Get the validation error message for an entry
  String? getValidationErrorMessage(int id) {
    return _validationResults[id]?.errorMessage;
  }

  /// Get the validation severity for an entry
  String getValidationSeverity(int id) {
    return _validationResults[id]?.severity ?? 'none';
  }

  /// Go to a specific page
  void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      _currentPage = page;
      notifyListeners();
    }
  }

  /// Validate a single entry
  Future<ValidationResult> validateEntry(MeterEntry entry) async {
    try {
      return await _repository.validateEntry(entry);
    } catch (e) {
      _error = 'Failed to validate entry: $e';
      notifyListeners();
      return ValidationResult.error('Failed to validate entry');
    }
  }
}
