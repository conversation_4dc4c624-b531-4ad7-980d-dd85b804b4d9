// File: lib/features/home/<USER>/widgets/meter_info_card.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A card that displays the last reading date and date to top up
class MeterInfoCard extends StatelessWidget {
  final String lastReadingDate;
  final String dateToTopUp;
  final VoidCallback? onTap;
  final bool isLoading;

  const MeterInfoCard({
    super.key,
    required this.lastReadingDate,
    required this.dateToTopUp,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.lastReadingLabelDark : AppColors.primary;
    final valueColor =
        isDarkMode ? AppColors.valueTextDark : AppColors.onBackground;

    return AppCard(
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Last Reading Column
                Expanded(
                  child: Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 20,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Last Reading',
                            style: AppTextStyles.labelLarge.copyWith(
                              color: labelColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            lastReadingDate,
                            style: AppTextStyles.bodyLarge.copyWith(
                              fontWeight: FontWeight.w500,
                              color: valueColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Top Up By Column
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 20,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Top Up By',
                            style: AppTextStyles.labelLarge.copyWith(
                              color: labelColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            dateToTopUp,
                            style: AppTextStyles.bodyLarge.copyWith(
                              fontWeight: FontWeight.w500,
                              color: valueColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}
