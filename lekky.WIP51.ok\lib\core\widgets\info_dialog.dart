// File: lib/core/widgets/info_dialog.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// A reusable dialog for displaying information with a "Got it" button
class InfoDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget content;
  final VoidCallback? onGotIt;
  final String gotItText;

  const InfoDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.content,
    this.onGotIt,
    this.gotItText = 'Got it',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;
    final primaryColor = isDarkMode ? AppColors.primaryDark : AppColors.primary;

    return Dialog(
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 10),
                Text(
                  title,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.onSurface),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: 'Close',
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                  iconSize: 20,
                ),
              ],
            ),
            
            // Subtitle
            Padding(
              padding: const EdgeInsets.only(left: 34), // Align with title text
              child: Text(
                subtitle,
                style: AppTextStyles.bodyMedium,
              ),
            ),
            const SizedBox(height: 16),
            
            // Content
            content,
            const SizedBox(height: 16),
            
            // "Got it" button
            Center(
              child: TextButton(
                onPressed: () {
                  onGotIt?.call();
                  Navigator.of(context).pop();
                },
                style: TextButton.styleFrom(
                  foregroundColor: primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: Text(
                  gotItText,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
