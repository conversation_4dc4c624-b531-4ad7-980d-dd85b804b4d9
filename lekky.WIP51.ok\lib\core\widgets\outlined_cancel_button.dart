// File: lib/core/widgets/outlined_cancel_button.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// A standardized outlined cancel button with blue border and text
/// 
/// This button is designed to be used across the app for cancel actions
/// in dialogs and forms, providing a consistent look and feel.
class OutlinedCancelButton extends StatelessWidget {
  /// The callback when the button is pressed
  final VoidCallback? onPressed;
  
  /// The text to display on the button (defaults to 'Cancel')
  final String text;
  
  /// The height of the button (defaults to 40)
  final double height;
  
  /// The width of the button (defaults to null, which means it will size to its content)
  final double? width;
  
  /// The border radius of the button (defaults to 8)
  final double borderRadius;
  
  /// Creates an outlined cancel button
  const OutlinedCancelButton({
    Key? key,
    required this.onPressed,
    this.text = 'Cancel',
    this.height = 40,
    this.width,
    this.borderRadius = 8,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the primary color (blue) for the cancel button
    final cancelColor = AppColors.primary;
    
    return SizedBox(
      height: height,
      width: width,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: cancelColor,
          side: BorderSide(color: cancelColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: Text(
          text,
          style: AppTextStyles.labelLarge.copyWith(
            color: cancelColor,
          ),
        ),
      ),
    );
  }
}
