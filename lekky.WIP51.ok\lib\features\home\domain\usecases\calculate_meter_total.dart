// File: lib/features/home/<USER>/usecases/calculate_meter_total.dart
import '../../../../core/data/repositories/meter_entry_repository.dart';

/// Use case for calculating the current meter total
class CalculateMeterTotal {
  final MeterEntryRepository _repository;

  CalculateMeterTotal(this._repository);

  /// Execute the use case
  Future<double> execute() async {
    return await _repository.calculateMeterTotalWithCache();
  }
}
