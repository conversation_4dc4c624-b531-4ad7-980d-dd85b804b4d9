// File: lib/core/widgets/theme_toggle.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

/// A widget for toggling between light and dark themes
class ThemeToggle extends StatelessWidget {
  const ThemeToggle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final currentTheme = themeProvider.themeModeString;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Appearance title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              Icon(
                Icons.palette_outlined,
                color: primaryColor,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      'Appearance',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Choose how <PERSON>kky looks to you',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Center the theme options horizontally
        Align(
          alignment: Alignment.center,
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 8, // Horizontal spacing between buttons
            runSpacing: 8, // Vertical spacing between rows if they wrap
            children: [
              _buildThemeOption(
                context: context,
                label: 'Light',
                icon: Icons.light_mode,
                value: 'light',
                isSelected: currentTheme == 'light',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: const Color(0xFFFFA000), // Amber for light mode
                onTap: () => themeProvider.setThemeModeFromString('light'),
              ),
              _buildThemeOption(
                context: context,
                label: 'Dark',
                icon: Icons.dark_mode,
                value: 'dark',
                isSelected: currentTheme == 'dark',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: const Color(0xFF90CAF9), // Blue for dark mode
                onTap: () => themeProvider.setThemeModeFromString('dark'),
              ),
              _buildThemeOption(
                context: context,
                label: 'System',
                icon: Icons.brightness_auto,
                value: 'system',
                isSelected: currentTheme == 'system',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: primaryColor,
                onTap: () => themeProvider.setThemeModeFromString('system'),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required String label,
    required IconData icon,
    required String value,
    required bool isSelected,
    required Color backgroundColor,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final selectedColor = Theme.of(context).colorScheme.primary;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80, // Reduced width
        padding: const EdgeInsets.symmetric(
            vertical: 10, horizontal: 6), // Reduced padding
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? selectedColor : Colors.transparent,
            width: 2,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? selectedColor : iconColor,
              size: 28, // Smaller icon
            ),
            const SizedBox(height: 6), // Reduced spacing
            Text(
              label,
              style: textTheme.bodySmall?.copyWith(
                // Smaller text
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? selectedColor : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
