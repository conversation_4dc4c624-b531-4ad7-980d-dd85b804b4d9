// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Controls_1_H
#define WINRT_Windows_UI_Xaml_Controls_1_H
#include "winrt/impl/Windows.UI.Xaml.Controls.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Controls
{
    struct __declspec(empty_bases) IAnchorRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnchorRequestedEventArgs>
    {
        IAnchorRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAnchorRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBar>
    {
        IAppBar(std::nullptr_t = nullptr) noexcept {}
        IAppBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBar2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBar2>
    {
        IAppBar2(std::nullptr_t = nullptr) noexcept {}
        IAppBar2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBar3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBar3>
    {
        IAppBar3(std::nullptr_t = nullptr) noexcept {}
        IAppBar3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBar4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBar4>
    {
        IAppBar4(std::nullptr_t = nullptr) noexcept {}
        IAppBar4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButton>
    {
        IAppBarButton(std::nullptr_t = nullptr) noexcept {}
        IAppBarButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButton3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButton3>
    {
        IAppBarButton3(std::nullptr_t = nullptr) noexcept {}
        IAppBarButton3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButton4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButton4>
    {
        IAppBarButton4(std::nullptr_t = nullptr) noexcept {}
        IAppBarButton4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButton5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButton5>
    {
        IAppBarButton5(std::nullptr_t = nullptr) noexcept {}
        IAppBarButton5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonFactory>
    {
        IAppBarButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonStatics>
    {
        IAppBarButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonStatics3>
    {
        IAppBarButtonStatics3(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonStatics4>
    {
        IAppBarButtonStatics4(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarElementContainer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarElementContainer>
    {
        IAppBarElementContainer(std::nullptr_t = nullptr) noexcept {}
        IAppBarElementContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarElementContainerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarElementContainerFactory>
    {
        IAppBarElementContainerFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarElementContainerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarElementContainerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarElementContainerStatics>
    {
        IAppBarElementContainerStatics(std::nullptr_t = nullptr) noexcept {}
        IAppBarElementContainerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarFactory>
    {
        IAppBarFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarOverrides>
    {
        IAppBarOverrides(std::nullptr_t = nullptr) noexcept {}
        IAppBarOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarOverrides3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarOverrides3>
    {
        IAppBarOverrides3(std::nullptr_t = nullptr) noexcept {}
        IAppBarOverrides3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarSeparator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarSeparator>
    {
        IAppBarSeparator(std::nullptr_t = nullptr) noexcept {}
        IAppBarSeparator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarSeparatorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarSeparatorFactory>
    {
        IAppBarSeparatorFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarSeparatorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarSeparatorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarSeparatorStatics>
    {
        IAppBarSeparatorStatics(std::nullptr_t = nullptr) noexcept {}
        IAppBarSeparatorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarSeparatorStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarSeparatorStatics3>
    {
        IAppBarSeparatorStatics3(std::nullptr_t = nullptr) noexcept {}
        IAppBarSeparatorStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarStatics>
    {
        IAppBarStatics(std::nullptr_t = nullptr) noexcept {}
        IAppBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarStatics2>
    {
        IAppBarStatics2(std::nullptr_t = nullptr) noexcept {}
        IAppBarStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarStatics4>
    {
        IAppBarStatics4(std::nullptr_t = nullptr) noexcept {}
        IAppBarStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButton>
    {
        IAppBarToggleButton(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButton3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButton3>
    {
        IAppBarToggleButton3(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButton3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButton4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButton4>
    {
        IAppBarToggleButton4(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButton4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButton5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButton5>
    {
        IAppBarToggleButton5(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButton5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonFactory>
    {
        IAppBarToggleButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonStatics>
    {
        IAppBarToggleButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonStatics3>
    {
        IAppBarToggleButtonStatics3(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonStatics4>
    {
        IAppBarToggleButtonStatics4(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBox>
    {
        IAutoSuggestBox(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBox2>
    {
        IAutoSuggestBox2(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBox3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBox3>
    {
        IAutoSuggestBox3(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBox3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBox4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBox4>
    {
        IAutoSuggestBox4(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBox4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxQuerySubmittedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxQuerySubmittedEventArgs>
    {
        IAutoSuggestBoxQuerySubmittedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxQuerySubmittedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxStatics>
    {
        IAutoSuggestBoxStatics(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxStatics2>
    {
        IAutoSuggestBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxStatics3>
    {
        IAutoSuggestBoxStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxStatics4>
    {
        IAutoSuggestBoxStatics4(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxSuggestionChosenEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxSuggestionChosenEventArgs>
    {
        IAutoSuggestBoxSuggestionChosenEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxSuggestionChosenEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxTextChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxTextChangedEventArgs>
    {
        IAutoSuggestBoxTextChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxTextChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxTextChangedEventArgsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxTextChangedEventArgsStatics>
    {
        IAutoSuggestBoxTextChangedEventArgsStatics(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxTextChangedEventArgsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBackClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackClickEventArgs>
    {
        IBackClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBackClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIcon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIcon>
    {
        IBitmapIcon(std::nullptr_t = nullptr) noexcept {}
        IBitmapIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIcon2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIcon2>
    {
        IBitmapIcon2(std::nullptr_t = nullptr) noexcept {}
        IBitmapIcon2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconFactory>
    {
        IBitmapIconFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconSource>
    {
        IBitmapIconSource(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconSourceFactory>
    {
        IBitmapIconSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconSourceStatics>
    {
        IBitmapIconSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconStatics>
    {
        IBitmapIconStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapIconStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapIconStatics2>
    {
        IBitmapIconStatics2(std::nullptr_t = nullptr) noexcept {}
        IBitmapIconStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBorder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBorder>
    {
        IBorder(std::nullptr_t = nullptr) noexcept {}
        IBorder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBorder2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBorder2>
    {
        IBorder2(std::nullptr_t = nullptr) noexcept {}
        IBorder2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBorderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBorderStatics>
    {
        IBorderStatics(std::nullptr_t = nullptr) noexcept {}
        IBorderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBorderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBorderStatics2>
    {
        IBorderStatics2(std::nullptr_t = nullptr) noexcept {}
        IBorderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButton>
    {
        IButton(std::nullptr_t = nullptr) noexcept {}
        IButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonFactory>
    {
        IButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonStaticsWithFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonStaticsWithFlyout>
    {
        IButtonStaticsWithFlyout(std::nullptr_t = nullptr) noexcept {}
        IButtonStaticsWithFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonWithFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonWithFlyout>
    {
        IButtonWithFlyout(std::nullptr_t = nullptr) noexcept {}
        IButtonWithFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePicker>
    {
        ICalendarDatePicker(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePicker2>
    {
        ICalendarDatePicker2(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePicker3>
    {
        ICalendarDatePicker3(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerDateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerDateChangedEventArgs>
    {
        ICalendarDatePickerDateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerDateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerFactory>
    {
        ICalendarDatePickerFactory(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerStatics>
    {
        ICalendarDatePickerStatics(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerStatics2>
    {
        ICalendarDatePickerStatics2(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerStatics3>
    {
        ICalendarDatePickerStatics3(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarView>
    {
        ICalendarView(std::nullptr_t = nullptr) noexcept {}
        ICalendarView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewDayItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewDayItem>
    {
        ICalendarViewDayItem(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewDayItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewDayItemChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewDayItemChangingEventArgs>
    {
        ICalendarViewDayItemChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewDayItemChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewDayItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewDayItemFactory>
    {
        ICalendarViewDayItemFactory(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewDayItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewDayItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewDayItemStatics>
    {
        ICalendarViewDayItemStatics(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewDayItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewFactory>
    {
        ICalendarViewFactory(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewSelectedDatesChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewSelectedDatesChangedEventArgs>
    {
        ICalendarViewSelectedDatesChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewSelectedDatesChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewStatics>
    {
        ICalendarViewStatics(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICandidateWindowBoundsChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICandidateWindowBoundsChangedEventArgs>
    {
        ICandidateWindowBoundsChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICandidateWindowBoundsChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICanvas :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICanvas>
    {
        ICanvas(std::nullptr_t = nullptr) noexcept {}
        ICanvas(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICanvasFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICanvasFactory>
    {
        ICanvasFactory(std::nullptr_t = nullptr) noexcept {}
        ICanvasFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICanvasStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICanvasStatics>
    {
        ICanvasStatics(std::nullptr_t = nullptr) noexcept {}
        ICanvasStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICaptureElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICaptureElement>
    {
        ICaptureElement(std::nullptr_t = nullptr) noexcept {}
        ICaptureElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICaptureElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICaptureElementStatics>
    {
        ICaptureElementStatics(std::nullptr_t = nullptr) noexcept {}
        ICaptureElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICheckBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICheckBox>
    {
        ICheckBox(std::nullptr_t = nullptr) noexcept {}
        ICheckBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICheckBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICheckBoxFactory>
    {
        ICheckBoxFactory(std::nullptr_t = nullptr) noexcept {}
        ICheckBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IChoosingGroupHeaderContainerEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChoosingGroupHeaderContainerEventArgs>
    {
        IChoosingGroupHeaderContainerEventArgs(std::nullptr_t = nullptr) noexcept {}
        IChoosingGroupHeaderContainerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IChoosingItemContainerEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChoosingItemContainerEventArgs>
    {
        IChoosingItemContainerEventArgs(std::nullptr_t = nullptr) noexcept {}
        IChoosingItemContainerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICleanUpVirtualizedItemEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICleanUpVirtualizedItemEventArgs>
    {
        ICleanUpVirtualizedItemEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICleanUpVirtualizedItemEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorChangedEventArgs>
    {
        IColorChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IColorChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPicker>
    {
        IColorPicker(std::nullptr_t = nullptr) noexcept {}
        IColorPicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerFactory>
    {
        IColorPickerFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPickerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerStatics>
    {
        IColorPickerStatics(std::nullptr_t = nullptr) noexcept {}
        IColorPickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColumnDefinition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColumnDefinition>
    {
        IColumnDefinition(std::nullptr_t = nullptr) noexcept {}
        IColumnDefinition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColumnDefinitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColumnDefinitionStatics>
    {
        IColumnDefinitionStatics(std::nullptr_t = nullptr) noexcept {}
        IColumnDefinitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox>
    {
        IComboBox(std::nullptr_t = nullptr) noexcept {}
        IComboBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox2>
    {
        IComboBox2(std::nullptr_t = nullptr) noexcept {}
        IComboBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox3>
    {
        IComboBox3(std::nullptr_t = nullptr) noexcept {}
        IComboBox3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox4>
    {
        IComboBox4(std::nullptr_t = nullptr) noexcept {}
        IComboBox4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox5>
    {
        IComboBox5(std::nullptr_t = nullptr) noexcept {}
        IComboBox5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBox6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBox6>
    {
        IComboBox6(std::nullptr_t = nullptr) noexcept {}
        IComboBox6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxFactory>
    {
        IComboBoxFactory(std::nullptr_t = nullptr) noexcept {}
        IComboBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItem>
    {
        IComboBoxItem(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItemFactory>
    {
        IComboBoxItemFactory(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxOverrides>
    {
        IComboBoxOverrides(std::nullptr_t = nullptr) noexcept {}
        IComboBoxOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics>
    {
        IComboBoxStatics(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics2>
    {
        IComboBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics3>
    {
        IComboBoxStatics3(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics4>
    {
        IComboBoxStatics4(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics5>
    {
        IComboBoxStatics5(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxStatics6>
    {
        IComboBoxStatics6(std::nullptr_t = nullptr) noexcept {}
        IComboBoxStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxTextSubmittedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxTextSubmittedEventArgs>
    {
        IComboBoxTextSubmittedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IComboBoxTextSubmittedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBar>
    {
        ICommandBar(std::nullptr_t = nullptr) noexcept {}
        ICommandBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBar2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBar2>
    {
        ICommandBar2(std::nullptr_t = nullptr) noexcept {}
        ICommandBar2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBar3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBar3>
    {
        ICommandBar3(std::nullptr_t = nullptr) noexcept {}
        ICommandBar3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarElement>
    {
        ICommandBarElement(std::nullptr_t = nullptr) noexcept {}
        ICommandBarElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarElement2>
    {
        ICommandBarElement2(std::nullptr_t = nullptr) noexcept {}
        ICommandBarElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFactory>
    {
        ICommandBarFactory(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyout>
    {
        ICommandBarFlyout(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutFactory>
    {
        ICommandBarFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarOverflowPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarOverflowPresenter>
    {
        ICommandBarOverflowPresenter(std::nullptr_t = nullptr) noexcept {}
        ICommandBarOverflowPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarOverflowPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarOverflowPresenterFactory>
    {
        ICommandBarOverflowPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        ICommandBarOverflowPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarStatics>
    {
        ICommandBarStatics(std::nullptr_t = nullptr) noexcept {}
        ICommandBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarStatics2>
    {
        ICommandBarStatics2(std::nullptr_t = nullptr) noexcept {}
        ICommandBarStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarStatics3>
    {
        ICommandBarStatics3(std::nullptr_t = nullptr) noexcept {}
        ICommandBarStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContainerContentChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContainerContentChangingEventArgs>
    {
        IContainerContentChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContainerContentChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentControl>
    {
        IContentControl(std::nullptr_t = nullptr) noexcept {}
        IContentControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentControl2>
    {
        IContentControl2(std::nullptr_t = nullptr) noexcept {}
        IContentControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentControlFactory>
    {
        IContentControlFactory(std::nullptr_t = nullptr) noexcept {}
        IContentControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentControlOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentControlOverrides>
    {
        IContentControlOverrides(std::nullptr_t = nullptr) noexcept {}
        IContentControlOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentControlStatics>
    {
        IContentControlStatics(std::nullptr_t = nullptr) noexcept {}
        IContentControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialog :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialog>
    {
        IContentDialog(std::nullptr_t = nullptr) noexcept {}
        IContentDialog(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialog2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialog2>
    {
        IContentDialog2(std::nullptr_t = nullptr) noexcept {}
        IContentDialog2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialog3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialog3>
    {
        IContentDialog3(std::nullptr_t = nullptr) noexcept {}
        IContentDialog3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogButtonClickDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogButtonClickDeferral>
    {
        IContentDialogButtonClickDeferral(std::nullptr_t = nullptr) noexcept {}
        IContentDialogButtonClickDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogButtonClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogButtonClickEventArgs>
    {
        IContentDialogButtonClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentDialogButtonClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogClosedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogClosedEventArgs>
    {
        IContentDialogClosedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentDialogClosedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogClosingDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogClosingDeferral>
    {
        IContentDialogClosingDeferral(std::nullptr_t = nullptr) noexcept {}
        IContentDialogClosingDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogClosingEventArgs>
    {
        IContentDialogClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentDialogClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogFactory>
    {
        IContentDialogFactory(std::nullptr_t = nullptr) noexcept {}
        IContentDialogFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogOpenedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogOpenedEventArgs>
    {
        IContentDialogOpenedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentDialogOpenedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogStatics>
    {
        IContentDialogStatics(std::nullptr_t = nullptr) noexcept {}
        IContentDialogStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentDialogStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentDialogStatics2>
    {
        IContentDialogStatics2(std::nullptr_t = nullptr) noexcept {}
        IContentDialogStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkChangedEventArgs>
    {
        IContentLinkChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentLinkChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenter>
    {
        IContentPresenter(std::nullptr_t = nullptr) noexcept {}
        IContentPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenter2>
    {
        IContentPresenter2(std::nullptr_t = nullptr) noexcept {}
        IContentPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenter3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenter3>
    {
        IContentPresenter3(std::nullptr_t = nullptr) noexcept {}
        IContentPresenter3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenter4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenter4>
    {
        IContentPresenter4(std::nullptr_t = nullptr) noexcept {}
        IContentPresenter4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenter5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenter5>
    {
        IContentPresenter5(std::nullptr_t = nullptr) noexcept {}
        IContentPresenter5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterFactory>
    {
        IContentPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterOverrides>
    {
        IContentPresenterOverrides(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterStatics>
    {
        IContentPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterStatics2>
    {
        IContentPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterStatics3>
    {
        IContentPresenterStatics3(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterStatics4>
    {
        IContentPresenterStatics4(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentPresenterStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPresenterStatics5>
    {
        IContentPresenterStatics5(std::nullptr_t = nullptr) noexcept {}
        IContentPresenterStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContextMenuEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContextMenuEventArgs>
    {
        IContextMenuEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContextMenuEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl>
    {
        IControl(std::nullptr_t = nullptr) noexcept {}
        IControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl2>
    {
        IControl2(std::nullptr_t = nullptr) noexcept {}
        IControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl3>
    {
        IControl3(std::nullptr_t = nullptr) noexcept {}
        IControl3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl4>
    {
        IControl4(std::nullptr_t = nullptr) noexcept {}
        IControl4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl5>
    {
        IControl5(std::nullptr_t = nullptr) noexcept {}
        IControl5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControl7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControl7>
    {
        IControl7(std::nullptr_t = nullptr) noexcept {}
        IControl7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlFactory>
    {
        IControlFactory(std::nullptr_t = nullptr) noexcept {}
        IControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlOverrides>
    {
        IControlOverrides(std::nullptr_t = nullptr) noexcept {}
        IControlOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlOverrides6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlOverrides6>
    {
        IControlOverrides6(std::nullptr_t = nullptr) noexcept {}
        IControlOverrides6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlProtected>
    {
        IControlProtected(std::nullptr_t = nullptr) noexcept {}
        IControlProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics>
    {
        IControlStatics(std::nullptr_t = nullptr) noexcept {}
        IControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics2>
    {
        IControlStatics2(std::nullptr_t = nullptr) noexcept {}
        IControlStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics3>
    {
        IControlStatics3(std::nullptr_t = nullptr) noexcept {}
        IControlStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics4>
    {
        IControlStatics4(std::nullptr_t = nullptr) noexcept {}
        IControlStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics5>
    {
        IControlStatics5(std::nullptr_t = nullptr) noexcept {}
        IControlStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlStatics7>
    {
        IControlStatics7(std::nullptr_t = nullptr) noexcept {}
        IControlStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IControlTemplate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IControlTemplate>
    {
        IControlTemplate(std::nullptr_t = nullptr) noexcept {}
        IControlTemplate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataTemplateSelector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateSelector>
    {
        IDataTemplateSelector(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateSelector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataTemplateSelector2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateSelector2>
    {
        IDataTemplateSelector2(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateSelector2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataTemplateSelectorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateSelectorFactory>
    {
        IDataTemplateSelectorFactory(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateSelectorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataTemplateSelectorOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateSelectorOverrides>
    {
        IDataTemplateSelectorOverrides(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateSelectorOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataTemplateSelectorOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateSelectorOverrides2>
    {
        IDataTemplateSelectorOverrides2(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateSelectorOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickedEventArgs>
    {
        IDatePickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDatePickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePicker>
    {
        IDatePicker(std::nullptr_t = nullptr) noexcept {}
        IDatePicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePicker2>
    {
        IDatePicker2(std::nullptr_t = nullptr) noexcept {}
        IDatePicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePicker3>
    {
        IDatePicker3(std::nullptr_t = nullptr) noexcept {}
        IDatePicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFactory>
    {
        IDatePickerFactory(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyout>
    {
        IDatePickerFlyout(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyout2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyout2>
    {
        IDatePickerFlyout2(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyout2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutItem>
    {
        IDatePickerFlyoutItem(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutItemStatics>
    {
        IDatePickerFlyoutItemStatics(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutPresenter>
    {
        IDatePickerFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutPresenter2>
    {
        IDatePickerFlyoutPresenter2(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutPresenterStatics2>
    {
        IDatePickerFlyoutPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutStatics>
    {
        IDatePickerFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutStatics2>
    {
        IDatePickerFlyoutStatics2(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerSelectedValueChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerSelectedValueChangedEventArgs>
    {
        IDatePickerSelectedValueChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDatePickerSelectedValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerStatics>
    {
        IDatePickerStatics(std::nullptr_t = nullptr) noexcept {}
        IDatePickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerStatics2>
    {
        IDatePickerStatics2(std::nullptr_t = nullptr) noexcept {}
        IDatePickerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerStatics3>
    {
        IDatePickerStatics3(std::nullptr_t = nullptr) noexcept {}
        IDatePickerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerValueChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerValueChangedEventArgs>
    {
        IDatePickerValueChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDatePickerValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragItemsCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragItemsCompletedEventArgs>
    {
        IDragItemsCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragItemsCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragItemsStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragItemsStartingEventArgs>
    {
        IDragItemsStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragItemsStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropDownButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButton>
    {
        IDropDownButton(std::nullptr_t = nullptr) noexcept {}
        IDropDownButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropDownButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButtonAutomationPeer>
    {
        IDropDownButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IDropDownButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropDownButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButtonAutomationPeerFactory>
    {
        IDropDownButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IDropDownButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropDownButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButtonFactory>
    {
        IDropDownButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IDropDownButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDynamicOverflowItemsChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicOverflowItemsChangingEventArgs>
    {
        IDynamicOverflowItemsChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDynamicOverflowItemsChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipView>
    {
        IFlipView(std::nullptr_t = nullptr) noexcept {}
        IFlipView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipView2>
    {
        IFlipView2(std::nullptr_t = nullptr) noexcept {}
        IFlipView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewFactory>
    {
        IFlipViewFactory(std::nullptr_t = nullptr) noexcept {}
        IFlipViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItem>
    {
        IFlipViewItem(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItemFactory>
    {
        IFlipViewItemFactory(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewStatics2>
    {
        IFlipViewStatics2(std::nullptr_t = nullptr) noexcept {}
        IFlipViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyout>
    {
        IFlyout(std::nullptr_t = nullptr) noexcept {}
        IFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutFactory>
    {
        IFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        IFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenter>
    {
        IFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenter2>
    {
        IFlyoutPresenter2(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenterFactory>
    {
        IFlyoutPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenterStatics2>
    {
        IFlyoutPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutStatics>
    {
        IFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        IFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusDisengagedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusDisengagedEventArgs>
    {
        IFocusDisengagedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IFocusDisengagedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusEngagedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusEngagedEventArgs>
    {
        IFocusEngagedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IFocusEngagedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusEngagedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusEngagedEventArgs2>
    {
        IFocusEngagedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IFocusEngagedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIcon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIcon>
    {
        IFontIcon(std::nullptr_t = nullptr) noexcept {}
        IFontIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIcon2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIcon2>
    {
        IFontIcon2(std::nullptr_t = nullptr) noexcept {}
        IFontIcon2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIcon3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIcon3>
    {
        IFontIcon3(std::nullptr_t = nullptr) noexcept {}
        IFontIcon3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconFactory>
    {
        IFontIconFactory(std::nullptr_t = nullptr) noexcept {}
        IFontIconFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconSource>
    {
        IFontIconSource(std::nullptr_t = nullptr) noexcept {}
        IFontIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconSourceFactory>
    {
        IFontIconSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IFontIconSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconSourceStatics>
    {
        IFontIconSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IFontIconSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconStatics>
    {
        IFontIconStatics(std::nullptr_t = nullptr) noexcept {}
        IFontIconStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconStatics2>
    {
        IFontIconStatics2(std::nullptr_t = nullptr) noexcept {}
        IFontIconStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontIconStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontIconStatics3>
    {
        IFontIconStatics3(std::nullptr_t = nullptr) noexcept {}
        IFontIconStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrame>
    {
        IFrame(std::nullptr_t = nullptr) noexcept {}
        IFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrame2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrame2>
    {
        IFrame2(std::nullptr_t = nullptr) noexcept {}
        IFrame2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrame3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrame3>
    {
        IFrame3(std::nullptr_t = nullptr) noexcept {}
        IFrame3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrame4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrame4>
    {
        IFrame4(std::nullptr_t = nullptr) noexcept {}
        IFrame4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrame5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrame5>
    {
        IFrame5(std::nullptr_t = nullptr) noexcept {}
        IFrame5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameFactory>
    {
        IFrameFactory(std::nullptr_t = nullptr) noexcept {}
        IFrameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameStatics>
    {
        IFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameStatics2>
    {
        IFrameStatics2(std::nullptr_t = nullptr) noexcept {}
        IFrameStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameStatics5>
    {
        IFrameStatics5(std::nullptr_t = nullptr) noexcept {}
        IFrameStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGrid :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGrid>
    {
        IGrid(std::nullptr_t = nullptr) noexcept {}
        IGrid(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGrid2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGrid2>
    {
        IGrid2(std::nullptr_t = nullptr) noexcept {}
        IGrid2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGrid3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGrid3>
    {
        IGrid3(std::nullptr_t = nullptr) noexcept {}
        IGrid3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGrid4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGrid4>
    {
        IGrid4(std::nullptr_t = nullptr) noexcept {}
        IGrid4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridFactory>
    {
        IGridFactory(std::nullptr_t = nullptr) noexcept {}
        IGridFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridStatics>
    {
        IGridStatics(std::nullptr_t = nullptr) noexcept {}
        IGridStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridStatics2>
    {
        IGridStatics2(std::nullptr_t = nullptr) noexcept {}
        IGridStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridStatics3>
    {
        IGridStatics3(std::nullptr_t = nullptr) noexcept {}
        IGridStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridStatics4>
    {
        IGridStatics4(std::nullptr_t = nullptr) noexcept {}
        IGridStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridView>
    {
        IGridView(std::nullptr_t = nullptr) noexcept {}
        IGridView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewFactory>
    {
        IGridViewFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewHeaderItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewHeaderItem>
    {
        IGridViewHeaderItem(std::nullptr_t = nullptr) noexcept {}
        IGridViewHeaderItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewHeaderItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewHeaderItemFactory>
    {
        IGridViewHeaderItemFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewHeaderItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItem>
    {
        IGridViewItem(std::nullptr_t = nullptr) noexcept {}
        IGridViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemFactory>
    {
        IGridViewItemFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupItem>
    {
        IGroupItem(std::nullptr_t = nullptr) noexcept {}
        IGroupItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupItemFactory>
    {
        IGroupItemFactory(std::nullptr_t = nullptr) noexcept {}
        IGroupItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyle :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyle>
    {
        IGroupStyle(std::nullptr_t = nullptr) noexcept {}
        IGroupStyle(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyle2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyle2>
    {
        IGroupStyle2(std::nullptr_t = nullptr) noexcept {}
        IGroupStyle2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyleFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyleFactory>
    {
        IGroupStyleFactory(std::nullptr_t = nullptr) noexcept {}
        IGroupStyleFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyleSelector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyleSelector>
    {
        IGroupStyleSelector(std::nullptr_t = nullptr) noexcept {}
        IGroupStyleSelector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyleSelectorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyleSelectorFactory>
    {
        IGroupStyleSelectorFactory(std::nullptr_t = nullptr) noexcept {}
        IGroupStyleSelectorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupStyleSelectorOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupStyleSelectorOverrides>
    {
        IGroupStyleSelectorOverrides(std::nullptr_t = nullptr) noexcept {}
        IGroupStyleSelectorOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHandwritingPanelClosedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHandwritingPanelClosedEventArgs>
    {
        IHandwritingPanelClosedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHandwritingPanelClosedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHandwritingPanelOpenedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHandwritingPanelOpenedEventArgs>
    {
        IHandwritingPanelOpenedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHandwritingPanelOpenedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHandwritingView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHandwritingView>
    {
        IHandwritingView(std::nullptr_t = nullptr) noexcept {}
        IHandwritingView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHandwritingViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHandwritingViewFactory>
    {
        IHandwritingViewFactory(std::nullptr_t = nullptr) noexcept {}
        IHandwritingViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHandwritingViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHandwritingViewStatics>
    {
        IHandwritingViewStatics(std::nullptr_t = nullptr) noexcept {}
        IHandwritingViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHub :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHub>
    {
        IHub(std::nullptr_t = nullptr) noexcept {}
        IHub(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubFactory>
    {
        IHubFactory(std::nullptr_t = nullptr) noexcept {}
        IHubFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSection>
    {
        IHubSection(std::nullptr_t = nullptr) noexcept {}
        IHubSection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSectionFactory>
    {
        IHubSectionFactory(std::nullptr_t = nullptr) noexcept {}
        IHubSectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSectionHeaderClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSectionHeaderClickEventArgs>
    {
        IHubSectionHeaderClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHubSectionHeaderClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSectionStatics>
    {
        IHubSectionStatics(std::nullptr_t = nullptr) noexcept {}
        IHubSectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubStatics>
    {
        IHubStatics(std::nullptr_t = nullptr) noexcept {}
        IHubStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkButton>
    {
        IHyperlinkButton(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkButtonFactory>
    {
        IHyperlinkButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkButtonStatics>
    {
        IHyperlinkButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconElement>
    {
        IIconElement(std::nullptr_t = nullptr) noexcept {}
        IIconElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconElementFactory>
    {
        IIconElementFactory(std::nullptr_t = nullptr) noexcept {}
        IIconElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconElementStatics>
    {
        IIconElementStatics(std::nullptr_t = nullptr) noexcept {}
        IIconElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSource>
    {
        IIconSource(std::nullptr_t = nullptr) noexcept {}
        IIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSourceElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSourceElement>
    {
        IIconSourceElement(std::nullptr_t = nullptr) noexcept {}
        IIconSourceElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSourceElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSourceElementFactory>
    {
        IIconSourceElementFactory(std::nullptr_t = nullptr) noexcept {}
        IIconSourceElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSourceElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSourceElementStatics>
    {
        IIconSourceElementStatics(std::nullptr_t = nullptr) noexcept {}
        IIconSourceElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSourceFactory>
    {
        IIconSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IIconSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIconSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIconSourceStatics>
    {
        IIconSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IIconSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImage>
    {
        IImage(std::nullptr_t = nullptr) noexcept {}
        IImage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImage2>
    {
        IImage2(std::nullptr_t = nullptr) noexcept {}
        IImage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImage3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImage3>
    {
        IImage3(std::nullptr_t = nullptr) noexcept {}
        IImage3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageStatics>
    {
        IImageStatics(std::nullptr_t = nullptr) noexcept {}
        IImageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkCanvas :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkCanvas>
    {
        IInkCanvas(std::nullptr_t = nullptr) noexcept {}
        IInkCanvas(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkCanvasFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkCanvasFactory>
    {
        IInkCanvasFactory(std::nullptr_t = nullptr) noexcept {}
        IInkCanvasFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbar>
    {
        IInkToolbar(std::nullptr_t = nullptr) noexcept {}
        IInkToolbar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbar2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbar2>
    {
        IInkToolbar2(std::nullptr_t = nullptr) noexcept {}
        IInkToolbar2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbar3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbar3>
    {
        IInkToolbar3(std::nullptr_t = nullptr) noexcept {}
        IInkToolbar3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarBallpointPenButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarBallpointPenButton>
    {
        IInkToolbarBallpointPenButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarBallpointPenButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarBallpointPenButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarBallpointPenButtonFactory>
    {
        IInkToolbarBallpointPenButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarBallpointPenButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPen :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPen>
    {
        IInkToolbarCustomPen(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPen(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPenButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPenButton>
    {
        IInkToolbarCustomPenButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPenButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPenButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPenButtonFactory>
    {
        IInkToolbarCustomPenButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPenButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPenButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPenButtonStatics>
    {
        IInkToolbarCustomPenButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPenButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPenFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPenFactory>
    {
        IInkToolbarCustomPenFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPenFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomPenOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomPenOverrides>
    {
        IInkToolbarCustomPenOverrides(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomPenOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomToggleButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomToggleButton>
    {
        IInkToolbarCustomToggleButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomToggleButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomToggleButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomToggleButtonFactory>
    {
        IInkToolbarCustomToggleButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomToggleButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomToolButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomToolButton>
    {
        IInkToolbarCustomToolButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomToolButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomToolButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomToolButtonFactory>
    {
        IInkToolbarCustomToolButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomToolButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarCustomToolButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarCustomToolButtonStatics>
    {
        IInkToolbarCustomToolButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarCustomToolButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarEraserButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarEraserButton>
    {
        IInkToolbarEraserButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarEraserButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarEraserButton2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarEraserButton2>
    {
        IInkToolbarEraserButton2(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarEraserButton2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarEraserButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarEraserButtonFactory>
    {
        IInkToolbarEraserButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarEraserButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarEraserButtonStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarEraserButtonStatics2>
    {
        IInkToolbarEraserButtonStatics2(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarEraserButtonStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarFactory>
    {
        IInkToolbarFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarFlyoutItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarFlyoutItem>
    {
        IInkToolbarFlyoutItem(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarFlyoutItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarFlyoutItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarFlyoutItemFactory>
    {
        IInkToolbarFlyoutItemFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarFlyoutItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarFlyoutItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarFlyoutItemStatics>
    {
        IInkToolbarFlyoutItemStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarFlyoutItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarHighlighterButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarHighlighterButton>
    {
        IInkToolbarHighlighterButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarHighlighterButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarHighlighterButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarHighlighterButtonFactory>
    {
        IInkToolbarHighlighterButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarHighlighterButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarIsStencilButtonCheckedChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarIsStencilButtonCheckedChangedEventArgs>
    {
        IInkToolbarIsStencilButtonCheckedChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarIsStencilButtonCheckedChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarMenuButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarMenuButton>
    {
        IInkToolbarMenuButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarMenuButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarMenuButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarMenuButtonFactory>
    {
        IInkToolbarMenuButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarMenuButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarMenuButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarMenuButtonStatics>
    {
        IInkToolbarMenuButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarMenuButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenButton>
    {
        IInkToolbarPenButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenButtonFactory>
    {
        IInkToolbarPenButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenButtonStatics>
    {
        IInkToolbarPenButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenConfigurationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenConfigurationControl>
    {
        IInkToolbarPenConfigurationControl(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenConfigurationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenConfigurationControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenConfigurationControlFactory>
    {
        IInkToolbarPenConfigurationControlFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenConfigurationControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPenConfigurationControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPenConfigurationControlStatics>
    {
        IInkToolbarPenConfigurationControlStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPenConfigurationControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPencilButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPencilButton>
    {
        IInkToolbarPencilButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPencilButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarPencilButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarPencilButtonFactory>
    {
        IInkToolbarPencilButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarPencilButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarRulerButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarRulerButton>
    {
        IInkToolbarRulerButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarRulerButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarRulerButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarRulerButtonFactory>
    {
        IInkToolbarRulerButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarRulerButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarRulerButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarRulerButtonStatics>
    {
        IInkToolbarRulerButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarRulerButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStatics>
    {
        IInkToolbarStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStatics2>
    {
        IInkToolbarStatics2(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStatics3>
    {
        IInkToolbarStatics3(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStencilButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStencilButton>
    {
        IInkToolbarStencilButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStencilButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStencilButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStencilButtonFactory>
    {
        IInkToolbarStencilButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStencilButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarStencilButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarStencilButtonStatics>
    {
        IInkToolbarStencilButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarStencilButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarToggleButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarToggleButton>
    {
        IInkToolbarToggleButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarToggleButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarToggleButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarToggleButtonFactory>
    {
        IInkToolbarToggleButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarToggleButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarToolButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarToolButton>
    {
        IInkToolbarToolButton(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarToolButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarToolButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarToolButtonFactory>
    {
        IInkToolbarToolButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarToolButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarToolButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarToolButtonStatics>
    {
        IInkToolbarToolButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarToolButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInsertionPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInsertionPanel>
    {
        IInsertionPanel(std::nullptr_t = nullptr) noexcept {}
        IInsertionPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsTextTrimmedChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsTextTrimmedChangedEventArgs>
    {
        IIsTextTrimmedChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IIsTextTrimmedChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemClickEventArgs>
    {
        IItemClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IItemClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemContainerGenerator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemContainerGenerator>
    {
        IItemContainerGenerator(std::nullptr_t = nullptr) noexcept {}
        IItemContainerGenerator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemContainerMapping :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemContainerMapping>
    {
        IItemContainerMapping(std::nullptr_t = nullptr) noexcept {}
        IItemContainerMapping(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControl>
    {
        IItemsControl(std::nullptr_t = nullptr) noexcept {}
        IItemsControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControl2>
    {
        IItemsControl2(std::nullptr_t = nullptr) noexcept {}
        IItemsControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControl3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControl3>
    {
        IItemsControl3(std::nullptr_t = nullptr) noexcept {}
        IItemsControl3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlFactory>
    {
        IItemsControlFactory(std::nullptr_t = nullptr) noexcept {}
        IItemsControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlOverrides>
    {
        IItemsControlOverrides(std::nullptr_t = nullptr) noexcept {}
        IItemsControlOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlStatics>
    {
        IItemsControlStatics(std::nullptr_t = nullptr) noexcept {}
        IItemsControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPanelTemplate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPanelTemplate>
    {
        IItemsPanelTemplate(std::nullptr_t = nullptr) noexcept {}
        IItemsPanelTemplate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPickedEventArgs>
    {
        IItemsPickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IItemsPickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPresenter>
    {
        IItemsPresenter(std::nullptr_t = nullptr) noexcept {}
        IItemsPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPresenter2>
    {
        IItemsPresenter2(std::nullptr_t = nullptr) noexcept {}
        IItemsPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPresenterStatics>
    {
        IItemsPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IItemsPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsPresenterStatics2>
    {
        IItemsPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IItemsPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsStackPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsStackPanel>
    {
        IItemsStackPanel(std::nullptr_t = nullptr) noexcept {}
        IItemsStackPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsStackPanel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsStackPanel2>
    {
        IItemsStackPanel2(std::nullptr_t = nullptr) noexcept {}
        IItemsStackPanel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsStackPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsStackPanelStatics>
    {
        IItemsStackPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IItemsStackPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsStackPanelStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsStackPanelStatics2>
    {
        IItemsStackPanelStatics2(std::nullptr_t = nullptr) noexcept {}
        IItemsStackPanelStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsWrapGrid :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsWrapGrid>
    {
        IItemsWrapGrid(std::nullptr_t = nullptr) noexcept {}
        IItemsWrapGrid(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsWrapGrid2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsWrapGrid2>
    {
        IItemsWrapGrid2(std::nullptr_t = nullptr) noexcept {}
        IItemsWrapGrid2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsWrapGridStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsWrapGridStatics>
    {
        IItemsWrapGridStatics(std::nullptr_t = nullptr) noexcept {}
        IItemsWrapGridStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsWrapGridStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsWrapGridStatics2>
    {
        IItemsWrapGridStatics2(std::nullptr_t = nullptr) noexcept {}
        IItemsWrapGridStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBox>
    {
        IListBox(std::nullptr_t = nullptr) noexcept {}
        IListBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBox2>
    {
        IListBox2(std::nullptr_t = nullptr) noexcept {}
        IListBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxFactory>
    {
        IListBoxFactory(std::nullptr_t = nullptr) noexcept {}
        IListBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItem>
    {
        IListBoxItem(std::nullptr_t = nullptr) noexcept {}
        IListBoxItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItemFactory>
    {
        IListBoxItemFactory(std::nullptr_t = nullptr) noexcept {}
        IListBoxItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxStatics>
    {
        IListBoxStatics(std::nullptr_t = nullptr) noexcept {}
        IListBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxStatics2>
    {
        IListBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        IListBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListPickerFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListPickerFlyout>
    {
        IListPickerFlyout(std::nullptr_t = nullptr) noexcept {}
        IListPickerFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListPickerFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListPickerFlyoutPresenter>
    {
        IListPickerFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        IListPickerFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListPickerFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListPickerFlyoutStatics>
    {
        IListPickerFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        IListPickerFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListView>
    {
        IListView(std::nullptr_t = nullptr) noexcept {}
        IListView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase>
    {
        IListViewBase(std::nullptr_t = nullptr) noexcept {}
        IListViewBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase2>
    {
        IListViewBase2(std::nullptr_t = nullptr) noexcept {}
        IListViewBase2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase3>
    {
        IListViewBase3(std::nullptr_t = nullptr) noexcept {}
        IListViewBase3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase4>
    {
        IListViewBase4(std::nullptr_t = nullptr) noexcept {}
        IListViewBase4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase5>
    {
        IListViewBase5(std::nullptr_t = nullptr) noexcept {}
        IListViewBase5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBase6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBase6>
    {
        IListViewBase6(std::nullptr_t = nullptr) noexcept {}
        IListViewBase6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseFactory>
    {
        IListViewBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseHeaderItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseHeaderItem>
    {
        IListViewBaseHeaderItem(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseHeaderItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseHeaderItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseHeaderItemFactory>
    {
        IListViewBaseHeaderItemFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseHeaderItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseStatics>
    {
        IListViewBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseStatics2>
    {
        IListViewBaseStatics2(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseStatics3>
    {
        IListViewBaseStatics3(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseStatics4>
    {
        IListViewBaseStatics4(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseStatics5>
    {
        IListViewBaseStatics5(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewFactory>
    {
        IListViewFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewHeaderItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewHeaderItem>
    {
        IListViewHeaderItem(std::nullptr_t = nullptr) noexcept {}
        IListViewHeaderItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewHeaderItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewHeaderItemFactory>
    {
        IListViewHeaderItemFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewHeaderItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItem>
    {
        IListViewItem(std::nullptr_t = nullptr) noexcept {}
        IListViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemFactory>
    {
        IListViewItemFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewPersistenceHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewPersistenceHelper>
    {
        IListViewPersistenceHelper(std::nullptr_t = nullptr) noexcept {}
        IListViewPersistenceHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewPersistenceHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewPersistenceHelperStatics>
    {
        IListViewPersistenceHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IListViewPersistenceHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElement>
    {
        IMediaElement(std::nullptr_t = nullptr) noexcept {}
        IMediaElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElement2>
    {
        IMediaElement2(std::nullptr_t = nullptr) noexcept {}
        IMediaElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElement3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElement3>
    {
        IMediaElement3(std::nullptr_t = nullptr) noexcept {}
        IMediaElement3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElementStatics>
    {
        IMediaElementStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElementStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElementStatics2>
    {
        IMediaElementStatics2(std::nullptr_t = nullptr) noexcept {}
        IMediaElementStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerElement>
    {
        IMediaPlayerElement(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerElementFactory>
    {
        IMediaPlayerElementFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerElementStatics>
    {
        IMediaPlayerElementStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerPresenter>
    {
        IMediaPlayerPresenter(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerPresenterFactory>
    {
        IMediaPlayerPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerPresenterStatics>
    {
        IMediaPlayerPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControls :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControls>
    {
        IMediaTransportControls(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControls(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControls2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControls2>
    {
        IMediaTransportControls2(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControls2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControls3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControls3>
    {
        IMediaTransportControls3(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControls3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControls4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControls4>
    {
        IMediaTransportControls4(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControls4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsFactory>
    {
        IMediaTransportControlsFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsHelper>
    {
        IMediaTransportControlsHelper(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsHelperStatics>
    {
        IMediaTransportControlsHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsStatics>
    {
        IMediaTransportControlsStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsStatics2>
    {
        IMediaTransportControlsStatics2(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsStatics3>
    {
        IMediaTransportControlsStatics3(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsStatics4>
    {
        IMediaTransportControlsStatics4(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBar>
    {
        IMenuBar(std::nullptr_t = nullptr) noexcept {}
        IMenuBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarFactory>
    {
        IMenuBarFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItem>
    {
        IMenuBarItem(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemFactory>
    {
        IMenuBarItemFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemFlyout>
    {
        IMenuBarItemFlyout(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemFlyoutFactory>
    {
        IMenuBarItemFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemStatics>
    {
        IMenuBarItemStatics(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarStatics>
    {
        IMenuBarStatics(std::nullptr_t = nullptr) noexcept {}
        IMenuBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyout>
    {
        IMenuFlyout(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyout2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyout2>
    {
        IMenuFlyout2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyout2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutFactory>
    {
        IMenuFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItem>
    {
        IMenuFlyoutItem(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItem2>
    {
        IMenuFlyoutItem2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItem3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItem3>
    {
        IMenuFlyoutItem3(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItem3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemBase>
    {
        IMenuFlyoutItemBase(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemBaseFactory>
    {
        IMenuFlyoutItemBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemFactory>
    {
        IMenuFlyoutItemFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemStatics>
    {
        IMenuFlyoutItemStatics(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemStatics2>
    {
        IMenuFlyoutItemStatics2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemStatics3>
    {
        IMenuFlyoutItemStatics3(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenter>
    {
        IMenuFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenter2>
    {
        IMenuFlyoutPresenter2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenter3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenter3>
    {
        IMenuFlyoutPresenter3(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenter3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenterFactory>
    {
        IMenuFlyoutPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenterStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenterStatics3>
    {
        IMenuFlyoutPresenterStatics3(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenterStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSeparator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSeparator>
    {
        IMenuFlyoutSeparator(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSeparator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSeparatorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSeparatorFactory>
    {
        IMenuFlyoutSeparatorFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSeparatorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutStatics>
    {
        IMenuFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSubItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSubItem>
    {
        IMenuFlyoutSubItem(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSubItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSubItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSubItem2>
    {
        IMenuFlyoutSubItem2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSubItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSubItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSubItemStatics>
    {
        IMenuFlyoutSubItemStatics(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSubItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutSubItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutSubItemStatics2>
    {
        IMenuFlyoutSubItemStatics2(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutSubItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigate>
    {
        INavigate(std::nullptr_t = nullptr) noexcept {}
        INavigate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationView>
    {
        INavigationView(std::nullptr_t = nullptr) noexcept {}
        INavigationView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationView2>
    {
        INavigationView2(std::nullptr_t = nullptr) noexcept {}
        INavigationView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationView3>
    {
        INavigationView3(std::nullptr_t = nullptr) noexcept {}
        INavigationView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewBackRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewBackRequestedEventArgs>
    {
        INavigationViewBackRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationViewBackRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewDisplayModeChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewDisplayModeChangedEventArgs>
    {
        INavigationViewDisplayModeChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationViewDisplayModeChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewFactory>
    {
        INavigationViewFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItem>
    {
        INavigationViewItem(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItem2>
    {
        INavigationViewItem2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemBase>
    {
        INavigationViewItemBase(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemBaseFactory>
    {
        INavigationViewItemBaseFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemFactory>
    {
        INavigationViewItemFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemHeader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemHeader>
    {
        INavigationViewItemHeader(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemHeader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemHeaderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemHeaderFactory>
    {
        INavigationViewItemHeaderFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemHeaderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemInvokedEventArgs>
    {
        INavigationViewItemInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemInvokedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemInvokedEventArgs2>
    {
        INavigationViewItemInvokedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemInvokedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemSeparator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemSeparator>
    {
        INavigationViewItemSeparator(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemSeparator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemSeparatorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemSeparatorFactory>
    {
        INavigationViewItemSeparatorFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemSeparatorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemStatics>
    {
        INavigationViewItemStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemStatics2>
    {
        INavigationViewItemStatics2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewList :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewList>
    {
        INavigationViewList(std::nullptr_t = nullptr) noexcept {}
        INavigationViewList(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewListFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewListFactory>
    {
        INavigationViewListFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewListFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewPaneClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewPaneClosingEventArgs>
    {
        INavigationViewPaneClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationViewPaneClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewSelectionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewSelectionChangedEventArgs>
    {
        INavigationViewSelectionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationViewSelectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewSelectionChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewSelectionChangedEventArgs2>
    {
        INavigationViewSelectionChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewSelectionChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewStatics>
    {
        INavigationViewStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewStatics2>
    {
        INavigationViewStatics2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewStatics3>
    {
        INavigationViewStatics3(std::nullptr_t = nullptr) noexcept {}
        INavigationViewStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewTemplateSettings>
    {
        INavigationViewTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        INavigationViewTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewTemplateSettingsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewTemplateSettingsFactory>
    {
        INavigationViewTemplateSettingsFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewTemplateSettingsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewTemplateSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewTemplateSettingsStatics>
    {
        INavigationViewTemplateSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewTemplateSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotifyEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotifyEventArgs>
    {
        INotifyEventArgs(std::nullptr_t = nullptr) noexcept {}
        INotifyEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotifyEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotifyEventArgs2>
    {
        INotifyEventArgs2(std::nullptr_t = nullptr) noexcept {}
        INotifyEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPage>
    {
        IPage(std::nullptr_t = nullptr) noexcept {}
        IPage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageFactory>
    {
        IPageFactory(std::nullptr_t = nullptr) noexcept {}
        IPageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPageOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageOverrides>
    {
        IPageOverrides(std::nullptr_t = nullptr) noexcept {}
        IPageOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageStatics>
    {
        IPageStatics(std::nullptr_t = nullptr) noexcept {}
        IPageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanel>
    {
        IPanel(std::nullptr_t = nullptr) noexcept {}
        IPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPanel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanel2>
    {
        IPanel2(std::nullptr_t = nullptr) noexcept {}
        IPanel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanelFactory>
    {
        IPanelFactory(std::nullptr_t = nullptr) noexcept {}
        IPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanelStatics>
    {
        IPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IParallaxView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IParallaxView>
    {
        IParallaxView(std::nullptr_t = nullptr) noexcept {}
        IParallaxView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IParallaxViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IParallaxViewFactory>
    {
        IParallaxViewFactory(std::nullptr_t = nullptr) noexcept {}
        IParallaxViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IParallaxViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IParallaxViewStatics>
    {
        IParallaxViewStatics(std::nullptr_t = nullptr) noexcept {}
        IParallaxViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBox>
    {
        IPasswordBox(std::nullptr_t = nullptr) noexcept {}
        IPasswordBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBox2>
    {
        IPasswordBox2(std::nullptr_t = nullptr) noexcept {}
        IPasswordBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBox3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBox3>
    {
        IPasswordBox3(std::nullptr_t = nullptr) noexcept {}
        IPasswordBox3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBox4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBox4>
    {
        IPasswordBox4(std::nullptr_t = nullptr) noexcept {}
        IPasswordBox4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBox5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBox5>
    {
        IPasswordBox5(std::nullptr_t = nullptr) noexcept {}
        IPasswordBox5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxPasswordChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxPasswordChangingEventArgs>
    {
        IPasswordBoxPasswordChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxPasswordChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxStatics>
    {
        IPasswordBoxStatics(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxStatics2>
    {
        IPasswordBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxStatics3>
    {
        IPasswordBoxStatics3(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxStatics5>
    {
        IPasswordBoxStatics5(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIcon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIcon>
    {
        IPathIcon(std::nullptr_t = nullptr) noexcept {}
        IPathIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIconFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIconFactory>
    {
        IPathIconFactory(std::nullptr_t = nullptr) noexcept {}
        IPathIconFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIconSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIconSource>
    {
        IPathIconSource(std::nullptr_t = nullptr) noexcept {}
        IPathIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIconSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIconSourceFactory>
    {
        IPathIconSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IPathIconSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIconSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIconSourceStatics>
    {
        IPathIconSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IPathIconSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIconStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIconStatics>
    {
        IPathIconStatics(std::nullptr_t = nullptr) noexcept {}
        IPathIconStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersonPicture :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPicture>
    {
        IPersonPicture(std::nullptr_t = nullptr) noexcept {}
        IPersonPicture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersonPictureFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureFactory>
    {
        IPersonPictureFactory(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersonPictureStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureStatics>
    {
        IPersonPictureStatics(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerConfirmedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerConfirmedEventArgs>
    {
        IPickerConfirmedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPickerConfirmedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyout>
    {
        IPickerFlyout(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutPresenter>
    {
        IPickerFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutStatics>
    {
        IPickerFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivot :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivot>
    {
        IPivot(std::nullptr_t = nullptr) noexcept {}
        IPivot(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivot2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivot2>
    {
        IPivot2(std::nullptr_t = nullptr) noexcept {}
        IPivot2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivot3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivot3>
    {
        IPivot3(std::nullptr_t = nullptr) noexcept {}
        IPivot3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotFactory>
    {
        IPivotFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItem>
    {
        IPivotItem(std::nullptr_t = nullptr) noexcept {}
        IPivotItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemEventArgs>
    {
        IPivotItemEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPivotItemEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemFactory>
    {
        IPivotItemFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemStatics>
    {
        IPivotItemStatics(std::nullptr_t = nullptr) noexcept {}
        IPivotItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotStatics>
    {
        IPivotStatics(std::nullptr_t = nullptr) noexcept {}
        IPivotStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotStatics2>
    {
        IPivotStatics2(std::nullptr_t = nullptr) noexcept {}
        IPivotStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotStatics3>
    {
        IPivotStatics3(std::nullptr_t = nullptr) noexcept {}
        IPivotStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBar>
    {
        IProgressBar(std::nullptr_t = nullptr) noexcept {}
        IProgressBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarFactory>
    {
        IProgressBarFactory(std::nullptr_t = nullptr) noexcept {}
        IProgressBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarStatics>
    {
        IProgressBarStatics(std::nullptr_t = nullptr) noexcept {}
        IProgressBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressRing :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRing>
    {
        IProgressRing(std::nullptr_t = nullptr) noexcept {}
        IProgressRing(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressRingStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingStatics>
    {
        IProgressRingStatics(std::nullptr_t = nullptr) noexcept {}
        IProgressRingStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRadioButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButton>
    {
        IRadioButton(std::nullptr_t = nullptr) noexcept {}
        IRadioButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRadioButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonFactory>
    {
        IRadioButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRadioButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonStatics>
    {
        IRadioButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControl>
    {
        IRatingControl(std::nullptr_t = nullptr) noexcept {}
        IRatingControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlFactory>
    {
        IRatingControlFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlStatics>
    {
        IRatingControlStatics(std::nullptr_t = nullptr) noexcept {}
        IRatingControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemFontInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemFontInfo>
    {
        IRatingItemFontInfo(std::nullptr_t = nullptr) noexcept {}
        IRatingItemFontInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemFontInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemFontInfoFactory>
    {
        IRatingItemFontInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingItemFontInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemFontInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemFontInfoStatics>
    {
        IRatingItemFontInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IRatingItemFontInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemImageInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemImageInfo>
    {
        IRatingItemImageInfo(std::nullptr_t = nullptr) noexcept {}
        IRatingItemImageInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemImageInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemImageInfoFactory>
    {
        IRatingItemImageInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingItemImageInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemImageInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemImageInfoStatics>
    {
        IRatingItemImageInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IRatingItemImageInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemInfo>
    {
        IRatingItemInfo(std::nullptr_t = nullptr) noexcept {}
        IRatingItemInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingItemInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingItemInfoFactory>
    {
        IRatingItemInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingItemInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshContainer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshContainer>
    {
        IRefreshContainer(std::nullptr_t = nullptr) noexcept {}
        IRefreshContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshContainerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshContainerFactory>
    {
        IRefreshContainerFactory(std::nullptr_t = nullptr) noexcept {}
        IRefreshContainerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshContainerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshContainerStatics>
    {
        IRefreshContainerStatics(std::nullptr_t = nullptr) noexcept {}
        IRefreshContainerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshInteractionRatioChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshInteractionRatioChangedEventArgs>
    {
        IRefreshInteractionRatioChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRefreshInteractionRatioChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshRequestedEventArgs>
    {
        IRefreshRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRefreshRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshStateChangedEventArgs>
    {
        IRefreshStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRefreshStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshVisualizer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshVisualizer>
    {
        IRefreshVisualizer(std::nullptr_t = nullptr) noexcept {}
        IRefreshVisualizer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshVisualizerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshVisualizerFactory>
    {
        IRefreshVisualizerFactory(std::nullptr_t = nullptr) noexcept {}
        IRefreshVisualizerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRefreshVisualizerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRefreshVisualizerStatics>
    {
        IRefreshVisualizerStatics(std::nullptr_t = nullptr) noexcept {}
        IRefreshVisualizerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRelativePanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRelativePanel>
    {
        IRelativePanel(std::nullptr_t = nullptr) noexcept {}
        IRelativePanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRelativePanel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRelativePanel2>
    {
        IRelativePanel2(std::nullptr_t = nullptr) noexcept {}
        IRelativePanel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRelativePanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRelativePanelFactory>
    {
        IRelativePanelFactory(std::nullptr_t = nullptr) noexcept {}
        IRelativePanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRelativePanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRelativePanelStatics>
    {
        IRelativePanelStatics(std::nullptr_t = nullptr) noexcept {}
        IRelativePanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRelativePanelStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRelativePanelStatics2>
    {
        IRelativePanelStatics2(std::nullptr_t = nullptr) noexcept {}
        IRelativePanelStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox>
    {
        IRichEditBox(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox2>
    {
        IRichEditBox2(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox3>
    {
        IRichEditBox3(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox4>
    {
        IRichEditBox4(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox5>
    {
        IRichEditBox5(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox6>
    {
        IRichEditBox6(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox7>
    {
        IRichEditBox7(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBox8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBox8>
    {
        IRichEditBox8(std::nullptr_t = nullptr) noexcept {}
        IRichEditBox8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxFactory>
    {
        IRichEditBoxFactory(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxSelectionChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxSelectionChangingEventArgs>
    {
        IRichEditBoxSelectionChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxSelectionChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics>
    {
        IRichEditBoxStatics(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics2>
    {
        IRichEditBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics3>
    {
        IRichEditBoxStatics3(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics4>
    {
        IRichEditBoxStatics4(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics5>
    {
        IRichEditBoxStatics5(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics6>
    {
        IRichEditBoxStatics6(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics7>
    {
        IRichEditBoxStatics7(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxStatics8>
    {
        IRichEditBoxStatics8(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxTextChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxTextChangingEventArgs>
    {
        IRichEditBoxTextChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxTextChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxTextChangingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxTextChangingEventArgs2>
    {
        IRichEditBoxTextChangingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxTextChangingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock>
    {
        IRichTextBlock(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock2>
    {
        IRichTextBlock2(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock3>
    {
        IRichTextBlock3(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock4>
    {
        IRichTextBlock4(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock5>
    {
        IRichTextBlock5(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlock6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlock6>
    {
        IRichTextBlock6(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlock6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflow>
    {
        IRichTextBlockOverflow(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflow2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflow2>
    {
        IRichTextBlockOverflow2(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflow2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflow3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflow3>
    {
        IRichTextBlockOverflow3(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflow3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflowStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflowStatics>
    {
        IRichTextBlockOverflowStatics(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflowStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflowStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflowStatics2>
    {
        IRichTextBlockOverflowStatics2(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflowStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflowStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflowStatics3>
    {
        IRichTextBlockOverflowStatics3(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflowStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics>
    {
        IRichTextBlockStatics(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics2>
    {
        IRichTextBlockStatics2(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics3>
    {
        IRichTextBlockStatics3(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics4>
    {
        IRichTextBlockStatics4(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics5>
    {
        IRichTextBlockStatics5(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockStatics6>
    {
        IRichTextBlockStatics6(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRowDefinition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRowDefinition>
    {
        IRowDefinition(std::nullptr_t = nullptr) noexcept {}
        IRowDefinition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRowDefinitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRowDefinitionStatics>
    {
        IRowDefinitionStatics(std::nullptr_t = nullptr) noexcept {}
        IRowDefinitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollAnchorProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollAnchorProvider>
    {
        IScrollAnchorProvider(std::nullptr_t = nullptr) noexcept {}
        IScrollAnchorProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollContentPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollContentPresenter>
    {
        IScrollContentPresenter(std::nullptr_t = nullptr) noexcept {}
        IScrollContentPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollContentPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollContentPresenter2>
    {
        IScrollContentPresenter2(std::nullptr_t = nullptr) noexcept {}
        IScrollContentPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollContentPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollContentPresenterStatics2>
    {
        IScrollContentPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IScrollContentPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewer>
    {
        IScrollViewer(std::nullptr_t = nullptr) noexcept {}
        IScrollViewer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewer2>
    {
        IScrollViewer2(std::nullptr_t = nullptr) noexcept {}
        IScrollViewer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewer3>
    {
        IScrollViewer3(std::nullptr_t = nullptr) noexcept {}
        IScrollViewer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewer4>
    {
        IScrollViewer4(std::nullptr_t = nullptr) noexcept {}
        IScrollViewer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerStatics>
    {
        IScrollViewerStatics(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerStatics2>
    {
        IScrollViewerStatics2(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerStatics4>
    {
        IScrollViewerStatics4(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerView>
    {
        IScrollViewerView(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerViewChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerViewChangedEventArgs>
    {
        IScrollViewerViewChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerViewChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerViewChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerViewChangingEventArgs>
    {
        IScrollViewerViewChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerViewChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBox>
    {
        ISearchBox(std::nullptr_t = nullptr) noexcept {}
        ISearchBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxFactory>
    {
        ISearchBoxFactory(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxQueryChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxQueryChangedEventArgs>
    {
        ISearchBoxQueryChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxQueryChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxQuerySubmittedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxQuerySubmittedEventArgs>
    {
        ISearchBoxQuerySubmittedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxQuerySubmittedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxResultSuggestionChosenEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxResultSuggestionChosenEventArgs>
    {
        ISearchBoxResultSuggestionChosenEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxResultSuggestionChosenEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxStatics>
    {
        ISearchBoxStatics(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxSuggestionsRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxSuggestionsRequestedEventArgs>
    {
        ISearchBoxSuggestionsRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxSuggestionsRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISectionsInViewChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISectionsInViewChangedEventArgs>
    {
        ISectionsInViewChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISectionsInViewChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISectionsInViewChangedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISectionsInViewChangedEventArgsFactory>
    {
        ISectionsInViewChangedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        ISectionsInViewChangedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionChangedEventArgs>
    {
        ISelectionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISelectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionChangedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionChangedEventArgsFactory>
    {
        ISelectionChangedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectionChangedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoom :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoom>
    {
        ISemanticZoom(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoom(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomInformation>
    {
        ISemanticZoomInformation(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomLocation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomLocation>
    {
        ISemanticZoomLocation(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomLocation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomStatics>
    {
        ISemanticZoomStatics(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomViewChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomViewChangedEventArgs>
    {
        ISemanticZoomViewChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomViewChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyout>
    {
        ISettingsFlyout(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyoutFactory>
    {
        ISettingsFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyoutStatics>
    {
        ISettingsFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISlider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISlider>
    {
        ISlider(std::nullptr_t = nullptr) noexcept {}
        ISlider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISlider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISlider2>
    {
        ISlider2(std::nullptr_t = nullptr) noexcept {}
        ISlider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISliderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISliderFactory>
    {
        ISliderFactory(std::nullptr_t = nullptr) noexcept {}
        ISliderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISliderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISliderStatics>
    {
        ISliderStatics(std::nullptr_t = nullptr) noexcept {}
        ISliderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISliderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISliderStatics2>
    {
        ISliderStatics2(std::nullptr_t = nullptr) noexcept {}
        ISliderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButton>
    {
        ISplitButton(std::nullptr_t = nullptr) noexcept {}
        ISplitButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonAutomationPeer>
    {
        ISplitButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonAutomationPeerFactory>
    {
        ISplitButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButtonClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonClickEventArgs>
    {
        ISplitButtonClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonFactory>
    {
        ISplitButtonFactory(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonStatics>
    {
        ISplitButtonStatics(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitView>
    {
        ISplitView(std::nullptr_t = nullptr) noexcept {}
        ISplitView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitView2>
    {
        ISplitView2(std::nullptr_t = nullptr) noexcept {}
        ISplitView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitView3>
    {
        ISplitView3(std::nullptr_t = nullptr) noexcept {}
        ISplitView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitViewFactory>
    {
        ISplitViewFactory(std::nullptr_t = nullptr) noexcept {}
        ISplitViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitViewPaneClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitViewPaneClosingEventArgs>
    {
        ISplitViewPaneClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISplitViewPaneClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitViewStatics>
    {
        ISplitViewStatics(std::nullptr_t = nullptr) noexcept {}
        ISplitViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitViewStatics2>
    {
        ISplitViewStatics2(std::nullptr_t = nullptr) noexcept {}
        ISplitViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanel>
    {
        IStackPanel(std::nullptr_t = nullptr) noexcept {}
        IStackPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanel2>
    {
        IStackPanel2(std::nullptr_t = nullptr) noexcept {}
        IStackPanel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanel4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanel4>
    {
        IStackPanel4(std::nullptr_t = nullptr) noexcept {}
        IStackPanel4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanel5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanel5>
    {
        IStackPanel5(std::nullptr_t = nullptr) noexcept {}
        IStackPanel5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanelFactory>
    {
        IStackPanelFactory(std::nullptr_t = nullptr) noexcept {}
        IStackPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanelStatics>
    {
        IStackPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IStackPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanelStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanelStatics2>
    {
        IStackPanelStatics2(std::nullptr_t = nullptr) noexcept {}
        IStackPanelStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanelStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanelStatics4>
    {
        IStackPanelStatics4(std::nullptr_t = nullptr) noexcept {}
        IStackPanelStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStackPanelStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStackPanelStatics5>
    {
        IStackPanelStatics5(std::nullptr_t = nullptr) noexcept {}
        IStackPanelStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStyleSelector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStyleSelector>
    {
        IStyleSelector(std::nullptr_t = nullptr) noexcept {}
        IStyleSelector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStyleSelectorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStyleSelectorFactory>
    {
        IStyleSelectorFactory(std::nullptr_t = nullptr) noexcept {}
        IStyleSelectorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStyleSelectorOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStyleSelectorOverrides>
    {
        IStyleSelectorOverrides(std::nullptr_t = nullptr) noexcept {}
        IStyleSelectorOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainBackgroundPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainBackgroundPanel>
    {
        ISwapChainBackgroundPanel(std::nullptr_t = nullptr) noexcept {}
        ISwapChainBackgroundPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainBackgroundPanel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainBackgroundPanel2>
    {
        ISwapChainBackgroundPanel2(std::nullptr_t = nullptr) noexcept {}
        ISwapChainBackgroundPanel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainBackgroundPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainBackgroundPanelFactory>
    {
        ISwapChainBackgroundPanelFactory(std::nullptr_t = nullptr) noexcept {}
        ISwapChainBackgroundPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainPanel>
    {
        ISwapChainPanel(std::nullptr_t = nullptr) noexcept {}
        ISwapChainPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainPanelFactory>
    {
        ISwapChainPanelFactory(std::nullptr_t = nullptr) noexcept {}
        ISwapChainPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwapChainPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwapChainPanelStatics>
    {
        ISwapChainPanelStatics(std::nullptr_t = nullptr) noexcept {}
        ISwapChainPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeControl>
    {
        ISwipeControl(std::nullptr_t = nullptr) noexcept {}
        ISwipeControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeControlFactory>
    {
        ISwipeControlFactory(std::nullptr_t = nullptr) noexcept {}
        ISwipeControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeControlStatics>
    {
        ISwipeControlStatics(std::nullptr_t = nullptr) noexcept {}
        ISwipeControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItem>
    {
        ISwipeItem(std::nullptr_t = nullptr) noexcept {}
        ISwipeItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItemFactory>
    {
        ISwipeItemFactory(std::nullptr_t = nullptr) noexcept {}
        ISwipeItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItemInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItemInvokedEventArgs>
    {
        ISwipeItemInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISwipeItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItemStatics>
    {
        ISwipeItemStatics(std::nullptr_t = nullptr) noexcept {}
        ISwipeItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItems :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItems>
    {
        ISwipeItems(std::nullptr_t = nullptr) noexcept {}
        ISwipeItems(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItemsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItemsFactory>
    {
        ISwipeItemsFactory(std::nullptr_t = nullptr) noexcept {}
        ISwipeItemsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISwipeItemsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeItemsStatics>
    {
        ISwipeItemsStatics(std::nullptr_t = nullptr) noexcept {}
        ISwipeItemsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIcon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIcon>
    {
        ISymbolIcon(std::nullptr_t = nullptr) noexcept {}
        ISymbolIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIconFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIconFactory>
    {
        ISymbolIconFactory(std::nullptr_t = nullptr) noexcept {}
        ISymbolIconFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIconSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIconSource>
    {
        ISymbolIconSource(std::nullptr_t = nullptr) noexcept {}
        ISymbolIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIconSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIconSourceFactory>
    {
        ISymbolIconSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ISymbolIconSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIconSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIconSourceStatics>
    {
        ISymbolIconSourceStatics(std::nullptr_t = nullptr) noexcept {}
        ISymbolIconSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymbolIconStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymbolIconStatics>
    {
        ISymbolIconStatics(std::nullptr_t = nullptr) noexcept {}
        ISymbolIconStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock>
    {
        ITextBlock(std::nullptr_t = nullptr) noexcept {}
        ITextBlock(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock2>
    {
        ITextBlock2(std::nullptr_t = nullptr) noexcept {}
        ITextBlock2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock3>
    {
        ITextBlock3(std::nullptr_t = nullptr) noexcept {}
        ITextBlock3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock4>
    {
        ITextBlock4(std::nullptr_t = nullptr) noexcept {}
        ITextBlock4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock5>
    {
        ITextBlock5(std::nullptr_t = nullptr) noexcept {}
        ITextBlock5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock6>
    {
        ITextBlock6(std::nullptr_t = nullptr) noexcept {}
        ITextBlock6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlock7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlock7>
    {
        ITextBlock7(std::nullptr_t = nullptr) noexcept {}
        ITextBlock7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics>
    {
        ITextBlockStatics(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics2>
    {
        ITextBlockStatics2(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics3>
    {
        ITextBlockStatics3(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics5>
    {
        ITextBlockStatics5(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics6>
    {
        ITextBlockStatics6(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockStatics7>
    {
        ITextBlockStatics7(std::nullptr_t = nullptr) noexcept {}
        ITextBlockStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox>
    {
        ITextBox(std::nullptr_t = nullptr) noexcept {}
        ITextBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox2>
    {
        ITextBox2(std::nullptr_t = nullptr) noexcept {}
        ITextBox2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox3>
    {
        ITextBox3(std::nullptr_t = nullptr) noexcept {}
        ITextBox3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox4>
    {
        ITextBox4(std::nullptr_t = nullptr) noexcept {}
        ITextBox4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox5>
    {
        ITextBox5(std::nullptr_t = nullptr) noexcept {}
        ITextBox5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox6>
    {
        ITextBox6(std::nullptr_t = nullptr) noexcept {}
        ITextBox6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox7>
    {
        ITextBox7(std::nullptr_t = nullptr) noexcept {}
        ITextBox7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBox8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBox8>
    {
        ITextBox8(std::nullptr_t = nullptr) noexcept {}
        ITextBox8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxBeforeTextChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxBeforeTextChangingEventArgs>
    {
        ITextBoxBeforeTextChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextBoxBeforeTextChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxFactory>
    {
        ITextBoxFactory(std::nullptr_t = nullptr) noexcept {}
        ITextBoxFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxSelectionChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxSelectionChangingEventArgs>
    {
        ITextBoxSelectionChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextBoxSelectionChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics>
    {
        ITextBoxStatics(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics2>
    {
        ITextBoxStatics2(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics3>
    {
        ITextBoxStatics3(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics5>
    {
        ITextBoxStatics5(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics6>
    {
        ITextBoxStatics6(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics7>
    {
        ITextBoxStatics7(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxStatics8>
    {
        ITextBoxStatics8(std::nullptr_t = nullptr) noexcept {}
        ITextBoxStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxTextChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxTextChangingEventArgs>
    {
        ITextBoxTextChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextBoxTextChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxTextChangingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxTextChangingEventArgs2>
    {
        ITextBoxTextChangingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ITextBoxTextChangingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextChangedEventArgs>
    {
        ITextChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextCommandBarFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextCommandBarFlyout>
    {
        ITextCommandBarFlyout(std::nullptr_t = nullptr) noexcept {}
        ITextCommandBarFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextCommandBarFlyoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextCommandBarFlyoutFactory>
    {
        ITextCommandBarFlyoutFactory(std::nullptr_t = nullptr) noexcept {}
        ITextCommandBarFlyoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextCompositionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextCompositionChangedEventArgs>
    {
        ITextCompositionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextCompositionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextCompositionEndedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextCompositionEndedEventArgs>
    {
        ITextCompositionEndedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextCompositionEndedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextCompositionStartedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextCompositionStartedEventArgs>
    {
        ITextCompositionStartedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextCompositionStartedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextControlCopyingToClipboardEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextControlCopyingToClipboardEventArgs>
    {
        ITextControlCopyingToClipboardEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextControlCopyingToClipboardEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextControlCuttingToClipboardEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextControlCuttingToClipboardEventArgs>
    {
        ITextControlCuttingToClipboardEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextControlCuttingToClipboardEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextControlPasteEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextControlPasteEventArgs>
    {
        ITextControlPasteEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITextControlPasteEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickedEventArgs>
    {
        ITimePickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITimePickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePicker>
    {
        ITimePicker(std::nullptr_t = nullptr) noexcept {}
        ITimePicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePicker2>
    {
        ITimePicker2(std::nullptr_t = nullptr) noexcept {}
        ITimePicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePicker3>
    {
        ITimePicker3(std::nullptr_t = nullptr) noexcept {}
        ITimePicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFactory>
    {
        ITimePickerFactory(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyout>
    {
        ITimePickerFlyout(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyoutPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyoutPresenter>
    {
        ITimePickerFlyoutPresenter(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyoutPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyoutPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyoutPresenter2>
    {
        ITimePickerFlyoutPresenter2(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyoutPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyoutPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyoutPresenterStatics2>
    {
        ITimePickerFlyoutPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyoutPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyoutStatics>
    {
        ITimePickerFlyoutStatics(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerSelectedValueChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerSelectedValueChangedEventArgs>
    {
        ITimePickerSelectedValueChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITimePickerSelectedValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerStatics>
    {
        ITimePickerStatics(std::nullptr_t = nullptr) noexcept {}
        ITimePickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerStatics2>
    {
        ITimePickerStatics2(std::nullptr_t = nullptr) noexcept {}
        ITimePickerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerStatics3>
    {
        ITimePickerStatics3(std::nullptr_t = nullptr) noexcept {}
        ITimePickerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerValueChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerValueChangedEventArgs>
    {
        ITimePickerValueChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITimePickerValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleMenuFlyoutItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleMenuFlyoutItem>
    {
        IToggleMenuFlyoutItem(std::nullptr_t = nullptr) noexcept {}
        IToggleMenuFlyoutItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleMenuFlyoutItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleMenuFlyoutItemFactory>
    {
        IToggleMenuFlyoutItemFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleMenuFlyoutItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleMenuFlyoutItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleMenuFlyoutItemStatics>
    {
        IToggleMenuFlyoutItemStatics(std::nullptr_t = nullptr) noexcept {}
        IToggleMenuFlyoutItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSplitButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButton>
    {
        IToggleSplitButton(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSplitButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonAutomationPeer>
    {
        IToggleSplitButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSplitButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonAutomationPeerFactory>
    {
        IToggleSplitButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSplitButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonFactory>
    {
        IToggleSplitButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSplitButtonIsCheckedChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonIsCheckedChangedEventArgs>
    {
        IToggleSplitButtonIsCheckedChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonIsCheckedChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitch>
    {
        IToggleSwitch(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitchOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitchOverrides>
    {
        IToggleSwitchOverrides(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitchOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitchStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitchStatics>
    {
        IToggleSwitchStatics(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitchStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTip>
    {
        IToolTip(std::nullptr_t = nullptr) noexcept {}
        IToolTip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTip2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTip2>
    {
        IToolTip2(std::nullptr_t = nullptr) noexcept {}
        IToolTip2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipFactory>
    {
        IToolTipFactory(std::nullptr_t = nullptr) noexcept {}
        IToolTipFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipService>
    {
        IToolTipService(std::nullptr_t = nullptr) noexcept {}
        IToolTipService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipServiceStatics>
    {
        IToolTipServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IToolTipServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipStatics>
    {
        IToolTipStatics(std::nullptr_t = nullptr) noexcept {}
        IToolTipStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipStatics2>
    {
        IToolTipStatics2(std::nullptr_t = nullptr) noexcept {}
        IToolTipStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeView>
    {
        ITreeView(std::nullptr_t = nullptr) noexcept {}
        ITreeView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeView2>
    {
        ITreeView2(std::nullptr_t = nullptr) noexcept {}
        ITreeView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewCollapsedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewCollapsedEventArgs>
    {
        ITreeViewCollapsedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITreeViewCollapsedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewCollapsedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewCollapsedEventArgs2>
    {
        ITreeViewCollapsedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ITreeViewCollapsedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewDragItemsCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewDragItemsCompletedEventArgs>
    {
        ITreeViewDragItemsCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITreeViewDragItemsCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewDragItemsStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewDragItemsStartingEventArgs>
    {
        ITreeViewDragItemsStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITreeViewDragItemsStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewExpandingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewExpandingEventArgs>
    {
        ITreeViewExpandingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITreeViewExpandingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewExpandingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewExpandingEventArgs2>
    {
        ITreeViewExpandingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ITreeViewExpandingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewFactory>
    {
        ITreeViewFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItem>
    {
        ITreeViewItem(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItem2>
    {
        ITreeViewItem2(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemFactory>
    {
        ITreeViewItemFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemInvokedEventArgs>
    {
        ITreeViewItemInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemStatics>
    {
        ITreeViewItemStatics(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemStatics2>
    {
        ITreeViewItemStatics2(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemTemplateSettings>
    {
        ITreeViewItemTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemTemplateSettingsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemTemplateSettingsFactory>
    {
        ITreeViewItemTemplateSettingsFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemTemplateSettingsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemTemplateSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemTemplateSettingsStatics>
    {
        ITreeViewItemTemplateSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemTemplateSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewList :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewList>
    {
        ITreeViewList(std::nullptr_t = nullptr) noexcept {}
        ITreeViewList(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewListFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewListFactory>
    {
        ITreeViewListFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewListFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewNode :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewNode>
    {
        ITreeViewNode(std::nullptr_t = nullptr) noexcept {}
        ITreeViewNode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewNodeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewNodeFactory>
    {
        ITreeViewNodeFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewNodeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewNodeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewNodeStatics>
    {
        ITreeViewNodeStatics(std::nullptr_t = nullptr) noexcept {}
        ITreeViewNodeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewStatics>
    {
        ITreeViewStatics(std::nullptr_t = nullptr) noexcept {}
        ITreeViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewStatics2>
    {
        ITreeViewStatics2(std::nullptr_t = nullptr) noexcept {}
        ITreeViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITwoPaneView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITwoPaneView>
    {
        ITwoPaneView(std::nullptr_t = nullptr) noexcept {}
        ITwoPaneView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITwoPaneViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITwoPaneViewFactory>
    {
        ITwoPaneViewFactory(std::nullptr_t = nullptr) noexcept {}
        ITwoPaneViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITwoPaneViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITwoPaneViewStatics>
    {
        ITwoPaneViewStatics(std::nullptr_t = nullptr) noexcept {}
        ITwoPaneViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUIElementCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementCollection>
    {
        IUIElementCollection(std::nullptr_t = nullptr) noexcept {}
        IUIElementCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserControl>
    {
        IUserControl(std::nullptr_t = nullptr) noexcept {}
        IUserControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserControlFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserControlFactory>
    {
        IUserControlFactory(std::nullptr_t = nullptr) noexcept {}
        IUserControlFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserControlStatics>
    {
        IUserControlStatics(std::nullptr_t = nullptr) noexcept {}
        IUserControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVariableSizedWrapGrid :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVariableSizedWrapGrid>
    {
        IVariableSizedWrapGrid(std::nullptr_t = nullptr) noexcept {}
        IVariableSizedWrapGrid(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVariableSizedWrapGridStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVariableSizedWrapGridStatics>
    {
        IVariableSizedWrapGridStatics(std::nullptr_t = nullptr) noexcept {}
        IVariableSizedWrapGridStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IViewbox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewbox>
    {
        IViewbox(std::nullptr_t = nullptr) noexcept {}
        IViewbox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IViewboxStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewboxStatics>
    {
        IViewboxStatics(std::nullptr_t = nullptr) noexcept {}
        IViewboxStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingPanel>
    {
        IVirtualizingPanel(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingPanelFactory>
    {
        IVirtualizingPanelFactory(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingPanelOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingPanelOverrides>
    {
        IVirtualizingPanelOverrides(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingPanelOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingPanelProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingPanelProtected>
    {
        IVirtualizingPanelProtected(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingPanelProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingStackPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingStackPanel>
    {
        IVirtualizingStackPanel(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingStackPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingStackPanelOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingStackPanelOverrides>
    {
        IVirtualizingStackPanelOverrides(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingStackPanelOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizingStackPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizingStackPanelStatics>
    {
        IVirtualizingStackPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IVirtualizingStackPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView>
    {
        IWebView(std::nullptr_t = nullptr) noexcept {}
        IWebView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView2>
    {
        IWebView2(std::nullptr_t = nullptr) noexcept {}
        IWebView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView3>
    {
        IWebView3(std::nullptr_t = nullptr) noexcept {}
        IWebView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView4>
    {
        IWebView4(std::nullptr_t = nullptr) noexcept {}
        IWebView4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView5>
    {
        IWebView5(std::nullptr_t = nullptr) noexcept {}
        IWebView5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView6>
    {
        IWebView6(std::nullptr_t = nullptr) noexcept {}
        IWebView6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebView7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebView7>
    {
        IWebView7(std::nullptr_t = nullptr) noexcept {}
        IWebView7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewBrush>
    {
        IWebViewBrush(std::nullptr_t = nullptr) noexcept {}
        IWebViewBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewBrushStatics>
    {
        IWebViewBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IWebViewBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewContentLoadingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewContentLoadingEventArgs>
    {
        IWebViewContentLoadingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewContentLoadingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewDOMContentLoadedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewDOMContentLoadedEventArgs>
    {
        IWebViewDOMContentLoadedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewDOMContentLoadedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewDeferredPermissionRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewDeferredPermissionRequest>
    {
        IWebViewDeferredPermissionRequest(std::nullptr_t = nullptr) noexcept {}
        IWebViewDeferredPermissionRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewFactory4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewFactory4>
    {
        IWebViewFactory4(std::nullptr_t = nullptr) noexcept {}
        IWebViewFactory4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewLongRunningScriptDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewLongRunningScriptDetectedEventArgs>
    {
        IWebViewLongRunningScriptDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewLongRunningScriptDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewNavigationCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewNavigationCompletedEventArgs>
    {
        IWebViewNavigationCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewNavigationCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewNavigationFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewNavigationFailedEventArgs>
    {
        IWebViewNavigationFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewNavigationFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewNavigationStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewNavigationStartingEventArgs>
    {
        IWebViewNavigationStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewNavigationStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewNewWindowRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewNewWindowRequestedEventArgs>
    {
        IWebViewNewWindowRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewNewWindowRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewPermissionRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewPermissionRequest>
    {
        IWebViewPermissionRequest(std::nullptr_t = nullptr) noexcept {}
        IWebViewPermissionRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewPermissionRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewPermissionRequestedEventArgs>
    {
        IWebViewPermissionRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewPermissionRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewSeparateProcessLostEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewSeparateProcessLostEventArgs>
    {
        IWebViewSeparateProcessLostEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewSeparateProcessLostEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewSettings>
    {
        IWebViewSettings(std::nullptr_t = nullptr) noexcept {}
        IWebViewSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewStatics>
    {
        IWebViewStatics(std::nullptr_t = nullptr) noexcept {}
        IWebViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewStatics2>
    {
        IWebViewStatics2(std::nullptr_t = nullptr) noexcept {}
        IWebViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewStatics3>
    {
        IWebViewStatics3(std::nullptr_t = nullptr) noexcept {}
        IWebViewStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewStatics4>
    {
        IWebViewStatics4(std::nullptr_t = nullptr) noexcept {}
        IWebViewStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewStatics5>
    {
        IWebViewStatics5(std::nullptr_t = nullptr) noexcept {}
        IWebViewStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewUnsupportedUriSchemeIdentifiedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewUnsupportedUriSchemeIdentifiedEventArgs>
    {
        IWebViewUnsupportedUriSchemeIdentifiedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewUnsupportedUriSchemeIdentifiedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewUnviewableContentIdentifiedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewUnviewableContentIdentifiedEventArgs>
    {
        IWebViewUnviewableContentIdentifiedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewUnviewableContentIdentifiedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewUnviewableContentIdentifiedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewUnviewableContentIdentifiedEventArgs2>
    {
        IWebViewUnviewableContentIdentifiedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IWebViewUnviewableContentIdentifiedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebViewWebResourceRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebViewWebResourceRequestedEventArgs>
    {
        IWebViewWebResourceRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWebViewWebResourceRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWrapGrid :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWrapGrid>
    {
        IWrapGrid(std::nullptr_t = nullptr) noexcept {}
        IWrapGrid(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWrapGridStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWrapGridStatics>
    {
        IWrapGridStatics(std::nullptr_t = nullptr) noexcept {}
        IWrapGridStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
