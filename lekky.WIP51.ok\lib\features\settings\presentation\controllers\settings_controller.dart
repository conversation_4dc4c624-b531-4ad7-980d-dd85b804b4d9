// File: lib/features/settings/presentation/controllers/settings_controller.dart
import 'dart:async';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../core/data/database/db_helper.dart';
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/utils/error_handler.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/result.dart';
import '../../../../features/backup/backup_service.dart';

/// Controller for the settings screen
class SettingsController extends ChangeNotifier {
  final SettingsRepository _settingsRepository;
  final MeterEntryRepository _meterEntryRepository;
  final DBHelper _dbHelper;
  final BackupService _backupService = BackupService();

  bool _isLoading = true;
  String _error = '';
  String _meterUnit = '£';
  double _alertThreshold = 5.0;
  int _daysInAdvance = 2;
  String _dateFormat = 'DD-MM-YYYY';
  String _dateInfo = 'Date only';
  String _themeMode = 'system';
  bool _notificationsEnabled = true;
  double? _initialCredit;
  String _language = 'en';

  final _meterUnitController = StreamController<String>.broadcast();
  final _alertThresholdController = StreamController<double>.broadcast();
  final _daysInAdvanceController = StreamController<int>.broadcast();
  final _dateFormatController = StreamController<String>.broadcast();
  final _dateInfoController = StreamController<String>.broadcast();
  final _themeModeController = StreamController<String>.broadcast();
  final _notificationsEnabledController = StreamController<bool>.broadcast();
  final _initialCreditController = StreamController<double?>.broadcast();
  final _languageController = StreamController<String>.broadcast();

  SettingsController({
    SettingsRepository? settingsRepository,
    MeterEntryRepository? meterEntryRepository,
    DBHelper? dbHelper,
  })  : _settingsRepository = settingsRepository ?? SettingsRepository(),
        _meterEntryRepository = meterEntryRepository ?? MeterEntryRepository(),
        _dbHelper = dbHelper ?? DBHelper() {
    // Set default values immediately to prevent infinite loading
    _isLoading = false;
    notifyListeners();

    // Then load actual settings
    _loadSettings();
  }

  /// Get the loading state
  bool get isLoading => _isLoading;

  /// Get the error message
  String get error => _error;

  /// Get the meter unit
  Stream<String> get meterUnit => _meterUnitController.stream;

  /// Get the current meter unit value
  String get meterUnitValue => _meterUnit;

  /// Get the alert threshold
  Stream<double> get alertThreshold => _alertThresholdController.stream;

  /// Get the days in advance
  Stream<int> get daysInAdvance => _daysInAdvanceController.stream;

  /// Get the date format
  Stream<String> get dateFormat => _dateFormatController.stream;

  /// Get the date info
  Stream<String> get dateInfo => _dateInfoController.stream;

  /// Get the theme mode
  Stream<String> get themeMode => _themeModeController.stream;

  /// Get the notifications enabled state
  Stream<bool> get notificationsEnabled =>
      _notificationsEnabledController.stream;

  /// Get the initial credit
  Stream<double?> get initialCredit => _initialCreditController.stream;

  /// Get the initial credit value
  double? get initialCreditValue => _initialCredit;

  /// Get the language
  Stream<String> get language => _languageController.stream;

  /// Get the current language value
  String get languageValue => _language;

  /// Get the meter entry repository
  MeterEntryRepository get meterEntryRepository => _meterEntryRepository;

  /// Set the meter unit
  Future<void> setMeterUnit(String value) async {
    try {
      await _settingsRepository.setMeterUnit(value);
      _meterUnit = value;
      _meterUnitController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save meter unit: $e';
      notifyListeners();
    }
  }

  /// Set the alert threshold
  Future<void> setAlertThreshold(double value) async {
    try {
      await _settingsRepository.setAlertThreshold(value);
      _alertThreshold = value;
      _alertThresholdController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save alert threshold: $e';
      notifyListeners();
    }
  }

  /// Set the days in advance
  Future<void> setDaysInAdvance(int value) async {
    try {
      await _settingsRepository.setDaysInAdvance(value);
      _daysInAdvance = value;
      _daysInAdvanceController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save days in advance: $e';
      notifyListeners();
    }
  }

  /// Set the date format
  Future<void> setDateFormat(String value) async {
    try {
      await _settingsRepository.setDateFormat(value);
      _dateFormat = value;
      _dateFormatController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save date format: $e';
      notifyListeners();
    }
  }

  /// Set the date info
  Future<void> setDateInfo(String value) async {
    try {
      await _settingsRepository.setDateInfo(value);
      _dateInfo = value;
      _dateInfoController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save date info: $e';
      notifyListeners();
    }
  }

  /// Set the theme mode
  Future<void> setThemeMode(String value) async {
    try {
      await _settingsRepository.setThemeMode(value);
      _themeMode = value;
      _themeModeController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save theme mode: $e';
      notifyListeners();
    }
  }

  /// Set the notifications enabled state
  Future<void> setNotificationsEnabled(bool value) async {
    try {
      await _settingsRepository.setNotificationsEnabled(value);
      _notificationsEnabled = value;
      _notificationsEnabledController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save notifications setting: $e';
      notifyListeners();
    }
  }

  /// Set the language
  Future<void> setLanguage(String value) async {
    try {
      await _settingsRepository.setLanguage(value);
      _language = value;
      _languageController.sink.add(value);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save language setting: $e';
      notifyListeners();
    }
  }

  /// Set the initial credit
  Future<void> setInitialCredit(double? value) async {
    try {
      if (value == null) {
        await clearInitialCredit();
      } else {
        await _settingsRepository.setInitialCredit(value);
        _initialCredit = value;
        _initialCreditController.sink.add(value);
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to save initial credit: $e';
      notifyListeners();
    }
  }

  /// Clear the initial credit
  Future<void> clearInitialCredit() async {
    try {
      await _settingsRepository.clearInitialCredit();
      _initialCredit = null;
      _initialCreditController.sink.add(null);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to clear initial credit: $e';
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _meterUnitController.close();
    _alertThresholdController.close();
    _daysInAdvanceController.close();
    _dateFormatController.close();
    _dateInfoController.close();
    _themeModeController.close();
    _notificationsEnabledController.close();
    _initialCreditController.close();
    _languageController.close();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      _meterUnit = await _settingsRepository.getMeterUnit() ?? '£';
      _alertThreshold = await _settingsRepository.getAlertThreshold() ?? 5.0;
      _daysInAdvance = await _settingsRepository.getDaysInAdvance() ?? 2;
      _dateFormat = await _settingsRepository.getDateFormat() ?? 'DD-MM-YYYY';
      _dateInfo = await _settingsRepository.getDateInfo() ?? 'Date only';
      _themeMode = await _settingsRepository.getThemeMode() ?? 'system';
      _notificationsEnabled =
          await _settingsRepository.areNotificationsEnabled() ?? true;

      // Load initial credit
      _initialCredit = await _settingsRepository.getInitialCredit();

      // Load language
      try {
        _language = await _settingsRepository.getLanguage() ?? 'en';
      } catch (e) {
        // If the method doesn't exist yet, use default
        _language = 'en';
      }

      // Add values to streams
      _meterUnitController.sink.add(_meterUnit);
      _alertThresholdController.sink.add(_alertThreshold);
      _daysInAdvanceController.sink.add(_daysInAdvance);
      _dateFormatController.sink.add(_dateFormat);
      _dateInfoController.sink.add(_dateInfo);
      _themeModeController.sink.add(_themeMode);
      _notificationsEnabledController.sink.add(_notificationsEnabled);
      _initialCreditController.sink.add(_initialCredit);
      _languageController.sink.add(_language);

      // Set loading to false after successfully loading settings
      _isLoading = false;
    } catch (e) {
      _error = 'Failed to load settings: $e';
      _isLoading = false; // Set loading to false even if there's an error
    }
    // Notify listeners that loading state has changed
    notifyListeners();
  }

  /// Export data to a CSV file
  Future<void> exportData(BuildContext context) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Get all entries
      final entries = await _meterEntryRepository.getAllEntries();

      // Export entries using the backup service
      final result = await _backupService.exportMeterEntries(entries: entries);

      if (context.mounted) {
        result.fold(
          onSuccess: (file) {
            // Success
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Backup saved to Downloads/lekky_export_100.csv'),
                duration: Duration(seconds: 3),
              ),
            );
          },
          onFailure: (error) {
            // Error
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.message),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      }
    } catch (e) {
      _error = 'Failed to export data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Import data from a CSV file
  Future<void> importData(BuildContext context) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Show a loading dialog to indicate the process is starting
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Selecting file...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Pick a file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        dialogTitle: 'Select CSV file',
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        _isLoading = false;
        _error = 'No file selected';
        notifyListeners();
        return;
      }

      File csvFile;
      // Check if we have file bytes (for Android)
      if (result.files.first.bytes != null) {
        // Create a temporary file from bytes
        final tempDir = await getTemporaryDirectory();
        csvFile = File('${tempDir.path}/temp_import.csv');
        await csvFile.writeAsBytes(result.files.first.bytes!);
      }
      // Otherwise try to read from path (for iOS)
      else if (result.files.first.path != null) {
        csvFile = File(result.files.first.path!);
      } else {
        _error = 'Could not read file data';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Import entries using the backup service
      final importResult = await _backupService.importMeterEntries(csvFile);

      if (context.mounted) {
        importResult.fold(
          onSuccess: (entries) async {
            // Success - save entries to database
            for (final entry in entries) {
              await _meterEntryRepository.addEntry(entry);
            }

            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Backup loaded successfully: ${entries.length} entries imported'),
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          },
          onFailure: (error) {
            // Error
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.message),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      }
    } catch (e) {
      _error = 'Failed to import data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Backup all settings and data for testing
  Future<Result<File>> backupDataForTesting() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Get all entries
      final entries = await _meterEntryRepository.getAllEntries();

      // Export entries using the backup service
      final result = await _backupService.exportMeterEntries(entries: entries);

      return result;
    } catch (e) {
      _error = 'Failed to backup data: $e';
      logger.e('Failed to backup data', details: e.toString());
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Restore data from backup
  Future<Result<bool>> restoreDataFromBackup() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Check if backup file exists
      if (!await _backupService.backupFileExists()) {
        _error = 'No backup file found';
        logger.e('No backup file found');
        return Result.failure(AppError(
          message: 'No backup file found',
          type: ErrorType.permission,
          severity: ErrorSeverity.medium,
        ));
      }

      // Get the backup file path
      final path = await _backupService.getBackupFilePath();
      if (path == null) {
        return Result.failure(AppError(
          message: 'Could not access backup file path',
          type: ErrorType.permission,
          severity: ErrorSeverity.medium,
        ));
      }

      final file = File(path);

      // Import entries using the backup service
      final importResult = await _backupService.importMeterEntries(file);

      return importResult.fold(
        onSuccess: (entries) async {
          // Clear existing data first
          await clearAllData(silent: true);

          // Save entries to database
          for (final entry in entries) {
            await _meterEntryRepository.addEntry(entry);
          }

          // Set setup completed to true
          await _settingsRepository.setSetupCompleted(true);

          logger.i('Data restored from backup: ${entries.length} entries');
          return Result.success(true);
        },
        onFailure: (error) {
          _error = error.message;
          logger.e('Failed to restore data', details: error.message);
          return Result.failure(error);
        },
      );
    } catch (e) {
      _error = 'Failed to restore data: $e';
      logger.e('Failed to restore data', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to restore data: $e',
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
        details: e,
      ));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Reset app for testing
  Future<void> resetAppForTesting() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Clear all settings
      await _settingsRepository.clearAllSettings();

      // Explicitly set setup completed to false
      await _settingsRepository.setSetupCompleted(false);

      // Clear all entries
      final entries = await _meterEntryRepository.getAllEntries();
      for (final entry in entries) {
        if (entry.id != null) {
          await _meterEntryRepository.deleteEntry(entry.id!);
        }
      }

      // Reset all settings to defaults
      await setMeterUnit('£');
      await setAlertThreshold(5.0);
      await setDaysInAdvance(2);
      await setDateFormat('DD-MM-YYYY');
      await setDateInfo('Date only');
      await setThemeMode('system');
      await setNotificationsEnabled(false);
      await setInitialCredit(null);

      logger.i('App reset for testing');
    } catch (e) {
      _error = 'Failed to reset app: $e';
      logger.e('Failed to reset app', details: e.toString());
      rethrow; // Rethrow to allow proper error handling
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear all data
  Future<void> clearAllData({bool silent = false}) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Get all entries
      final entries = await _meterEntryRepository.getAllEntries();

      // Delete all entries
      for (final entry in entries) {
        if (entry.id != null) {
          await _meterEntryRepository.deleteEntry(entry.id!);
        }
      }

      // Show success message
      _error = '';
    } catch (e) {
      _error = 'Failed to clear data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
