// File: lib/features/history/presentation/widgets/history_table.dart
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../controllers/history_controller.dart';

/// A table that displays meter entries
class HistoryTable extends StatelessWidget {
  final List<MeterEntry> entries;
  final HistoryController controller;
  final Function(MeterEntry) onEntryTap;

  const HistoryTable({
    Key? key,
    required this.entries,
    required this.controller,
    required this.onEntryTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (entries.isEmpty) {
      return const Center(
        child: Text('No entries found'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: DataTable(
              columnSpacing: 16,
              headingRowHeight: 48,
              dataRowMinHeight: 56,
              dataRowMaxHeight: 56,
              dividerThickness: 1,
              showCheckboxColumn: controller.isEditMode,
              headingRowColor: MaterialStateProperty.all(
                  AppColors.surfaceVariant.withOpacity(0.3)),
              columns: _buildColumns(context),
              rows: _buildRows(context),
              horizontalMargin: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<DataColumn> _buildColumns(BuildContext context) {
    return [
      if (controller.isEditMode)
        const DataColumn(
          label: Text(''),
        ),
      DataColumn(
        label: Tooltip(
          message: 'When the action happened (meter reading or top-up)',
          child: Text(
            'Date',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ),
      ),
      DataColumn(
        label: Tooltip(
          message: 'Amount left on meter (readings) or amount added (top-ups)',
          child: Text(
            'Amount',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ),
      ),
      DataColumn(
        label: Tooltip(
          message: 'Recent average usage between consecutive readings',
          child: Text(
            'Average',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ),
      ),
      if (controller.isEditMode)
        DataColumn(
          label: Text(
            'Actions',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ),
    ];
  }

  List<DataRow> _buildRows(BuildContext context) {
    return entries.map((entry) {
      final isSelected = controller.selectedEntryId == entry.id;
      final isValid = controller.isEntryValid(entry.id ?? -1);
      final severity = controller.getValidationSeverity(entry.id ?? -1);

      Color? rowColor;
      if (!isValid) {
        rowColor = severity == 'error'
            ? AppColors.error.withOpacity(0.1)
            : AppColors.warning.withOpacity(0.1);
      }

      return DataRow(
        selected: isSelected,
        color: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.selected)) {
              return Theme.of(context).colorScheme.primary.withOpacity(0.08);
            }
            return rowColor;
          },
        ),
        onSelectChanged: controller.isEditMode
            ? (selected) {
                if (selected != null && selected) {
                  controller.selectEntry(entry.id ?? -1);
                } else {
                  controller.clearSelection();
                }
              }
            : null,
        cells: _buildCells(context, entry, isValid, severity),
      );
    }).toList();
  }

  List<DataCell> _buildCells(
      BuildContext context, MeterEntry entry, bool isValid, String severity) {
    final isTopUp = entry.amountToppedUp > 0;
    final amount = isTopUp ? entry.amountToppedUp : entry.reading;
    final average = entry.shortAverageAfterTopUp;

    return [
      if (controller.isEditMode)
        DataCell(
          Checkbox(
            value: controller.selectedEntryId == entry.id,
            onChanged: (selected) {
              if (selected != null && selected) {
                controller.selectEntry(entry.id ?? -1);
              } else {
                controller.clearSelection();
              }
            },
          ),
        ),
      DataCell(
        Text(
          controller.formatDate(entry.timestamp),
          style: AppTextStyles.bodyMedium.copyWith(
            color: !isValid
                ? (severity == 'error' ? AppColors.error : AppColors.warning)
                : null,
          ),
        ),
        onTap: () => onEntryTap(entry),
      ),
      DataCell(
        Row(
          children: [
            Icon(
              isTopUp ? Icons.add_circle : Icons.remove_circle,
              color: isTopUp ? AppColors.tertiary : AppColors.primary,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              '${controller.meterUnit}${amount.toStringAsFixed(2)}',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: isTopUp ? AppColors.tertiary : AppColors.primary,
              ),
            ),
          ],
        ),
        onTap: () => onEntryTap(entry),
      ),
      DataCell(
        Tooltip(
          message: average != null
              ? 'Recent average: ${controller.meterUnit}${average.toStringAsFixed(2)}/day'
              : 'No average available for this entry',
          child: Text(
            average != null
                ? '${controller.meterUnit}${average.toStringAsFixed(2)}/day'
                : 'N/A',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
              color: average != null ? AppColors.onSurface : AppColors.outline,
            ),
          ),
        ),
        onTap: () => onEntryTap(entry),
      ),
      if (controller.isEditMode)
        DataCell(
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: AppColors.primary),
                onPressed: () => onEntryTap(entry),
                tooltip: 'Edit',
                iconSize: 20,
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  if (entry.id != null) {
                    controller.deleteEntry(entry.id!);
                  }
                },
                tooltip: 'Delete',
                iconSize: 20,
                color: AppColors.error,
              ),
            ],
          ),
        ),
    ];
  }
}
