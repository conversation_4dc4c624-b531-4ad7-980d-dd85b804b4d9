// File: lib/core/utils/date_time_utils.dart
import 'package:intl/intl.dart';

/// Utility class for date and time operations
class DateTimeUtils {
  // Private constructor to prevent instantiation
  DateTimeUtils._();

  /// Formats a date according to the specified format
  static String formatDate(DateTime date, String format) {
    return DateFormat(format).format(date);
  }

  /// Formats a date using the default format (DD-MM-YYYY)
  static String formatDateDefault(DateTime date) {
    return DateFormat('dd-MM-yyyy').format(date);
  }

  /// Formats a date with time (DD-MM-YYYY HH:MM)
  static String formatDateWithTime(DateTime date) {
    return DateFormat('dd-MM-yyyy HH:mm').format(date);
  }

  /// Formats a date with day name (DDD, DD-MM-YYYY)
  static String formatDateWithDayName(DateTime date) {
    return DateFormat('EEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with full day name (DDDD, DD-MM-YYYY)
  static String formatDateWithFullDayName(DateTime date) {
    return DateFormat('EEEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with month name (DD MMM YYYY)
  static String formatDateWithMonthName(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  /// Formats a date with full month name (DD MMMM YYYY)
  static String formatDateWithFullMonthName(DateTime date) {
    return DateFormat('dd MMMM yyyy').format(date);
  }

  /// Formats a date in a relative way (Today, Yesterday, etc.)
  static String formatDateRelative(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return formatDateDefault(date);
    }
  }

  /// Formats a date in a relative way with time (Today at HH:MM, etc.)
  static String formatDateRelativeWithTime(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    final time = DateFormat('HH:mm').format(date);

    if (dateOnly == today) {
      return 'Today at $time';
    } else if (dateOnly == yesterday) {
      return 'Yesterday at $time';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow at $time';
    } else {
      return '${formatDateDefault(date)} at $time';
    }
  }

  /// Calculates the difference in days between two dates
  static int daysBetween(DateTime from, DateTime to) {
    final fromDate = DateTime(from.year, from.month, from.day);
    final toDate = DateTime(to.year, to.month, to.day);
    return toDate.difference(fromDate).inDays;
  }

  /// Checks if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// Checks if a date is yesterday
  static bool isYesterday(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
  }

  /// Checks if a date is tomorrow
  static bool isTomorrow(DateTime date) {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return date.year == tomorrow.year && date.month == tomorrow.month && date.day == tomorrow.day;
  }

  /// Checks if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Checks if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Gets the start of the day for a date
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Gets the end of the day for a date
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Gets the start of the week for a date (Monday)
  static DateTime startOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(date.year, date.month, date.day - (day - 1));
  }

  /// Gets the end of the week for a date (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(date.year, date.month, date.day + (7 - day), 23, 59, 59, 999);
  }

  /// Gets the start of the month for a date
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Gets the end of the month for a date
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// Gets the start of the year for a date
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Gets the end of the year for a date
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }
}
