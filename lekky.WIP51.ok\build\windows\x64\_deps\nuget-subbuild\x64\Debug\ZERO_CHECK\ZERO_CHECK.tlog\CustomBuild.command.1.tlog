^D:\000.WORKSPACE\LEKKY\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\69C1F89F5AFA846445ABE3C1FFFF3AB7\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/000.Workspace/lekky/build/windows/x64/_deps/nuget-subbuild -BD:/000.Workspace/lekky/build/windows/x64/_deps/nuget-subbuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/000.Workspace/lekky/build/windows/x64/_deps/nuget-subbuild/nuget-populate.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
