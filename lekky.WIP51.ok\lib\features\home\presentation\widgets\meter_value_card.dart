// File: lib/features/home/<USER>/widgets/meter_value_card.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A card that displays the meter total and average usage
class MeterValueCard extends StatelessWidget {
  final String meterTotal;
  final String averageUsage;
  final String shortTermAverageUsage;
  final VoidCallback? onTap;
  final bool isLoading;

  const MeterValueCard({
    Key? key,
    required this.meterTotal,
    required this.averageUsage,
    required this.shortTermAverageUsage,
    this.onTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.primaryTextDark : AppColors.primary;
    final valueColor = isDarkMode ? AppColors.valueTextDark : AppColors.primary;

    return AppCard(
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Meter Total',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: labelColor,
                      ),
                    ),
                    const CircleAvatar(
                      backgroundColor: AppColors.primary,
                      radius: 14,
                      child: Icon(
                        Icons.electric_meter,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Check if the meter total contains a "+" sign
                meterTotal.contains('+')
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left side: Meter reading
                          Expanded(
                            flex: 1,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              alignment: Alignment.centerLeft,
                              child: Text(
                                meterTotal.split('+')[0].trim(),
                                style: AppTextStyles.valueText.copyWith(
                                  fontSize:
                                      36, // Slightly smaller font size to prevent overflow
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                          ),
                          // Right side: Top-up amount
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // Top-up amount
                                FittedBox(
                                  fit: BoxFit.scaleDown,
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    '+ ${meterTotal.split('+')[1].trim()}',
                                    style: AppTextStyles.valueText.copyWith(
                                      fontSize:
                                          36, // Slightly smaller font size to prevent overflow
                                      fontWeight: FontWeight.bold,
                                      color: AppColors
                                          .costTab, // Dark orange from Cost screen
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                // "Total Top Ups" text
                                Text(
                                  'Total Top Ups',
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    fontSize: 14,
                                    color: AppColors.costTab,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      )
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.centerLeft,
                        child: Text(
                          meterTotal,
                          style: AppTextStyles.valueText.copyWith(
                            fontSize: 40, // Larger font size to match design
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Average Usage',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: labelColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        averageUsage == 'No averages yet :('
                            ? Text(
                                averageUsage,
                                style: AppTextStyles.valueText.copyWith(
                                  fontSize: 16,
                                  color: valueColor,
                                  fontStyle: FontStyle.italic,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : Text(
                                averageUsage,
                                style: AppTextStyles.valueText.copyWith(
                                  fontSize: 20,
                                  color: valueColor,
                                ),
                              ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Recent Usage',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: labelColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        shortTermAverageUsage == 'No averages yet :('
                            ? Text(
                                shortTermAverageUsage,
                                style: AppTextStyles.valueText.copyWith(
                                  fontSize: 16,
                                  color: valueColor,
                                  fontStyle: FontStyle.italic,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : Text(
                                shortTermAverageUsage,
                                style: AppTextStyles.valueText.copyWith(
                                  fontSize: 20,
                                  color: valueColor,
                                ),
                              ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
    );
  }
}
