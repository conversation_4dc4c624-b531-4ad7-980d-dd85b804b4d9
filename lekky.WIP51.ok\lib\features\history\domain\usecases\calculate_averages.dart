// File: lib/features/history/domain/usecases/calculate_averages.dart
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/average_manager.dart';

/// Use case for calculating averages for meter entries
class CalculateAverages {
  /// Execute the use case to calculate both short and total averages using the agreed logic
  List<MeterEntry> execute(List<MeterEntry> entries) {
    if (entries.isEmpty) return entries;

    // Use the AverageManager to calculate averages
    return AverageManager().calculateAndUpdateAverages(entries);
  }

  /// Calculate the total average up to a specific entry
  double calculateTotalAverageUpToEntry(
      List<MeterEntry> entries, int entryIndex) {
    if (entries.isEmpty || entryIndex < 0 || entryIndex >= entries.length) {
      return 0.0;
    }

    // Get the entry at the specified index
    final entry = entries[entryIndex];

    // Process all entries up to and including this entry
    final entriesToProcess = entries
        .where((e) => e.timestamp.compareTo(entry.timestamp) <= 0)
        .toList();

    // Calculate averages using the AverageManager
    final updatedEntries =
        AverageManager().calculateAndUpdateAverages(entriesToProcess);

    // Find the entry that corresponds to the entry at entryIndex
    final updatedEntry = updatedEntries.lastWhere(
      (e) => e.timestamp.isAtSameMomentAs(entry.timestamp),
      orElse: () => entry,
    );

    // Return the total average as a double
    return updatedEntry.totalAverageUpToThisPoint ?? 0.0;
  }

  /// Calculate the short average for a specific entry
  double calculateShortAverageForEntry(
      List<MeterEntry> entries, int entryIndex) {
    if (entries.isEmpty || entryIndex < 0 || entryIndex >= entries.length) {
      return 0.0;
    }

    // Get the entry at the specified index
    final entry = entries[entryIndex];

    // Process all entries up to and including this entry
    final entriesToProcess = entries
        .where((e) => e.timestamp.compareTo(entry.timestamp) <= 0)
        .toList();

    // Calculate averages using the AverageManager
    final updatedEntries =
        AverageManager().calculateAndUpdateAverages(entriesToProcess);

    // Find the entry that corresponds to the entry at entryIndex
    final updatedEntry = updatedEntries.lastWhere(
      (e) => e.timestamp.isAtSameMomentAs(entry.timestamp),
      orElse: () => entry,
    );

    // Return the short average as a double
    return updatedEntry.shortAverageAfterTopUp ?? 0.0;
  }
}
