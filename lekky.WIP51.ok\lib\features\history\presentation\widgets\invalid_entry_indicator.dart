// File: lib/features/history/presentation/widgets/invalid_entry_indicator.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_dialog.dart';
import '../../domain/models/validation_result.dart';

/// A widget that displays an indicator for invalid entries
class InvalidEntryIndicator extends StatelessWidget {
  final ValidationResult validationResult;
  final bool showIcon;
  final bool showText;
  final double iconSize;

  const InvalidEntryIndicator({
    Key? key,
    required this.validationResult,
    this.showIcon = true,
    this.showText = false,
    this.iconSize = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (validationResult.isValid) {
      return const SizedBox.shrink();
    }

    final isError = validationResult.severity == 'error';
    final color = isError ? AppColors.error : AppColors.warning;
    final icon = isError ? Icons.error : Icons.warning;

    return InkWell(
      onTap: () => _showErrorDialog(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon)
            Icon(
              icon,
              color: color,
              size: iconSize,
            ),
          if (showIcon && showText) const SizedBox(width: 4),
          if (showText)
            Text(
              validationResult.errorMessage ?? 'Invalid entry',
              style: AppTextStyles.bodySmall.copyWith(
                color: color,
              ),
            ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context) {
    final isError = validationResult.severity == 'error';
    final color = isError ? AppColors.error : AppColors.warning;
    final icon = isError ? Icons.error : Icons.warning;
    final title = 'Invalid Entry';

    AppDialog.show(
      context: context,
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            validationResult.errorMessage ?? 'Invalid entry',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'To fix this issue, enable Edit Mode and edit or delete the entry.',
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
