// File: lib/features/setup/presentation/controllers/setup_controller.dart
import 'package:flutter/material.dart';
import '../../../../core/settings/index.dart';
import '../../data/setup_repository.dart';
import '../../domain/models/setup_config.dart';

/// Controller for the setup screen
class SetupController extends ChangeNotifier {
  final SetupRepository _repository;

  // State variables
  SetupConfig _config = SetupConfig.defaultConfig();
  AppSettings _appSettings = AppSettings.defaultSettings();
  bool _isLoading = true;
  bool _isSaving = false;
  String _error = '';
  bool _isSetupCompleted = false;

  // Getters
  SetupConfig get config => _config;
  AppSettings get appSettings => _appSettings;
  bool get isLoading => _isLoading;
  bool get isSaving => _isSaving;
  String get error => _error;
  bool get isSetupCompleted => _isSetupCompleted;

  SetupController(this._repository);

  /// Initialize the controller
  Future<void> init() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Check if setup is completed
      _isSetupCompleted = await _repository.isSetupCompleted();

      // Load existing configuration if setup is completed
      if (_isSetupCompleted) {
        _config = await _repository.loadSetup();

        // Convert SetupConfig to AppSettings
        _appSettings = AppSettings(
          meterUnit: _config.meterUnit,
          alertThreshold: _config.alertThreshold,
          daysInAdvance: _config.daysInAdvance,
          dateFormat: _config.dateFormat,
          dateInfo: _config.dateInfo,
          notificationsEnabled: _config.notificationsEnabled,
          initialMeterCredit: _config.initialMeterCredit,
          themeMode: ThemeMode.system,
          language: _config.language,
        );
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load setup: $e';
      notifyListeners();
    }
  }

  /// Update the meter unit
  void updateMeterUnit(String meterUnit) {
    _config = _config.copyWith(meterUnit: meterUnit);
    _appSettings = _appSettings.copyWith(meterUnit: meterUnit);
    notifyListeners();
  }

  /// Update the alert threshold
  void updateAlertThreshold(double alertThreshold) {
    _config = _config.copyWith(alertThreshold: alertThreshold);
    _appSettings = _appSettings.copyWith(alertThreshold: alertThreshold);
    notifyListeners();
  }

  /// Update the days in advance
  void updateDaysInAdvance(int daysInAdvance) {
    _config = _config.copyWith(daysInAdvance: daysInAdvance);
    _appSettings = _appSettings.copyWith(daysInAdvance: daysInAdvance);
    notifyListeners();
  }

  /// Update the date format
  void updateDateFormat(String dateFormat) {
    _config = _config.copyWith(dateFormat: dateFormat);
    _appSettings = _appSettings.copyWith(dateFormat: dateFormat);
    notifyListeners();
  }

  /// Update the date info
  void updateDateInfo(String dateInfo) {
    _config = _config.copyWith(dateInfo: dateInfo);
    _appSettings = _appSettings.copyWith(dateInfo: dateInfo);
    notifyListeners();
  }

  /// Update notifications enabled
  void updateNotificationsEnabled(bool notificationsEnabled) {
    _config = _config.copyWith(notificationsEnabled: notificationsEnabled);
    _appSettings =
        _appSettings.copyWith(notificationsEnabled: notificationsEnabled);
    notifyListeners();
  }

  /// Update initial meter credit
  void updateInitialMeterCredit(double? initialMeterCredit) {
    _config = _config.copyWith(initialMeterCredit: initialMeterCredit);
    _appSettings =
        _appSettings.copyWith(initialMeterCredit: initialMeterCredit);
    notifyListeners();
  }

  /// Update theme mode
  void updateThemeMode(ThemeMode themeMode) {
    _appSettings = _appSettings.copyWith(themeMode: themeMode);
    notifyListeners();
  }

  /// Update language
  void updateLanguage(String language) {
    _config = _config.copyWith(language: language);
    _appSettings = _appSettings.copyWith(language: language);
    notifyListeners();
  }

  /// Save the setup configuration
  Future<bool> saveSetup() async {
    _isSaving = true;
    _error = '';
    notifyListeners();

    try {
      // Update AppSettings from SetupConfig
      _appSettings = AppSettings(
        meterUnit: _config.meterUnit,
        alertThreshold: _config.alertThreshold,
        daysInAdvance: _config.daysInAdvance,
        dateFormat: _config.dateFormat,
        dateInfo: _config.dateInfo,
        notificationsEnabled: _config.notificationsEnabled,
        initialMeterCredit: _config.initialMeterCredit,
        themeMode: _appSettings.themeMode,
        language: _config.language,
      );

      await _repository.saveSetup(_config);
      _isSetupCompleted = true;
      _isSaving = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isSaving = false;
      _error = 'Failed to save setup: $e';
      notifyListeners();
      return false;
    }
  }

  /// Reset the setup configuration to default
  void resetToDefault() {
    _config = SetupConfig.defaultConfig();
    _appSettings = AppSettings.defaultSettings();
    notifyListeners();
  }

  /// Load meter data from storage
  Future<bool> loadMeterData() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Load existing data from repository
      final success = await _repository.loadMeterData();

      if (success) {
        // Reload the setup configuration
        _config = await _repository.loadSetup();

        // Convert SetupConfig to AppSettings
        _appSettings = AppSettings(
          meterUnit: _config.meterUnit,
          alertThreshold: _config.alertThreshold,
          daysInAdvance: _config.daysInAdvance,
          dateFormat: _config.dateFormat,
          dateInfo: _config.dateInfo,
          notificationsEnabled: _config.notificationsEnabled,
          initialMeterCredit: _config.initialMeterCredit,
          themeMode: _appSettings.themeMode,
          language: _config.language,
        );

        _isSetupCompleted = true;
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _isLoading = false;
        _error = 'No meter data found to load';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load meter data: $e';
      notifyListeners();
      return false;
    }
  }
}
