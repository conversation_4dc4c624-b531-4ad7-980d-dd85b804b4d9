// File: lib/core/utils/helpful_messages.dart

/// Class that provides helpful messages to display to users
class HelpfulMessages {
  // Private constructor to prevent instantiation
  HelpfulMessages._();

  /// List of all helpful messages
  static final List<String> allMessages = [
    // Original messages - shortened for better display in message bar
    'Log meter readings weekly for accurate data.',
    'Recent-avg shows usage between consecutive readings.',
    'Total-avg shows usage since your first reading.',
    'Date column shows when readings/top-ups happened.',
    'Review your usage history to find savings.',
    'Export history for spreadsheet analysis.',
    'Enable alerts to avoid usage surprises.',
    'Update thresholds when your plan changes.',
    'Test scenarios to see potential savings.',
    'Check the app daily for better control.',

    // Additional messages - shortened for better display
    'Compare monthly averages to spot patterns.',
    'Add top-ups promptly for accurate balance.',
    'Use filters to focus on specific entries.',
    'Check for invalid entries regularly.',
    'Track daily average for realistic goals.',
    'Identify high usage periods to save money.',
    'Validate readings for accurate calculations.',
    'Monitor trends to predict future costs.',
    'Total-avg provides long-term consumption insights.',
    'Use history to spot unusual patterns.',
    'Enter readings at consistent times daily.',
    'Review past top-ups for better budgeting.',
    'Check usage after adding new appliances.',
    'Use dark mode to save battery at night.',
    'Set reminders for regular meter readings.',
    'Compare usage before/after energy changes.',
    'Look for usage spikes to identify issues.',
    'Track credit balance to avoid cutoffs.',
    'Track your usage patterns for budget planning.',
    'Check history before contacting provider.',
  ];

  /// Get a random helpful message
  static String getRandomMessage() {
    allMessages.shuffle();
    return allMessages.first;
  }

  /// Get a specific message by index
  static String getMessageByIndex(int index) {
    if (index < 0 || index >= allMessages.length) {
      return getRandomMessage();
    }
    return allMessages[index];
  }

  /// Get a rotating message based on the current date
  /// This ensures users see different messages each day
  static String getDailyMessage() {
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    final messageIndex = dayOfYear % allMessages.length;
    return allMessages[messageIndex];
  }
}
