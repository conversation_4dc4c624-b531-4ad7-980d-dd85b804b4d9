// File: lib/features/setup/data/setup_repository.dart
import '../../../core/data/repositories/settings_repository.dart';
import '../../../core/data/repositories/meter_entry_repository.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../domain/models/setup_config.dart';
import '../domain/usecases/save_setup.dart';
import '../domain/usecases/load_setup.dart';

/// Repository for the setup screen
class SetupRepository {
  final SettingsRepository _settingsRepository;
  final MeterEntryRepository _meterEntryRepository;
  final SaveSetup _saveSetup;
  final LoadSetup _loadSetup;

  SetupRepository({
    required SettingsRepository settingsRepository,
    MeterEntryRepository? meterEntryRepository,
  })  : _settingsRepository = settingsRepository,
        _meterEntryRepository = meterEntryRepository ?? MeterEntryRepository(),
        _saveSetup = SaveSetup(settingsRepository),
        _loadSetup = LoadSetup(settingsRepository);

  /// Save setup configuration
  Future<void> saveSetup(SetupConfig config) async {
    await _saveSetup.execute(config);

    // If initialMeterCredit is provided, create an initial meter entry
    if (config.initialMeterCredit != null && config.initialMeterCredit! > 0) {
      // Create a meter entry with the initial credit
      final initialEntry = MeterEntry(
        reading: config.initialMeterCredit!,
        amountToppedUp: 0.0,
        timestamp: DateTime.now(),
      );

      // Save the initial entry to the database
      await _meterEntryRepository.addEntry(initialEntry);
    }
  }

  /// Load setup configuration
  Future<SetupConfig> loadSetup() async {
    return await _loadSetup.execute();
  }

  /// Check if setup is completed
  Future<bool> isSetupCompleted() async {
    return await _loadSetup.isSetupCompleted();
  }

  /// Set setup completed
  Future<void> setSetupCompleted(bool value) async {
    await _settingsRepository.setSetupCompleted(value);
  }

  /// Load meter data from storage
  Future<bool> loadMeterData() async {
    try {
      // Check if there are any entries in the database
      final entries = await _meterEntryRepository.getAllEntries();

      if (entries.isEmpty) {
        return false; // No data to load
      }

      // Data exists, mark setup as completed
      await setSetupCompleted(true);

      return true;
    } catch (e) {
      // Log the error
      logger.e('Error loading meter data', details: e.toString());
      return false;
    }
  }
}
