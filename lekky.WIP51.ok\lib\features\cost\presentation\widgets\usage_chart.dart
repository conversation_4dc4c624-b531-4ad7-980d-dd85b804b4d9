// File: lib/features/cost/presentation/widgets/usage_chart.dart
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../models/chart_data.dart';

/// A chart that displays usage data
class UsageChart extends StatelessWidget {
  final List<ChartData> data;
  final String meterUnit;

  const UsageChart({
    super.key,
    required this.data,
    required this.meterUnit,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('No data available'),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxY(),
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: AppColors.surfaceVariant,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final chartData = data[groupIndex];
              return BarTooltipItem(
                '${DateTimeUtils.formatDateWithMonthName(chartData.date)}\n',
                const TextStyle(
                  color: AppColors.onSurfaceVariant,
                  fontWeight: FontWeight.bold,
                ),
                children: [
                  TextSpan(
                    text: '$meterUnit${chartData.cost.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const TextSpan(
                    text: '\n',
                  ),
                  TextSpan(
                    text: '${chartData.usage.toStringAsFixed(2)} units',
                    style: const TextStyle(
                      color: AppColors.secondary,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value < 0 || value >= data.length) {
                  return const SizedBox();
                }
                final date = data[value.toInt()].date;
                return Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    _getDateLabel(date),
                    style: const TextStyle(
                      color: AppColors.onSurfaceVariant,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(
                      color: AppColors.onSurfaceVariant,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(
              showTitles: false,
            ),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(
              showTitles: false,
            ),
          ),
        ),
        gridData: FlGridData(
          show: true,
          horizontalInterval: _getMaxY() / 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.outline.withOpacity(0.2),
              strokeWidth: 1,
            );
          },
          drawVerticalLine: false,
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _getBarGroups(),
      ),
    );
  }

  double _getMaxY() {
    if (data.isEmpty) {
      return 10;
    }
    final maxCost = data.map((e) => e.cost).reduce((a, b) => a > b ? a : b);
    return (maxCost * 1.2).ceilToDouble();
  }

  List<BarChartGroupData> _getBarGroups() {
    return List.generate(
      data.length,
      (index) {
        final chartData = data[index];
        return BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              toY: chartData.cost,
              color: AppColors.primary,
              width: 16,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getDateLabel(DateTime date) {
    final now = DateTime.now();
    if (DateTimeUtils.isToday(date)) {
      return 'Today';
    } else if (DateTimeUtils.isYesterday(date)) {
      return 'Yesterday';
    } else if (date.year == now.year) {
      return DateTimeUtils.formatDate(date, 'dd MMM');
    } else {
      return DateTimeUtils.formatDate(date, 'MM/yy');
    }
  }
}
