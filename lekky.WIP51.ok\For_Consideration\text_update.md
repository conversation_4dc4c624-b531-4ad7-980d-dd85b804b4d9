# Text Enhancement Plan for Lekky App

## Overview

This document outlines a comprehensive plan to enhance the typography and text elements in the Lekky app to improve readability, accessibility, and overall user experience. The plan is based on mobile typography best practices and aims to create a consistent, legible, and visually appealing text system throughout the app.

## Current State Analysis

The Lekky app currently uses:
- Roboto as the primary font family
- A comprehensive text style system with display, headline, title, body, and label styles
- Consistent color schemes for light and dark modes
- Appropriate font sizes for most UI elements

## Areas for Improvement

1. **Font Size Optimization**
   - Some text elements may be too small for comfortable reading on mobile devices
   - Input fields should maintain minimum 16px font size for better usability
   - Small text elements (11px) may cause accessibility issues

2. **Contrast Enhancement**
   - Improve text contrast ratios, especially in dark mode
   - Ensure all text meets WCAG AA standards (4.5:1 for normal text, 3:1 for large text)

3. **Line Height and Spacing**
   - Optimize line height for better readability
   - Improve paragraph spacing for better content scanning

4. **Font Weight Distribution**
   - Review and optimize font weight usage for better visual hierarchy
   - Ensure sufficient weight contrast between headings and body text

5. **Text Truncation and Overflow**
   - Implement better handling of long text in confined spaces
   - Add proper ellipsis and text wrapping strategies

6. **Responsive Typography**
   - Ensure text scales appropriately on different device sizes
   - Implement minimum touch target sizes for interactive text elements

7. **Accessibility Enhancements**
   - Support dynamic text sizing for accessibility settings
   - Improve screen reader compatibility

## Implementation Plan

### 1. Font Size Optimization

#### Minimum Font Sizes
- Body text: Increase minimum from 12px to 14px
- Input text: Ensure minimum 16px
- Labels: Increase minimum from 11px to 12px

```dart
// Update in text_styles.dart
static final TextStyle bodySmall = TextStyle(
  fontFamily: fontFamily,
  fontSize: 14, // Increased from 12
  fontWeight: FontWeight.w400,
  letterSpacing: 0.4,
  height: 1.33,
  color: AppColors.onBackground,
);

static final TextStyle labelSmall = TextStyle(
  fontFamily: fontFamily,
  fontSize: 12, // Increased from 11
  fontWeight: FontWeight.w500,
  letterSpacing: 0.5,
  height: 1.45,
  color: AppColors.onBackground,
);
```

#### Optimize Reading Text
- Increase font size for primary reading content to 16px
- Use appropriate scaling for headings based on this base size

### 2. Contrast Enhancement

#### Improve Text Contrast
- Audit all text colors against backgrounds to ensure WCAG AA compliance
- Enhance contrast in dark mode, especially for secondary and hint text

```dart
// Example updates for dark mode text
static final TextStyle bodyMediumDark = TextStyle(
  fontFamily: fontFamily,
  fontSize: 14,
  fontWeight: FontWeight.w400,
  letterSpacing: 0.25,
  height: 1.43,
  color: Colors.white.withOpacity(0.87), // Increased opacity for better contrast
);

// Update hint text in dark mode
labelStyle: AppTextStyles.bodyMedium.copyWith(
  color: AppColors.onSurfaceVariantDark.withOpacity(0.8) // Increased from 0.7
),
```

#### Implement High Contrast Mode
- Add a high contrast mode option for users with visual impairments
- Create alternative color schemes with enhanced contrast ratios

### 3. Line Height and Spacing Optimization

#### Adjust Line Heights
- Increase line height for body text to improve readability
- Recommended line heights:
  - Body text: 1.5-1.6
  - Headings: 1.2-1.3

```dart
// Update in text_styles.dart
static final TextStyle bodyLarge = TextStyle(
  fontFamily: fontFamily,
  fontSize: 16,
  fontWeight: FontWeight.w400,
  letterSpacing: 0.5,
  height: 1.6, // Increased from 1.5
  color: AppColors.onBackground,
);

static final TextStyle bodyMedium = TextStyle(
  fontFamily: fontFamily,
  fontSize: 14,
  fontWeight: FontWeight.w400,
  letterSpacing: 0.25,
  height: 1.6, // Increased from 1.43
  color: AppColors.onBackground,
);
```

#### Paragraph Spacing
- Add consistent paragraph spacing (1.5× the font size)
- Implement proper spacing between text blocks for better content scanning

### 4. Font Weight Distribution

#### Optimize Weight Hierarchy
- Use more distinct weight differences between headings and body text
- Recommended weights:
  - Headings: 700 (Bold)
  - Subheadings: 600 (Semi-bold)
  - Body: 400 (Regular)
  - Emphasis: 500 (Medium)

```dart
// Update in text_styles.dart
static final TextStyle titleMedium = TextStyle(
  fontFamily: fontFamily,
  fontSize: 16,
  fontWeight: FontWeight.w600, // Changed from 700 to 600
  letterSpacing: 0.15,
  height: 1.5,
  color: AppColors.onBackground,
);
```

#### Emphasize Important Information
- Use weight variations strategically to highlight important information
- Maintain consistent weight usage across similar UI elements

### 5. Text Truncation and Overflow

#### Implement Smart Truncation
- Create helper widgets for handling text overflow consistently
- Use ellipsis with tooltip for truncated text

```dart
// Example TruncatedText widget
class TruncatedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;

  const TruncatedText(this.text, {this.style, this.maxLines = 1});

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: text,
      child: Text(
        text,
        style: style,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
```

#### Long Text Handling
- Implement proper text wrapping for longer content
- Add "Read More" functionality for expandable text sections

### 6. Responsive Typography

#### Scale Text Based on Device Size
- Implement a responsive typography system that scales based on screen size
- Use MediaQuery to adjust text sizes on different devices

```dart
// Example responsive text size calculation
double getResponsiveFontSize(BuildContext context, double baseFontSize) {
  double screenWidth = MediaQuery.of(context).size.width;
  double scaleFactor = screenWidth / 375; // Base on iPhone 8 width
  return baseFontSize * min(scaleFactor, 1.2); // Cap scaling at 120%
}
```

#### Touch Targets
- Ensure interactive text elements have minimum 44×44 dp touch targets
- Add proper padding around clickable text

### 7. Accessibility Enhancements

#### Support Dynamic Text Sizing
- Respect system font size settings
- Implement scaling based on accessibility preferences

```dart
// Example of respecting system text scaling
MediaQuery(
  data: MediaQuery.of(context).copyWith(
    textScaleFactor: MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.3),
  ),
  child: child,
)
```

#### Screen Reader Support
- Add proper semantics labels for all text elements
- Ensure proper heading structure for screen readers

```dart
// Example of adding semantics
Semantics(
  label: 'Meter reading value: $meterReading',
  child: Text(
    '$meterReading',
    style: AppTextStyles.valueText,
  ),
)
```

## Implementation Priority

1. **High Priority**
   - Font size optimization for readability
   - Contrast enhancement for accessibility
   - Line height adjustments for better readability

2. **Medium Priority**
   - Font weight distribution improvements
   - Text truncation and overflow handling
   - Screen reader support

3. **Lower Priority**
   - Responsive typography implementation
   - Dynamic text sizing support
   - High contrast mode

## Testing Plan

1. **Readability Testing**
   - Test on various device sizes and screen resolutions
   - Verify readability in different lighting conditions

2. **Accessibility Testing**
   - Verify contrast ratios using automated tools
   - Test with screen readers
   - Test with different system font sizes

3. **User Testing**
   - Gather feedback on readability and visual hierarchy
   - Test with users of different age groups and visual abilities

## Conclusion

Implementing these typography enhancements will significantly improve the readability, accessibility, and overall user experience of the Lekky app. The changes will make the app more inclusive for users with different visual abilities while maintaining a clean and professional design aesthetic.

By prioritizing these improvements based on impact and implementation complexity, we can systematically enhance the text elements throughout the app, creating a more polished and user-friendly experience.
