# Backup Service

The Backup Service provides a modular, reusable solution for backing up and restoring meter entries in the Lekky app.

## Features

- Export meter entries to CSV files
- Import meter entries from CSV files
- Version identification for future compatibility
- Robust error handling
- User-friendly feedback

## CSV Format

The backup files use a standardized CSV format:

```
# Lekky v1.0.0 BackupFormat=100
Date,Type,Amount
2023-01-01 00:00:00,Meter Reading,100.0
2023-01-05 00:00:00,Top-up,50.0
```

- The first line contains version metadata
- The second line contains column headers
- Subsequent lines contain meter entries

## Usage

### Export Meter Entries

```dart
final backupService = BackupService();
final entries = await meterEntryRepository.getAllEntries();
final result = await backupService.exportMeterEntries(entries: entries);

result.fold(
  onSuccess: (file) {
    // Success - show confirmation message
    showSnackBar('Backup saved to lekky_export_100.csv');
  },
  onFailure: (error) {
    // Error - show error message
    showSnackBar(error.message);
  },
);
```

### Import Meter Entries

```dart
final backupService = BackupService();
final file = File('path/to/lekky_export_100.csv');
final result = await backupService.importMeterEntries(file);

result.fold(
  onSuccess: (entries) {
    // Success - save entries to database
    for (final entry in entries) {
      await meterEntryRepository.addEntry(entry);
    }
    showSnackBar('Backup loaded successfully: ${entries.length} entries imported');
  },
  onFailure: (error) {
    // Error - show error message
    showSnackBar(error.message);
  },
);
```

## Error Handling

The service returns a `Result<T, AppError>` type that can be used to handle success and failure cases:

- `fileIOError`: Error reading or writing files
- `parseError`: Error parsing backup data
- `versionMismatch`: Backup version mismatch
- `emptyData`: No data found in backup
- `unknown`: Unknown error

## Future Improvements

- Support for multiple backup files
- Backup encryption
- Cloud backup integration
- User settings backup
