// File: lib/features/history/presentation/widgets/entry_edit_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_dialog.dart';
import '../../../../core/widgets/app_text_field.dart';
import '../../../../core/widgets/dialogs/confirmation_dialog.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../../../core/widgets/dialogs/input_dialog.dart';
import '../controllers/history_controller.dart';

/// A dialog for editing a meter entry
class EntryEditDialog extends StatefulWidget {
  final MeterEntry? entry;
  final HistoryController controller;
  final Function(MeterEntry) onSave;
  final Function(int)? onDelete; // Add delete callback

  const EntryEditDialog({
    super.key,
    this.entry,
    required this.controller,
    required this.onSave,
    this.onDelete,
  });

  /// Show the dialog
  static Future<void> show({
    required BuildContext context,
    MeterEntry? entry,
    required HistoryController controller,
    required Function(MeterEntry) onSave,
    Function(int)? onDelete,
  }) async {
    return InputDialog.showCustomForm(
      context: context,
      title: entry != null ? 'Edit Entry' : 'Add Entry',
      widget: EntryEditDialog(
        entry: entry,
        controller: controller,
        onSave: onSave,
        onDelete: onDelete,
      ),
      icon: entry != null ? Icons.edit : Icons.add_circle,
    );
  }

  @override
  State<EntryEditDialog> createState() => _EntryEditDialogState();
}

class _EntryEditDialogState extends State<EntryEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _readingController;
  late TextEditingController _topUpController;
  late DateTime _selectedDate;
  late bool _isTopUp;
  String? _readingError;
  String? _topUpError;
  String? _dateError;

  @override
  void initState() {
    super.initState();
    _readingController =
        TextEditingController(text: widget.entry?.reading.toString() ?? '');
    _topUpController = TextEditingController(
        text: widget.entry?.amountToppedUp.toString() ?? '');

    // Use the existing timestamp or current date and time
    _selectedDate = widget.entry?.timestamp ?? DateTime.now();

    // Ensure the timestamp includes time, not just date
    if (_selectedDate.hour == 0 &&
        _selectedDate.minute == 0 &&
        _selectedDate.second == 0) {
      final now = DateTime.now();
      _selectedDate = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        now.hour,
        now.minute,
        now.second,
      );
    }

    _isTopUp = widget.entry?.amountToppedUp != null &&
        widget.entry!.amountToppedUp > 0;
  }

  @override
  void dispose() {
    _readingController.dispose();
    _topUpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEntryTypeSelector(),
          const SizedBox(height: 16),
          _buildDatePicker(),
          const SizedBox(height: 16),
          if (_isTopUp) _buildTopUpField() else _buildReadingField(),
          const SizedBox(height: 24),
          // Evenly spaced buttons in a row
          // Centered row of buttons with consistent sizing
          Container(
            alignment: Alignment.center,
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate button width based on available space
                // If we have delete button, divide by 3, otherwise by 2
                final hasDeleteButton = widget.entry != null &&
                    widget.entry!.id != null &&
                    widget.onDelete != null;
                final buttonCount = hasDeleteButton ? 3 : 2;
                final buttonWidth =
                    (constraints.maxWidth - (buttonCount - 1) * 8) /
                        buttonCount;

                // Common button style properties
                const buttonHeight = 40.0;
                const buttonPadding =
                    EdgeInsets.symmetric(horizontal: 8, vertical: 0);
                const buttonTextStyle = TextStyle(fontSize: 14);

                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Delete button - only show for existing entries
                    if (hasDeleteButton) ...[
                      SizedBox(
                        width: buttonWidth,
                        height: buttonHeight,
                        child: ElevatedButton(
                          onPressed: () {
                            // Show confirmation dialog using our new ConfirmationDialog class
                            ConfirmationDialog.show(
                              context: context,
                              title: 'Delete Entry',
                              message:
                                  'Are you sure you want to delete this entry? This action cannot be undone.',
                              confirmText: 'Delete',
                              cancelText: 'Cancel',
                              isDestructive: true,
                              icon: Icons.delete_forever,
                            ).then((confirmed) {
                              if (confirmed == true && mounted) {
                                // Store the entry ID
                                final int entryId = widget.entry!.id!;

                                // Close the edit dialog first
                                Navigator.of(context).pop();

                                // Then call the delete callback
                                widget.onDelete!(entryId);
                              }
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: buttonPadding,
                            minimumSize: Size(buttonWidth, buttonHeight),
                          ),
                          child: const Text(
                            'Delete',
                            textAlign: TextAlign.center,
                            style: buttonTextStyle,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8), // Spacing between buttons
                    ],

                    // Cancel button
                    SizedBox(
                      width: buttonWidth,
                      height: buttonHeight,
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: const BorderSide(color: AppColors.primary),
                          padding: buttonPadding,
                          minimumSize: Size(buttonWidth, buttonHeight),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Cancel',
                          textAlign: TextAlign.center,
                          style: buttonTextStyle,
                        ),
                      ),
                    ),

                    const SizedBox(width: 8), // Spacing between buttons

                    // Save button
                    SizedBox(
                      width: buttonWidth,
                      height: buttonHeight,
                      child: ElevatedButton(
                        onPressed: _validateAndSave,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: buttonPadding,
                          minimumSize: Size(buttonWidth, buttonHeight),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Save',
                          textAlign: TextAlign.center,
                          style: buttonTextStyle,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntryTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Entry Type',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        SegmentedButton<bool>(
          segments: const [
            ButtonSegment<bool>(
              value: false,
              label: Text('Meter Reading'),
              icon: Icon(Icons.remove_circle),
            ),
            ButtonSegment<bool>(
              value: true,
              label: Text('Top-up'),
              icon: Icon(Icons.add_circle),
            ),
          ],
          selected: {_isTopUp},
          onSelectionChanged: (Set<bool> selected) {
            setState(() {
              _isTopUp = selected.first;
              // Clear errors when switching types
              _readingError = null;
              _topUpError = null;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDate(context),
          child: InputDecorator(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              errorText: _dateError,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateTimeUtils.formatDateWithTime(_selectedDate),
                  style: AppTextStyles.bodyMedium,
                ),
                const Icon(Icons.calendar_today),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReadingField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Meter Reading',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _readingController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
          ],
          errorText: _readingError,
          prefixIcon: Icon(
            Icons.electric_meter,
            color: Theme.of(context).colorScheme.primary,
          ),
          prefixText: widget.controller.meterUnit,
          hintText: '0.00',
        ),
      ],
    );
  }

  Widget _buildTopUpField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Top-up Amount',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _topUpController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
          ],
          errorText: _topUpError,
          prefixIcon: const Icon(
            Icons.add_circle,
            color: AppColors.success,
          ),
          prefixText: widget.controller.meterUnit,
          hintText: '0.00',
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (!mounted) return;

    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    // Use our DatePickerDialog class
    DateTime? pickedDateTime;

    if (includeTime) {
      // Show date and time picker if Date Info is set to "Date and time"
      pickedDateTime =
          await custom_date_picker.DatePickerDialog.showDateTimePicker(
        context: context,
        title: 'Select Date and Time',
        initialDate: _selectedDate,
        firstDate: DateTime(2000),
        lastDate: DateTime.now(),
        currentTime: TimeOfDay.fromDateTime(_selectedDate),
        includeTime: true,
        addCurrentSecond: true, // Add current second to avoid duplicates
      );
    } else {
      // Show only date picker if Date Info is set to "Date only"
      final pickedDate =
          await custom_date_picker.DatePickerDialog.showDatePicker(
        context: context,
        title: 'Select Date',
        initialDate: _selectedDate,
        firstDate: DateTime(2000),
        lastDate: DateTime.now(),
      );

      if (pickedDate != null) {
        // Use current time when Date Info is set to "Date only"
        final now = DateTime.now();
        pickedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          now.hour,
          now.minute,
          now.second,
        );
      }
    }

    if (pickedDateTime != null && mounted) {
      setState(() {
        _selectedDate = pickedDateTime!;
        _dateError = null;
      });
    }
  }

  void _validateAndSave() async {
    // Reset errors
    setState(() {
      _readingError = null;
      _topUpError = null;
      _dateError = null;
    });

    // Validate date
    final dateValidation = InputValidator.validateDate(_selectedDate);
    if (!dateValidation['isValid']) {
      setState(() {
        _dateError = dateValidation['errorMessage'];
      });
      return;
    }

    if (_isTopUp) {
      // Validate top-up amount
      final topUpValidation =
          InputValidator.validateTopUpAmount(_topUpController.text);
      if (!topUpValidation['isValid']) {
        setState(() {
          _topUpError = topUpValidation['errorMessage'];
        });
        return;
      }

      // Create a new top-up entry (without the original ID)
      final newEntry = MeterEntry(
        reading: 0,
        amountToppedUp: double.parse(_topUpController.text),
        timestamp: _selectedDate,
      );

      // Save the new entry
      widget.onSave(newEntry);

      // If we're editing an existing entry, delete the old one
      if (widget.entry != null &&
          widget.entry!.id != null &&
          widget.onDelete != null) {
        widget.onDelete!(widget.entry!.id!);
      }

      Navigator.of(context).pop();
    } else {
      // Validate meter reading format first
      final readingValidation =
          InputValidator.validateMeterReading(_readingController.text);
      if (!readingValidation['isValid']) {
        setState(() {
          _readingError = readingValidation['errorMessage'];
        });
        return;
      }

      // Create a new meter reading entry (without the original ID)
      final newEntry = MeterEntry(
        reading: double.parse(_readingController.text),
        amountToppedUp: 0,
        timestamp: _selectedDate,
      );

      // Validate the reading against existing entries
      final validationResult = await widget.controller.validateEntry(newEntry);
      if (!validationResult.isValid) {
        // Show a more specific error message
        setState(() {
          _readingError = validationResult.errorMessage;
        });

        // Show a dialog with more detailed information
        if (mounted) {
          await AppDialog.show(
            context: context,
            title: 'Validation Warning',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.warning,
                  color: Colors.amber,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  validationResult.errorMessage ?? 'Invalid entry',
                  style: AppTextStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Text(
                  'You can still save this entry, but it may cause inconsistencies in your data.',
                  style: AppTextStyles.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue,
                  side: const BorderSide(color: Colors.blue),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Save Anyway'),
              ),
            ],
          ).then((saveAnyway) {
            if (saveAnyway == true && mounted) {
              // Save the entry despite validation warnings
              widget.onSave(newEntry);

              // If we're editing an existing entry, delete the old one
              if (widget.entry != null &&
                  widget.entry!.id != null &&
                  widget.onDelete != null) {
                widget.onDelete!(widget.entry!.id!);
              }

              Navigator.of(context).pop();
            }
          });
          return;
        }
      } else {
        // Entry is valid, save it
        widget.onSave(newEntry);

        // If we're editing an existing entry, delete the old one
        if (widget.entry != null &&
            widget.entry!.id != null &&
            widget.onDelete != null) {
          widget.onDelete!(widget.entry!.id!);
        }

        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    }
  }
}
