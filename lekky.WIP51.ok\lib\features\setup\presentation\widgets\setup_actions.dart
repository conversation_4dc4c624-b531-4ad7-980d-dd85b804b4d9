// File: lib/features/setup/presentation/widgets/setup_actions.dart
import 'package:flutter/material.dart';
import '../../../../core/widgets/gradient_button.dart';

/// A widget for setup actions (save, reset, and load)
class SetupActions extends StatelessWidget {
  final VoidCallback onSave;
  final VoidCallback onReset;
  final VoidCallback? onLoad;
  final bool isSaving;
  final bool isInitialSetup;

  const SetupActions({
    super.key,
    required this.onSave,
    required this.onReset,
    this.onLoad,
    this.isSaving = false,
    this.isInitialSetup = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          GradientButton(
            text: isSaving ? 'Saving...' : 'Continue',
            onPressed: isSaving ? null : onSave,
            isLoading: isSaving,
            gradientColors: const [
              Color(0xFF1976D2),
              Color(0xFF42A5F5)
            ], // Primary color from lekky_pallet.md
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            width: double.infinity,
          ),
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: isSaving ? null : onReset,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              minimumSize: const Size(double.infinity, 0),
            ),
            child: const Text('Reset to Default'),
          ),
        ],
      ),
    );
  }
}
