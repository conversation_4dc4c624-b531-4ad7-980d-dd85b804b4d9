// File: lib/core/utils/responsive_text.dart
import 'package:flutter/material.dart';
import 'responsive_layout.dart';

/// Utility class for responsive text
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final bool softWrap;
  final double? textScaleFactor;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final double? largeDesktopFontSize;

  const ResponsiveText(
    this.text, {
    Key? key,
    this.style,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap = true,
    this.textScaleFactor,
    this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.largeDesktopFontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final baseStyle = style ?? Theme.of(context).textTheme.bodyMedium;
    final baseFontSize = mobileFontSize ?? baseStyle?.fontSize ?? 14;

    final responsiveFontSize = ResponsiveLayout.getValueForScreenType(
      context: context,
      mobile: baseFontSize,
      tablet: tabletFontSize ?? baseFontSize * 1.1,
      desktop: desktopFontSize ?? baseFontSize * 1.2,
      largeDesktop: largeDesktopFontSize ?? baseFontSize * 1.3,
    );

    final responsiveStyle = baseStyle?.copyWith(fontSize: responsiveFontSize);

    return Text(
      text,
      style: responsiveStyle,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      softWrap: softWrap,
      textScaleFactor: textScaleFactor,
    );
  }
}

/// Extension on Text widget to create a responsive version
extension ResponsiveTextExtension on Text {
  /// Creates a responsive version of this Text widget
  ResponsiveText responsive({
    double? mobileFontSize,
    double? tabletFontSize,
    double? desktopFontSize,
    double? largeDesktopFontSize,
  }) {
    return ResponsiveText(
      data ?? '',
      style: style,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      softWrap: softWrap ?? true,
      textScaleFactor: textScaleFactor,
      mobileFontSize: mobileFontSize,
      tabletFontSize: tabletFontSize,
      desktopFontSize: desktopFontSize,
      largeDesktopFontSize: largeDesktopFontSize,
    );
  }
}
