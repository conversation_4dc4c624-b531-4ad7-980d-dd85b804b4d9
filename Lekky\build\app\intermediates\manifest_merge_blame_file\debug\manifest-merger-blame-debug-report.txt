1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for exact alarms (Android 12+ / API 31+) -->
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> <!-- Add permission for boot completed to reschedule notifications -->
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-78
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-76
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for background work -->
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-80
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-78
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:5-67
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-76
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" /> <!-- Add permission to ignore battery optimizations -->
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-86
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:22-84
23    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- Add permission for alarm clock notifications -->
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:5-94
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:22-92
24    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- Storage permissions for file export -->
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:5-73
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:22-71
25    <uses-permission
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:5-21:38
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:9-64
27        android:maxSdkVersion="32" />
27-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:9-35
28    <uses-permission
28-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:22:5-24:38
29        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
29-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:23:9-65
30        android:maxSdkVersion="29" /> <!-- Android 13+ (API 33+) granular media permissions -->
30-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:24:9-35
31    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
31-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:5-76
31-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
32-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:5-75
32-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:22-72
33    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- Android 14+ (API 34+) partial media access -->
33-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:5-75
33-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:22-72
34    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:5-90
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:22-87
35    <!--
36 Required to query activities that can process text, see:
37         https://developer.android.com/training/package-visibility?hl=en and
38         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
39
40         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
41    -->
42    <queries>
42-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:99:5-116:15
43        <intent>
43-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:100:9-103:18
44            <action android:name="android.intent.action.PROCESS_TEXT" />
44-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:101:13-72
44-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:101:21-70
45
46            <data android:mimeType="text/plain" />
46-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
46-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
47        </intent>
48        <!-- For document selection -->
49        <intent>
49-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:105:9-107:18
50            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:106:13-79
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:106:21-76
51        </intent>
52        <intent>
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:108:9-111:18
53            <action android:name="android.intent.action.CREATE_DOCUMENT" />
53-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:109:13-76
53-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:109:21-73
54
55            <data android:mimeType="text/csv" />
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
56        </intent>
57        <intent>
57-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:112:9-115:18
58            <action android:name="android.intent.action.OPEN_DOCUMENT" />
58-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:13-74
58-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:21-71
59
60            <data android:mimeType="text/csv" />
60-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
60-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
61        </intent>
62        <intent>
62-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
63            <action android:name="android.intent.action.GET_CONTENT" />
63-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
63-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
64
65            <data android:mimeType="*/*" />
65-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
65-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
66        </intent>
67    </queries>
68
69    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
69-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-79
69-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-76
70    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
70-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
70-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
71    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
71-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
71-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
72
73    <permission
73-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
74        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
74-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
75        android:protectionLevel="signature" />
75-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
76
77    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
77-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
77-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
78    <!--
79 Note: For Android 10+ (API 29+), we use Storage Access Framework (SAF)
80         which doesn't require additional permissions for user-selected locations
81    -->
82    <application
83        android:name="android.app.Application"
84        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
84-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
85        android:debuggable="true"
86        android:enableOnBackInvokedCallback="true"
87        android:icon="@mipmap/ic_launcher"
88        android:label="lekky"
89        android:requestLegacyExternalStorage="true" >
90        <activity
91            android:name="com.lekky.app.MainActivity"
92            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
93            android:exported="true"
94            android:hardwareAccelerated="true"
95            android:launchMode="singleTop"
96            android:theme="@style/LaunchTheme"
97            android:windowSoftInputMode="adjustResize" >
98
99            <!--
100                 Specifies an Android theme to apply to this Activity as soon as
101                 the Android process has started. This theme is visible to the user
102                 while the Flutter UI initializes. After that, this theme continues
103                 to determine the Window background behind the Flutter UI.
104            -->
105            <meta-data
106                android:name="io.flutter.embedding.android.NormalTheme"
107                android:resource="@style/NormalTheme" />
108
109            <intent-filter>
110                <action android:name="android.intent.action.MAIN" />
111
112                <category android:name="android.intent.category.LAUNCHER" />
113            </intent-filter>
114        </activity>
115        <!--
116             Don't delete the meta-data below.
117             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
118        -->
119        <meta-data
120            android:name="flutterEmbedding"
121            android:value="2" />
122
123        <!-- Add receiver for boot completed to reschedule notifications -->
124        <receiver
125            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
126            android:exported="true" >
127            <intent-filter>
128                <action android:name="android.intent.action.BOOT_COMPLETED" />
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
129                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
130            </intent-filter>
131        </receiver>
132
133        <!-- Foreground service for reliable notifications -->
134        <service
135            android:name="com.lekky.app.NotificationForegroundService"
136            android:enabled="true"
137            android:exported="false"
138            android:foregroundServiceType="dataSync" />
139
140        <!-- Package name for the application -->
141        <meta-data
142            android:name="com.lekky.app.PACKAGE_NAME"
143            android:value="com.lekky.app" />
144
145        <service
145-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-19:72
146            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
146-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-107
147            android:exported="false"
147-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-69
149        <service
149-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-26:19
150            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
150-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-97
151            android:exported="false" >
151-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
152            <intent-filter>
152-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
153                <action android:name="com.google.firebase.MESSAGING_EVENT" />
153-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
153-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
154            </intent-filter>
155        </service>
156
157        <receiver
157-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-35:20
158            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
158-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-98
159            android:exported="true"
159-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-36
160            android:permission="com.google.android.c2dm.permission.SEND" >
160-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-73
161            <intent-filter>
161-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
162                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
162-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
162-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
163            </intent-filter>
164        </receiver>
165
166        <service
166-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-41:19
167            android:name="com.google.firebase.components.ComponentDiscoveryService"
167-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:18-89
168            android:directBootAware="true"
168-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
169            android:exported="false" >
169-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
170            <meta-data
170-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-40:85
171                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
171-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:39:17-128
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:40:17-82
173            <meta-data
173-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-13:85
174                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
174-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:12:17-124
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:13:17-82
176            <meta-data
176-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
177                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
177-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
179            <meta-data
179-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
180                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
180-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
182            <meta-data
182-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
183                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
183-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
185            <meta-data
185-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
186                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
186-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
188            <meta-data
188-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
189                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
189-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
191            <meta-data
191-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
192                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
192-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
194            <meta-data
194-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
195                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
195-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
197        </service>
198
199        <provider
199-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:9-47:38
200            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
200-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-102
201            android:authorities="com.lekky.app.flutterfirebasemessaginginitprovider"
201-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-88
202            android:exported="false"
202-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-37
203            android:initOrder="99" />
203-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-35
204
205        <activity
205-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
206            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
206-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
207            android:exported="false"
207-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
208            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
208-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
209
210        <receiver
210-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
211            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
211-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
212            android:exported="true"
212-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
213            android:permission="com.google.android.c2dm.permission.SEND" >
213-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
214            <intent-filter>
214-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
215                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
215-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
215-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
216            </intent-filter>
217
218            <meta-data
218-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
219                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
219-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
220                android:value="true" />
220-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
221        </receiver>
222        <!--
223             FirebaseMessagingService performs security checks at runtime,
224             but set to not exported to explicitly avoid allowing another app to call it.
225        -->
226        <service
226-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
227            android:name="com.google.firebase.messaging.FirebaseMessagingService"
227-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
228            android:directBootAware="true"
228-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
229            android:exported="false" >
229-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
230            <intent-filter android:priority="-500" >
230-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
231                <action android:name="com.google.firebase.MESSAGING_EVENT" />
231-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
231-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
232            </intent-filter>
233        </service>
234
235        <provider
235-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
236            android:name="com.google.firebase.provider.FirebaseInitProvider"
236-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
237            android:authorities="com.lekky.app.firebaseinitprovider"
237-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
238            android:directBootAware="true"
238-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
239            android:exported="false"
239-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
240            android:initOrder="100" />
240-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
241
242        <uses-library
242-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
243            android:name="androidx.window.extensions"
243-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
244            android:required="false" />
244-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
245        <uses-library
245-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
246            android:name="androidx.window.sidecar"
246-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
247            android:required="false" />
247-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
248
249        <provider
249-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
250            android:name="androidx.startup.InitializationProvider"
250-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
251            android:authorities="com.lekky.app.androidx-startup"
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
253            <meta-data
253-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
254                android:name="androidx.work.WorkManagerInitializer"
254-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
255                android:value="androidx.startup" />
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
256        </provider>
257
258        <service
258-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
259            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
259-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
261            android:enabled="@bool/enable_system_alarm_service_default"
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
262            android:exported="false" />
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
263        <service
263-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
264            android:name="androidx.work.impl.background.systemjob.SystemJobService"
264-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
265            android:directBootAware="false"
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
266            android:enabled="@bool/enable_system_job_service_default"
266-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
267            android:exported="true"
267-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
268            android:permission="android.permission.BIND_JOB_SERVICE" />
268-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
269        <service
269-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
270            android:name="androidx.work.impl.foreground.SystemForegroundService"
270-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
272            android:enabled="@bool/enable_system_foreground_service_default"
272-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
273            android:exported="false" />
273-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
274
275        <receiver
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
276            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
278            android:enabled="true"
278-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
279            android:exported="false" />
279-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
280        <receiver
280-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
281-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
286                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
287                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
288            </intent-filter>
289        </receiver>
290        <receiver
290-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
291            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
291-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
293            android:enabled="false"
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
294            android:exported="false" >
294-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
295            <intent-filter>
295-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
296                <action android:name="android.intent.action.BATTERY_OKAY" />
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
297                <action android:name="android.intent.action.BATTERY_LOW" />
297-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
297-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
301            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
301-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
303            android:enabled="false"
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
305            <intent-filter>
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
306                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
307                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
308            </intent-filter>
309        </receiver>
310        <receiver
310-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
311            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
311-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
313            android:enabled="false"
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
314            android:exported="false" >
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
315            <intent-filter>
315-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
316                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
320            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
322            android:enabled="false"
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
323            android:exported="false" >
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
324            <intent-filter>
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
325                <action android:name="android.intent.action.BOOT_COMPLETED" />
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
326                <action android:name="android.intent.action.TIME_SET" />
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
327                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
327-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
327-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
331            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
331-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
333            android:enabled="@bool/enable_system_alarm_service_default"
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
334            android:exported="false" >
334-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
335            <intent-filter>
335-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
336                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
336-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
336-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
337            </intent-filter>
338        </receiver>
339        <receiver
339-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
340            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
340-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
341            android:directBootAware="false"
341-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
342            android:enabled="true"
342-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
343            android:exported="true"
343-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
344            android:permission="android.permission.DUMP" >
344-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
345            <intent-filter>
345-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
346                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
347            </intent-filter>
348        </receiver>
349
350        <service
350-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
351            android:name="androidx.room.MultiInstanceInvalidationService"
351-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
352            android:directBootAware="true"
352-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
353            android:exported="false" />
353-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
354
355        <activity
355-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
356            android:name="com.google.android.gms.common.api.GoogleApiActivity"
356-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
357            android:exported="false"
357-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
358            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
358-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
359
360        <meta-data
360-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
361            android:name="com.google.android.gms.version"
361-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
362            android:value="@integer/google_play_services_version" />
362-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
363
364        <service
364-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
365            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
365-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
366            android:exported="false" >
366-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
367            <meta-data
367-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
368                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
368-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
369                android:value="cct" />
369-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
370        </service>
371        <service
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
372            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
372-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
373            android:exported="false"
373-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
374            android:permission="android.permission.BIND_JOB_SERVICE" >
374-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
375        </service>
376
377        <receiver
377-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
378            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
378-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
379            android:exported="false" />
379-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
380    </application>
381
382</manifest>
