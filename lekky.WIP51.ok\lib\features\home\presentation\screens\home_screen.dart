import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/widgets/home_notification_button.dart';
import '../../../../core/widgets/home_settings_button.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/models/notification_model.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../history/presentation/controllers/history_controller.dart';
import '../../../history/presentation/widgets/entry_edit_dialog.dart';
import '../controllers/home_controller.dart';
import '../widgets/meter_info_card.dart';
import '../widgets/meter_value_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Index for the current message to display
  int _currentMessageIndex = 0;

  @override
  void initState() {
    super.initState();

    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeController>().init();

      // Add a test notification for testing the notification dialog
      final notificationProvider =
          Provider.of<NotificationProvider>(context, listen: false);
      notificationProvider.addNotification(
        title: 'Test Notification',
        message:
            'This is a test notification to verify the notification dialog functionality.',
        priority: NotificationPriority.info,
      );
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
      // Removed FloatingActionButton as it's redundant with the green Add Entry button
    );
  }

  Widget _buildBody() {
    return Consumer<HomeController>(
      builder: (context, controller, _) {
        return RefreshIndicator(
          onRefresh: controller.refresh,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    MeterValueCard(
                      meterTotal: controller.formattedMeterTotal,
                      averageUsage: controller.formattedAverageUsage,
                      shortTermAverageUsage:
                          controller.formattedShortTermAverageUsage,
                      isLoading: controller.isLoading,
                    ),
                    const SizedBox(height: 16),
                    MeterInfoCard(
                      lastReadingDate: controller.formattedLastReadingDate,
                      dateToTopUp: controller.formattedDateToTopUp,
                      isLoading: controller.isLoading,
                    ),
                    const SizedBox(height: 24),
                    // Custom row layout to align buttons with card edges above
                    LayoutBuilder(
                      builder: (context, constraints) {
                        // Make button width match the Meter Total dialog box
                        // Card width = constraints.maxWidth (full width of SliverPadding)
                        final buttonWidth = constraints.maxWidth -
                            16; // Full width minus padding

                        return Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8), // Match card margin
                          child: Center(
                            // Center the Add Entry button
                            child: SizedBox(
                              width: buttonWidth,
                              child: GradientButton(
                                text: 'Add Entry', // Text on one line
                                icon: const Icon(
                                  Icons.add,
                                  color: Colors.white,
                                  size: 24, // Slightly larger icon
                                ),
                                height: 80, // Taller button
                                gradientColors: const [
                                  Color(0xFF43E97B),
                                  Color(0xFF38F9D7)
                                ], // Green gradient
                                onPressed: () => _showEntryEditDialog(null),
                                textStyle: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24, // Increased text size
                                  letterSpacing: 0.5, // Better letter spacing
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    if (controller.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          controller.error,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ]),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showEntryEditDialog(MeterEntry? entry) {
    final historyController = context.read<HistoryController>();
    EntryEditDialog.show(
      context: context,
      controller: historyController,
      entry: entry,
      onSave: (meterEntry) {
        historyController.addEntry(meterEntry);
      },
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return Container(
      color: Colors.grey[200],
      height: 32, // Reduced height to 32px as requested
      child: Center(
        // Ensure text is perfectly centered
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            HelpfulMessages.allMessages[_currentMessageIndex],
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.black87,
              height: 1.0, // Ensure single line height
              fontSize: 13.0, // Slightly smaller font size to fit more text
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  // Build the app bar with the message bar positioned directly below it
  Widget _buildAppBar() {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px for the banner
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use a Stack for the banner content
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF0D47A1),
                      Color(0xFF1976D2)
                    ], // Lekky Meter banner blue
                  ),
                ),
              ),
            ),
            // Custom positioned title - aligned with Meter Total dialog box
            const Positioned(
              top: 20, // Moved up by 4px as requested
              left:
                  20, // Exactly 20px from left edge to match Meter Total dialog box
              child: Text(
                'Lekky',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Doubled font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            // Positioned row of icons to match History screen layout
            const Positioned(
              top: 10, // Same top position as History icons
              right: 12, // Same right position as History icons
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Settings button - positioned to match edit icon on History screen
                  HomeSettingsButton(),
                  // Notification button - positioned to match info icon on History screen
                  HomeNotificationButton(),
                ],
              ),
            ),
          ],
        ),
      ),
      // No actions - notification button is now in the Stack
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }
}
