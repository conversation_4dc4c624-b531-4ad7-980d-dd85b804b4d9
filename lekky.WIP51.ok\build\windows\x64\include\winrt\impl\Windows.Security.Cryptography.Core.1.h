// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Security_Cryptography_Core_1_H
#define WINRT_Windows_Security_Cryptography_Core_1_H
#include "winrt/impl/Windows.Security.Cryptography.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Cryptography::Core
{
    struct __declspec(empty_bases) IAsymmetricAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAsymmetricAlgorithmNamesStatics>
    {
        IAsymmetricAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IAsymmetricAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAsymmetricAlgorithmNamesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAsymmetricAlgorithmNamesStatics2>
    {
        IAsymmetricAlgorithmNamesStatics2(std::nullptr_t = nullptr) noexcept {}
        IAsymmetricAlgorithmNamesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAsymmetricKeyAlgorithmProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAsymmetricKeyAlgorithmProvider>
    {
        IAsymmetricKeyAlgorithmProvider(std::nullptr_t = nullptr) noexcept {}
        IAsymmetricKeyAlgorithmProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAsymmetricKeyAlgorithmProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAsymmetricKeyAlgorithmProvider2>
    {
        IAsymmetricKeyAlgorithmProvider2(std::nullptr_t = nullptr) noexcept {}
        IAsymmetricKeyAlgorithmProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAsymmetricKeyAlgorithmProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAsymmetricKeyAlgorithmProviderStatics>
    {
        IAsymmetricKeyAlgorithmProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IAsymmetricKeyAlgorithmProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICryptographicEngineStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICryptographicEngineStatics>
    {
        ICryptographicEngineStatics(std::nullptr_t = nullptr) noexcept {}
        ICryptographicEngineStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICryptographicEngineStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICryptographicEngineStatics2>
    {
        ICryptographicEngineStatics2(std::nullptr_t = nullptr) noexcept {}
        ICryptographicEngineStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICryptographicKey :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICryptographicKey>
    {
        ICryptographicKey(std::nullptr_t = nullptr) noexcept {}
        ICryptographicKey(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEccCurveNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEccCurveNamesStatics>
    {
        IEccCurveNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IEccCurveNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEncryptedAndAuthenticatedData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEncryptedAndAuthenticatedData>
    {
        IEncryptedAndAuthenticatedData(std::nullptr_t = nullptr) noexcept {}
        IEncryptedAndAuthenticatedData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHashAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHashAlgorithmNamesStatics>
    {
        IHashAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IHashAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHashAlgorithmProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHashAlgorithmProvider>
    {
        IHashAlgorithmProvider(std::nullptr_t = nullptr) noexcept {}
        IHashAlgorithmProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHashAlgorithmProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHashAlgorithmProviderStatics>
    {
        IHashAlgorithmProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IHashAlgorithmProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHashComputation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHashComputation>
    {
        IHashComputation(std::nullptr_t = nullptr) noexcept {}
        IHashComputation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationAlgorithmNamesStatics>
    {
        IKeyDerivationAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationAlgorithmNamesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationAlgorithmNamesStatics2>
    {
        IKeyDerivationAlgorithmNamesStatics2(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationAlgorithmNamesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationAlgorithmProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationAlgorithmProvider>
    {
        IKeyDerivationAlgorithmProvider(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationAlgorithmProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationAlgorithmProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationAlgorithmProviderStatics>
    {
        IKeyDerivationAlgorithmProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationAlgorithmProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationParameters>
    {
        IKeyDerivationParameters(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationParameters2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationParameters2>
    {
        IKeyDerivationParameters2(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationParameters2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationParametersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationParametersStatics>
    {
        IKeyDerivationParametersStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationParametersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKeyDerivationParametersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyDerivationParametersStatics2>
    {
        IKeyDerivationParametersStatics2(std::nullptr_t = nullptr) noexcept {}
        IKeyDerivationParametersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMacAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMacAlgorithmNamesStatics>
    {
        IMacAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IMacAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMacAlgorithmProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMacAlgorithmProvider>
    {
        IMacAlgorithmProvider(std::nullptr_t = nullptr) noexcept {}
        IMacAlgorithmProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMacAlgorithmProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMacAlgorithmProvider2>
    {
        IMacAlgorithmProvider2(std::nullptr_t = nullptr) noexcept {}
        IMacAlgorithmProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMacAlgorithmProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMacAlgorithmProviderStatics>
    {
        IMacAlgorithmProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IMacAlgorithmProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersistedKeyProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersistedKeyProviderStatics>
    {
        IPersistedKeyProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IPersistedKeyProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymmetricAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymmetricAlgorithmNamesStatics>
    {
        ISymmetricAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        ISymmetricAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymmetricKeyAlgorithmProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymmetricKeyAlgorithmProvider>
    {
        ISymmetricKeyAlgorithmProvider(std::nullptr_t = nullptr) noexcept {}
        ISymmetricKeyAlgorithmProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISymmetricKeyAlgorithmProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISymmetricKeyAlgorithmProviderStatics>
    {
        ISymmetricKeyAlgorithmProviderStatics(std::nullptr_t = nullptr) noexcept {}
        ISymmetricKeyAlgorithmProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
