// File: lib/features/history/presentation/widgets/fixed_header_history_table.dart
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../controllers/history_controller.dart';

/// A table with a fixed header that displays meter entries
/// The table width matches the Meter Total dialog on the Homepage
/// The table header has the same height as the top of the Meter Total dialog
class FixedHeaderHistoryTable extends StatefulWidget {
  final List<MeterEntry> entries;
  final HistoryController controller;
  final Function(MeterEntry) onEntryTap;
  final Function(MeterEntry)? onInvalidEntryTap;
  final ScrollController?
      scrollController; // External scroll controller for syncing

  const FixedHeaderHistoryTable({
    super.key,
    required this.entries,
    required this.controller,
    required this.onEntryTap,
    this.onInvalidEntryTap,
    this.scrollController, // Optional - will be used to sync scrolling
  });

  @override
  State<FixedHeaderHistoryTable> createState() =>
      _FixedHeaderHistoryTableState();
}

class _FixedHeaderHistoryTableState extends State<FixedHeaderHistoryTable> {
  // Always use our own internal scroll controller
  final ScrollController _internalScrollController = ScrollController();

  // Constants for table dimensions
  static const double headerHeight = 60.0; // Height for header cells
  static const double rowHeight = 60.0; // Height for data rows

  @override
  void initState() {
    super.initState();

    // If external controller is provided, sync with it
    if (widget.scrollController != null) {
      // Listen to our internal controller and update the external one
      _internalScrollController.addListener(_syncScrollControllers);
    }
  }

  // Sync the scroll position between internal and external controllers
  void _syncScrollControllers() {
    // We don't need to do anything here since we're using our own internal controller
    // and the parent is using ValueNotifiers to track scroll position

    // In a real app, you might use a custom notification or a callback
    // to notify the parent about the scroll position
  }

  @override
  void dispose() {
    // Always dispose our internal controller
    _internalScrollController.removeListener(_syncScrollControllers);
    _internalScrollController.dispose();

    super.dispose();
  }

  List<MeterEntry> get _currentPageEntries {
    final startIndex =
        widget.controller.currentPage * widget.controller.itemsPerPage;
    final endIndex =
        (startIndex + widget.controller.itemsPerPage <= widget.entries.length)
            ? startIndex + widget.controller.itemsPerPage
            : widget.entries.length;

    if (startIndex >= widget.entries.length) {
      return [];
    }

    return widget.entries.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.entries.isEmpty) {
      return const Center(
        child: Text('No entries found'),
      );
    }

    return Column(
      children: [
        _buildTable(),
        const SizedBox(height: 16),
        // We'll move the pagination controls to the History screen
      ],
    );
  }

  Widget _buildTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use the available width from the parent, but constrain it to match the Meter Total dialog
        // The parent already has 16px padding on each side from SliverPadding in the history_screen.dart
        // We need to add 4px padding on each side to match the cost dialog box spacing
        final tableWidth =
            constraints.maxWidth - 8; // Subtract 8px (4px on each side)

        return Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: 4), // Add 4px padding on each side
          child: Stack(
            children: [
              // Main card with table - match the Meter Total dialog styling
              Card(
                elevation: 2,
                margin: EdgeInsets
                    .zero, // Remove margin to match Meter Total dialog
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: widget.controller.isEditMode
                      ? const BorderSide(color: AppColors.warning, width: 2)
                      : BorderSide.none,
                ),
                child: SizedBox(
                  width: tableWidth,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Edit mode notification removed as requested
                      // Fixed header
                      Container(
                        height: headerHeight,
                        decoration: const BoxDecoration(
                          color: Color(0xFFE1E1E1), // Light gray header
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          children: _buildHeaderCells(tableWidth),
                        ),
                      ),
                      // Scrollable content
                      Container(
                        // Use a fixed height that's large enough to show several rows
                        // but small enough to fit on most screens
                        height: math.min(
                          rowHeight * _currentPageEntries.length,
                          MediaQuery.of(context).size.height *
                              0.5, // Use 50% of screen height max
                        ),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12),
                            bottomRight: Radius.circular(12),
                          ),
                        ),
                        child: GestureDetector(
                          onHorizontalDragEnd: (details) {
                            final currentPage = widget.controller.currentPage;
                            final totalPages = widget.controller.totalPages;

                            // Swipe left to go to next page
                            if (details.primaryVelocity != null &&
                                details.primaryVelocity! < 0) {
                              if (currentPage < totalPages - 1) {
                                widget.controller.goToPage(currentPage + 1);
                                // Reset scroll position
                                _internalScrollController.jumpTo(0);
                              }
                            }
                            // Swipe right to go to previous page
                            else if (details.primaryVelocity != null &&
                                details.primaryVelocity! > 0) {
                              if (currentPage > 0) {
                                widget.controller.goToPage(currentPage - 1);
                                // Reset scroll position
                                _internalScrollController.jumpTo(0);
                              }
                            }
                          },
                          child: Stack(
                            children: [
                              // Table content
                              SingleChildScrollView(
                                controller: _internalScrollController,
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: Column(
                                  children: _buildRows(tableWidth),
                                ),
                              ),

                              // Remove internal parallax line since we're using an external one
                              // in the HistoryScreen
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Warning icons are now inside the rows
            ],
          ),
        );
      },
    );
  }

  List<Widget> _buildHeaderCells(double tableWidth) {
    // Calculate column widths based on the table width
    // Date column takes 41% of the width, Amount 22%, others 18% each, with 1% spacer
    // These percentages ensure the table matches the width of the Meter Total dialog
    final dateWidth = tableWidth * 0.41; // Reduced from 0.42
    final amountWidth = tableWidth * 0.22;
    final shortAvgWidth = tableWidth * 0.18;
    final totalAvgWidth = tableWidth * 0.18;
    final spacerWidth = tableWidth * 0.01; // Added 1% spacer

    // Use the same style for all column headers
    final headerStyle = AppTextStyles.titleMedium.copyWith(
      fontWeight: FontWeight.w600,
      color: AppColors.primary,
      fontSize: 16.0, // Consistent font size for all table elements
    );

    return [
      // Space for warning icon on the left
      const SizedBox(width: 24), // Reduced from 28 to 24
      // Date column
      SizedBox(
        width: dateWidth -
            24, // Adjust width to account for the reduced icon space
        child: Padding(
          padding: const EdgeInsets.only(
              left: 2.0, right: 4.0), // Reduced right padding
          child: Container(
            height: headerHeight,
            alignment: Alignment.center, // Center-align the header
            child: Text(
              'Date',
              style: headerStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      // Amount column (£)
      SizedBox(
        width: amountWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Text(
              widget.controller.meterUnit,
              style: headerStyle,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ),
      ),
      // Short average column (£/day)
      SizedBox(
        width: shortAvgWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Rcnt.',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Avg',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
      // Total average column (£/day)
      SizedBox(
        width: totalAvgWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Total',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Avg',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
      // Add spacer/buffer to the right of the Total Average column
      SizedBox(width: spacerWidth),
    ];
  }

  List<Widget> _buildRows(double tableWidth) {
    // Calculate column widths based on the table width
    // Date column takes 41% of the width, Amount 22%, others 18% each, with 1% spacer
    // These percentages ensure the table matches the width of the Meter Total dialog
    final dateWidth = tableWidth * 0.41; // Reduced from 0.42
    final amountWidth = tableWidth * 0.22;
    final shortAvgWidth = tableWidth * 0.18;
    final totalAvgWidth = tableWidth * 0.18;
    final spacerWidth = tableWidth * 0.01; // Added 1% spacer

    return _currentPageEntries.asMap().entries.map((entry) {
      final index = entry.key;
      final meterEntry = entry.value;
      final isValid = widget.controller.isEntryValid(meterEntry.id ?? -1);
      // We can use severity for additional styling if needed in the future
      // final severity = widget.controller.getValidationSeverity(meterEntry.id ?? -1);
      final isTopUp = meterEntry.amountToppedUp > 0;

      // Determine row color based on the entry type
      Color rowColor;
      if (!isValid) {
        // Yellow background for invalid entries
        rowColor = const Color(0xFFFFF9C4); // Light yellow
      } else if (isTopUp) {
        // Beige background for top-ups
        rowColor = const Color(0xFFFFF8E1); // Light beige
      } else if (index % 2 == 0) {
        // White background for even rows
        rowColor = Colors.white;
      } else {
        // Light gray for odd rows
        rowColor = const Color(0xFFF5F5F5); // Very light gray
      }

      return Container(
        height: rowHeight,
        color: rowColor,
        child: Row(
          children: [
            // Warning icon or empty space
            SizedBox(
              width: 24, // Reduced from 28 to 24
              child: !isValid
                  ? Center(
                      child: GestureDetector(
                        onTap: widget.onInvalidEntryTap != null
                            ? () => widget.onInvalidEntryTap!(meterEntry)
                            : null,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.amber, width: 1),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 2,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.warning,
                              color: Colors.amber,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    )
                  : null,
            ),
            // Date cell
            SizedBox(
              width: dateWidth -
                  24, // Adjust width to account for the reduced icon space
              child: _buildCell(
                widget.controller.formatDate(meterEntry.timestamp),
                AppTextStyles.bodyMedium.copyWith(fontSize: 16.0),
                meterEntry,
                isValid,
              ),
            ),
            // Amount cell
            SizedBox(
              width: amountWidth,
              child: _buildCell(
                (isTopUp ? meterEntry.amountToppedUp : meterEntry.reading)
                    .toStringAsFixed(2),
                AppTextStyles.bodyMedium.copyWith(
                  fontSize: 16.0,
                  fontWeight: FontWeight.w600,
                  color: isTopUp
                      ? const Color(0xFFFF9800)
                      : const Color(
                          0xFF003087), // Orange for top-ups, Blue for readings
                ),
                meterEntry,
                isValid,
              ),
            ),
            // Short average cell
            SizedBox(
              width: shortAvgWidth,
              child: _buildCell(
                isTopUp || meterEntry.shortAverageAfterTopUp == null
                    ? 'n/a'
                    : meterEntry.shortAverageAfterTopUp!.toStringAsFixed(2),
                isTopUp || meterEntry.shortAverageAfterTopUp == null
                    ? AppTextStyles.bodyMedium
                        .copyWith(fontSize: 16.0, color: Colors.grey)
                    : AppTextStyles.bodyMedium
                        .copyWith(fontSize: 16.0, color: Colors.black87),
                meterEntry,
                isValid,
              ),
            ),
            // Total average cell
            SizedBox(
              width: totalAvgWidth,
              child: _buildCell(
                isTopUp || meterEntry.totalAverageUpToThisPoint == null
                    ? 'n/a'
                    : meterEntry.totalAverageUpToThisPoint!.toStringAsFixed(2),
                isTopUp || meterEntry.totalAverageUpToThisPoint == null
                    ? AppTextStyles.bodyMedium
                        .copyWith(fontSize: 16.0, color: Colors.grey)
                    : AppTextStyles.bodyMedium
                        .copyWith(fontSize: 16.0, color: Colors.black87),
                meterEntry,
                isValid,
              ),
            ),
            // Add spacer/buffer to the right of the Total Average column
            SizedBox(width: spacerWidth),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildCell(
    String text,
    TextStyle style,
    MeterEntry entry,
    bool isValid,
  ) {
    // Determine text alignment based on content type
    // Date cells and text -> left aligned
    // Numbers -> right aligned
    final bool isDateCell = text.contains('-'); // Updated for DD-MM-YYYY format
    final bool isNumeric =
        double.tryParse(text.replaceAll('£', '').trim()) != null;

    final TextAlign textAlignment = isDateCell
        ? TextAlign.left
        : (isNumeric || text == 'n/a')
            ? TextAlign.right
            : TextAlign.left;

    return InkWell(
      onTap: widget.controller.isEditMode
          ? () => widget.onEntryTap(entry)
          : (!isValid && widget.onInvalidEntryTap != null)
              ? () => widget.onInvalidEntryTap!(entry)
              : () => widget.onEntryTap(
                  entry), // Always allow tapping entries when not in edit mode
      child: Padding(
        // Use consistent padding for all cells
        padding: const EdgeInsets.only(left: 2.0, right: 4.0),
        child: Container(
          height: rowHeight,
          // Align date cells to the left, numeric values and 'n/a' to the right
          alignment: isDateCell
              ? Alignment.centerLeft
              : (isNumeric || text == 'n/a')
                  ? Alignment.centerRight
                  : Alignment.center,
          child: Text(
            text,
            style: style,
            textAlign: textAlignment,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ),
    );
  }

  // Removed unused methods
}
