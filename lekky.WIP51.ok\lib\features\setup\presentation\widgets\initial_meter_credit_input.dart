// File: lib/features/setup/presentation/widgets/initial_meter_credit_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A widget for inputting the first meter reading
class InitialMeterCreditInput extends StatefulWidget {
  final double? initialMeterCredit;
  final String meterUnit;
  final Function(double?) onInitialMeterCreditChanged;

  const InitialMeterCreditInput({
    Key? key,
    this.initialMeterCredit,
    required this.meterUnit,
    required this.onInitialMeterCreditChanged,
  }) : super(key: key);

  @override
  State<InitialMeterCreditInput> createState() =>
      _InitialMeterCreditInputState();
}

class _InitialMeterCreditInputState extends State<InitialMeterCreditInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.initialMeterCredit?.toString() ?? '',
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(InitialMeterCreditInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus &&
        oldWidget.initialMeterCredit != widget.initialMeterCredit) {
      _controller.text = widget.initialMeterCredit?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'First Meter Reading',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Enter your first meter reading in monetary value (leave empty if not applicable)',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            focusNode: _focusNode,
            keyboardType: const TextInputType.numberWithOptions(
                decimal: true, signed: false),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
            ],
            prefixText: widget.meterUnit,
            errorText: _errorText,
            onChanged: _validateAndUpdate,
            hintText: 'Optional',
            selectAllOnFocus:
                false, // We handle selection in the focus listener
          ),
          const SizedBox(height: 8),
          Text(
            'This value must be positive and will be used to calculate your initial meter balance',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _validateAndUpdate(String value) {
    if (value.isEmpty) {
      setState(() {
        _errorText = null;
      });
      widget.onInitialMeterCreditChanged(null);
      return;
    }

    // First check if it's a valid monetary amount
    final validation = InputValidator.validateMonetaryAmount(value);

    if (!validation['isValid']) {
      setState(() {
        _errorText = validation['errorMessage'];
      });
      return;
    }

    // Then check if it's positive
    final initialCredit = double.tryParse(value);
    if (initialCredit != null && initialCredit <= 0) {
      setState(() {
        _errorText = 'First meter reading must be greater than zero';
      });
      return;
    }

    // All validations passed
    setState(() {
      _errorText = null;
    });

    // Round to 2 decimal places for consistency
    if (initialCredit != null) {
      final roundedValue = double.parse(initialCredit.toStringAsFixed(2));
      widget.onInitialMeterCreditChanged(roundedValue);
    }
  }
}
