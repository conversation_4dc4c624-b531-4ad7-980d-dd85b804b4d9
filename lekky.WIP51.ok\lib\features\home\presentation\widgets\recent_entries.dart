// File: lib/features/home/<USER>/widgets/recent_entries.dart
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/app_card.dart';

/// A widget that displays recent entries
class RecentEntries extends StatelessWidget {
  final List<MeterEntry> entries;
  final String meterUnit;
  final bool isLoading;
  final VoidCallback onViewAll;

  const RecentEntries({
    super.key,
    required this.entries,
    required this.meterUnit,
    this.isLoading = false,
    required this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Entries',
                style: AppTextStyles.titleMedium,
              ),
              TextButton(
                onPressed: onViewAll,
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildEntries(),
        ],
      ),
    );
  }

  Widget _buildEntries() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (entries.isEmpty) {
      return const Center(
        child: Text('No entries yet'),
      );
    }

    return Column(
      children: entries.map((entry) => _buildEntryItem(entry)).toList(),
    );
  }

  Widget _buildEntryItem(MeterEntry entry) {
    final isTopUp = entry.amountToppedUp > 0;
    final amount = isTopUp ? entry.amountToppedUp : entry.reading;
    final formattedDate = DateTimeUtils.formatDateWithMonthName(entry.timestamp);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isTopUp
            ? AppColors.secondary.withOpacity(0.1)
            : AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isTopUp ? AppColors.secondary : AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isTopUp ? Icons.add_card : Icons.electric_meter,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isTopUp ? 'Top-up' : 'Meter Reading',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: isTopUp ? AppColors.secondary : AppColors.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  formattedDate,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            isTopUp ? '+$meterUnit${amount.toStringAsFixed(2)}' : '$meterUnit${amount.toStringAsFixed(2)}',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: isTopUp ? AppColors.secondary : AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
