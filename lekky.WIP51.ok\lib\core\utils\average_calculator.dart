// File: lib/core/utils/average_calculator.dart
import '../models/meter_entry.dart';
import 'cost_interval.dart';

/// Utility class for calculating electricity usage averages and cost with exact interval logic
class AverageCalculator {
  /// Calculates the Short-Term Usage per Day between a meter reading and the previous meter reading.
  static double calculateShortAverage(List<MeterEntry> entries, int index) {
    if (index <= 0 ||
        index >= entries.length ||
        entries[index].amountToppedUp > 0) {
      return 0.0;
    }
    // Find previous meter reading
    int prevIndex = index - 1;
    double sumTopUps = 0.0;
    // Sum any top-ups between the two readings
    for (int j = prevIndex + 1; j < index; j++) {
      sumTopUps += entries[j].amountToppedUp;
    }
    final prev = entries[prevIndex];
    final curr = entries[index];
    final days = curr.timestamp.difference(prev.timestamp).inDays;
    if (days <= 0) return 0.0;
    final usage = prev.amountLeft + sumTopUps - curr.amountLeft;
    return usage > 0 ? usage / days : 0.0;
  }

  /// Calculates the Total Average usage per day from the first reading up to upToIndex.
  static double calculateTotalAverage(List<MeterEntry> entries,
      [int upToIndex = -1]) {
    if (entries.length < 2) return 0.0;
    final end = upToIndex >= 0 && upToIndex < entries.length
        ? upToIndex
        : entries.length - 1;
    double totalUsage = 0.0;
    int totalDays = 0;
    for (int i = 1; i <= end; i++) {
      if (entries[i].amountToppedUp > 0) continue;
      // find previous reading index
      int prev = i - 1;
      while (prev >= 0 && entries[prev].amountToppedUp > 0) {
        prev--;
      }
      if (prev < 0) continue;
      final days =
          entries[i].timestamp.difference(entries[prev].timestamp).inDays;
      if (days <= 0) continue;
      double sumTopUps = 0.0;
      for (int j = prev + 1; j < i; j++) {
        sumTopUps += entries[j].amountToppedUp;
      }
      final usage =
          entries[prev].amountLeft + sumTopUps - entries[i].amountLeft;
      if (usage > 0) {
        totalUsage += usage;
        totalDays += days;
      }
    }
    return totalDays > 0 ? totalUsage / totalDays : 0.0;
  }

  /// Calculates cost using a modular approach with interval-based calculation
  static double calculateCost(
      List<MeterEntry> entries, DateTime startDate, DateTime endDate) {
    // Validate inputs
    if (entries.isEmpty || startDate.isAfter(endDate)) {
      return 0.0;
    }

    // Create a sorted copy of entries
    final sorted = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Extract meter readings and top-ups
    final readings = sorted.where((e) => e.amountToppedUp == 0).toList();
    final topUps = sorted.where((e) => e.amountToppedUp > 0).toList();

    // If we don't have at least 2 readings, we can't calculate rates
    if (readings.length < 2) {
      return 0.0;
    }

    // Calculate total average for future projections
    final totalAverage = calculateTotalAverage(sorted);

    // Get the last historical date
    final lastDate = sorted.last.timestamp;

    // Handle future-only calculations
    if (startDate.isAfter(lastDate)) {
      // For dates entirely in the future, use total average
      final days =
          endDate.difference(startDate).inDays + 1; // Include both days
      return totalAverage * days;
    }

    // Create a list of intervals between meter readings
    List<CostInterval> intervals = [];

    // Build intervals between consecutive meter readings
    for (int i = 0; i < readings.length - 1; i++) {
      final current = readings[i];
      final next = readings[i + 1];
      final days = next.timestamp.difference(current.timestamp).inDays;

      if (days <= 0) continue; // Skip invalid intervals

      // Find top-ups in this interval
      double sumTopUps = 0.0;
      for (var topUp in topUps) {
        if (topUp.timestamp.isAfter(current.timestamp) &&
            topUp.timestamp.isBefore(next.timestamp)) {
          sumTopUps += topUp.amountToppedUp;
        }
      }

      // Calculate usage and daily rate
      final usage = current.amountLeft - next.amountLeft + sumTopUps;
      final rate = usage > 0 ? usage / days : 0.0;

      // Create interval
      intervals.add(CostInterval(
        startDate: current.timestamp,
        endDate: next.timestamp,
        dailyRate: rate,
        days: days,
      ));
    }

    // Calculate cost for the requested date range
    double totalCost = 0.0;

    // Process each interval that overlaps with the requested date range
    for (var interval in intervals) {
      // Check if this interval overlaps with the requested date range
      if (interval.endDate.isBefore(startDate) ||
          interval.startDate.isAfter(endDate)) {
        continue; // No overlap
      }

      // Calculate overlap start and end
      final overlapStart = interval.startDate.isBefore(startDate)
          ? startDate
          : interval.startDate;
      final overlapEnd =
          interval.endDate.isAfter(endDate) ? endDate : interval.endDate;

      // Calculate days in overlap
      final overlapDays = overlapEnd.difference(overlapStart).inDays;
      if (overlapDays <= 0) continue;

      // Add cost for this interval
      totalCost += interval.dailyRate * overlapDays;
    }

    // Handle future projection if endDate is after the last reading
    if (endDate.isAfter(readings.last.timestamp)) {
      final futureStart = readings.last.timestamp;
      final futureEnd = endDate;
      final futureDays = futureEnd.difference(futureStart).inDays;

      if (futureDays > 0) {
        totalCost += totalAverage * futureDays;
      }
    }

    return totalCost;
  }

  /// Calculates the Short-Term Usage per Day for all meter readings (excluding the oldest one).
  /// This method updates the shortAverageAfterTopUp field in each MeterEntry object.
  static List<MeterEntry> calculateShortAveragesAfterTopUps(
      List<MeterEntry> entries) {
    if (entries.length < 2) return entries;

    // Create a copy of the entries list to avoid modifying the original
    final updatedEntries = List<MeterEntry>.from(entries);

    // Sort entries by timestamp to ensure chronological order
    updatedEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Find all meter readings (not top-ups)
    final meterReadings =
        updatedEntries.where((e) => e.amountToppedUp == 0).toList();

    // If we have fewer than 2 meter readings, we can't calculate any short averages
    if (meterReadings.length < 2) return updatedEntries;

    // Calculate short averages for all meter readings except the first one
    for (int i = 1; i < meterReadings.length; i++) {
      final currentReading = meterReadings[i];
      final previousReading = meterReadings[i - 1];

      // Calculate days between the two meter readings
      final days =
          currentReading.timestamp.difference(previousReading.timestamp).inDays;

      // Skip if days is zero to avoid division by zero
      if (days <= 0) continue;

      // Find all top-ups between these two meter readings
      double sumOfTopUpsBetween = 0.0;

      // Find the indices of the current and previous meter readings in the original entries list
      final currentIndex = updatedEntries.indexWhere((e) =>
          e.id == currentReading.id && e.timestamp == currentReading.timestamp);
      final prevIndex = updatedEntries.indexWhere((e) =>
          e.id == previousReading.id &&
          e.timestamp == previousReading.timestamp);

      // Sum all top-ups between the previous meter reading and the current one
      for (int j = prevIndex + 1; j < currentIndex; j++) {
        if (updatedEntries[j].amountToppedUp > 0) {
          sumOfTopUpsBetween += updatedEntries[j].amountToppedUp;
        }
      }

      // Calculate usage: Previous Amount Left + Sum of Top-Ups Between - Current Amount Left
      final usage = previousReading.amountLeft +
          sumOfTopUpsBetween -
          currentReading.amountLeft;

      // Calculate daily average (or 0.0 if usage is negative)
      final dailyAverage = usage > 0 ? usage / days : 0.0;

      // Update the short average for the current meter reading
      final currentEntryIndex = updatedEntries.indexWhere((e) =>
          e.id == currentReading.id && e.timestamp == currentReading.timestamp);
      if (currentEntryIndex >= 0) {
        updatedEntries[currentEntryIndex] =
            updatedEntries[currentEntryIndex].copyWith(
          shortAverageAfterTopUp: dailyAverage,
        );
      }
    }

    // Ensure the first meter reading has null short average
    if (meterReadings.isNotEmpty) {
      final firstReading = meterReadings[0];
      final firstReadingIndex = updatedEntries.indexWhere((e) =>
          e.id == firstReading.id && e.timestamp == firstReading.timestamp);
      if (firstReadingIndex >= 0) {
        updatedEntries[firstReadingIndex] =
            updatedEntries[firstReadingIndex].copyWith(
          shortAverageAfterTopUp: null,
        );
      }
    }

    // Ensure all top-ups have null short average
    for (int i = 0; i < updatedEntries.length; i++) {
      if (updatedEntries[i].amountToppedUp > 0) {
        updatedEntries[i] = updatedEntries[i].copyWith(
          shortAverageAfterTopUp: null,
        );
      }
    }

    return updatedEntries;
  }

  /// Gets the most recent total average from the entries
  static double getMostRecentTotalAverage(List<MeterEntry> entries) {
    return calculateTotalAverage(entries);
  }

  /// Calculates the estimated date to top up based on the current meter total and average usage.
  static DateTime? calculateDateToTopUp(
      double meterTotal, double alertThreshold, double averageUsage) {
    if (averageUsage <= 0) return null;

    final daysUntilThreshold = (meterTotal - alertThreshold) / averageUsage;
    if (daysUntilThreshold <= 0) return null;

    return DateTime.now().add(Duration(days: daysUntilThreshold.round()));
  }
}
