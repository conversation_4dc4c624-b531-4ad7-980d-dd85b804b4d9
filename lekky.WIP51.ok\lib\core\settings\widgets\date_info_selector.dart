// File: lib/core/settings/widgets/date_info_selector.dart
import 'package:flutter/material.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';
import '../../widgets/setting_dialog.dart';

/// A shared widget for selecting date info display
/// Can be used in both Setup and Settings screens
class DateInfoSelector extends StatelessWidget {
  final String currentValue;
  final Function(String) onChanged;
  final bool useDialog;
  final bool showCard;

  const DateInfoSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      return ListTile(
        title: const Text('Date Info'),
        subtitle: Text(currentValue),
        leading: const Icon(Icons.info),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showDateInfoDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Information',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Select whether to show date only or date and time',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildDateInfoOptions(context),
      ],
    );

    if (showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  Widget _buildDateInfoOptions(BuildContext context) {
    return Column(
      children: [
        _buildInfoOption('Date only', context),
        _buildInfoOption('Date and time', context),
        _buildInfoOption('Relative date', context),
      ],
    );
  }

  Widget _buildInfoOption(String option, BuildContext context) {
    final isSelected = currentValue == option;
    final theme = Theme.of(context);

    return RadioListTile<String>(
      title: Text(option),
      value: option,
      groupValue: currentValue,
      onChanged: (value) {
        if (value != null) {
          onChanged(value);
          if (useDialog) {
            Navigator.of(context).pop();
          }
        }
      },
      activeColor: theme.colorScheme.primary,
      selected: isSelected,
      dense: true,
    );
  }

  /// Show a dialog for selecting the date info
  void _showDateInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Information',
        subtitle: 'Select whether to show date only or date and time',
        content: DateInfoSelector(
          currentValue: currentValue,
          onChanged: (value) {
            onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }

  /// Static method to show a date info selection dialog
  static Future<void> showDateInfoDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Information',
        subtitle: 'Select whether to show date only or date and time',
        content: DateInfoSelector(
          currentValue: currentValue,
          onChanged: (value) {
            onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }
}
