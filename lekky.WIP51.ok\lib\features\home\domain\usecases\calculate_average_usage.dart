// File: lib/features/home/<USER>/usecases/calculate_average_usage.dart
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/average_calculator.dart';

/// Use case for calculating the average usage
class CalculateAverageUsage {
  final MeterEntryRepository _repository;

  CalculateAverageUsage(this._repository);

  /// Execute the use case to get the most recent total average
  Future<double> execute() async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return 0.0;
    }
    
    return AverageCalculator.getMostRecentTotalAverage(entries);
  }
  
  /// Calculate the short-term average (between the last two meter readings)
  Future<double> calculateShortTermAverage() async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.length < 2) {
      return 0.0;
    }
    
    // Sort entries by timestamp
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Find the last two meter readings (not top-ups)
    final meterReadings = entries.where((e) => e.amountToppedUp == 0).toList();
    if (meterReadings.length < 2) {
      return 0.0;
    }
    
    // Get the last meter reading
    final lastMeterReading = meterReadings.last;
    
    // Find the index of the last meter reading in the original entries list
    final lastMeterReadingIndex = entries.indexWhere((e) => 
        e.id == lastMeterReading.id && e.timestamp == lastMeterReading.timestamp);
    
    if (lastMeterReadingIndex < 0) {
      return 0.0;
    }
    
    // Calculate the short average for the last meter reading
    return AverageCalculator.calculateShortAverage(entries, lastMeterReadingIndex);
  }
  
  /// Calculate the cost of electric for a specific time period
  Future<double> calculateCost(DateTime startDate, DateTime endDate) async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return 0.0;
    }
    
    return AverageCalculator.calculateCost(entries, startDate, endDate);
  }
}
