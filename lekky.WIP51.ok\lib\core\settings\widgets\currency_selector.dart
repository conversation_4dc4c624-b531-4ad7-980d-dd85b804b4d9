// File: lib/core/settings/widgets/currency_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';
import '../../widgets/app_text_field.dart';
import '../../widgets/setting_dialog.dart';
import '../validators/settings_validator.dart';

/// A shared widget for selecting currency/meter unit
/// Can be used in both Setup and Settings screens
class CurrencySelector extends StatelessWidget {
  final String currentValue;
  final Function(String) onChanged;
  final bool useDialog;
  final bool showCard;
  final FocusNode? focusNode;

  const CurrencySelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
    this.focusNode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      return ListTile(
        title: const Text('Currency'),
        subtitle: Text(currentValue),
        leading: const Icon(Icons.attach_money),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showCurrencyDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Currency',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Select the currency for your meter readings',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildCurrencyGrid(context),
      ],
    );

    if (showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  Widget _buildCurrencyGrid(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? Colors.blue[300] : Colors.blue[700];

    // Define currency options
    final List<Map<String, String>> currencies = [
      {'code': 'USD', 'symbol': '\$', 'name': 'United States Dollar'},
      {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
      {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
      {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
      {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
      {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
      {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
      {'code': 'CHF', 'symbol': 'Fr.', 'name': 'Swiss Franc'},
      {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
      {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
    ];

    final hasCustomOption = !currencies.any((c) => c['symbol'] == currentValue);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...currencies.map((currency) {
              return ChoiceChip(
                label: Text('${currency['symbol']} (${currency['code']})'),
                selected: currentValue == currency['symbol'],
                selectedColor: primaryColor?.withOpacity(0.2),
                labelStyle: TextStyle(
                  color: currentValue == currency['symbol']
                      ? primaryColor
                      : isDarkMode
                          ? Colors.white
                          : Colors.black,
                  fontWeight: currentValue == currency['symbol']
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
                onSelected: (selected) {
                  if (selected) {
                    onChanged(currency['symbol']!);
                  }
                },
              );
            }),

            // Custom option if current value isn't in the list
            if (hasCustomOption)
              ChoiceChip(
                label: Text('$currentValue (Custom)'),
                selected: true,
                selectedColor: primaryColor?.withOpacity(0.2),
                labelStyle: TextStyle(
                  color: primaryColor,
                  fontWeight: FontWeight.bold,
                ),
                onSelected: (_) {},
              ),
          ],
        ),
        const SizedBox(height: 16),
        AppTextField(
          labelText: 'Custom Currency Symbol',
          hintText: 'e.g. ₹, ₽, ฿',
          helperText: 'Enter a custom symbol (max 4 characters)',
          focusNode: focusNode, // Use the provided focus node
          selectAllOnFocus: true,
          maxLength: 4, // Limit to 4 characters
          inputFormatters: [
            LengthLimitingTextInputFormatter(4), // Ensure max 4 characters
          ],
          onChanged: (value) {
            if (value.isNotEmpty) {
              final validation = SettingsValidator.validateMeterUnit(value);
              if (validation['isValid']) {
                onChanged(value);
              }
            }
          },
        ),
      ],
    );
  }

  /// Show a dialog for selecting the currency
  void _showCurrencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return SettingDialog(
            title: 'Currency',
            subtitle: 'Select the currency for your meter readings',
            content: _buildCurrencyGrid(context),
            onCancel: () {},
            onSave: () {
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }

  /// Static method to show a currency selection dialog
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;

          return SettingDialog(
            title: 'Currency',
            subtitle: 'Select the currency for your meter readings',
            content: CurrencySelector(
              currentValue: selectedValue,
              onChanged: (value) {
                setState(() {
                  selectedValue = value;
                });
              },
              useDialog: false,
              showCard: false,
            ),
            onCancel: () {},
            onSave: () {
              onChanged(selectedValue);
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }
}
