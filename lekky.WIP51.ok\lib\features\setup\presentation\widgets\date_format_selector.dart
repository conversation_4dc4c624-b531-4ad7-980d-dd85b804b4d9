// File: lib/features/setup/presentation/widgets/date_format_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/app_card.dart';

/// A widget for selecting the date format
class DateFormatSelector extends StatelessWidget {
  final String selectedFormat;
  final Function(String) onFormatChanged;

  const DateFormatSelector({
    Key? key,
    required this.selectedFormat,
    required this.onFormatChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();

    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Date Format',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Select how dates will be displayed',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          _buildFormatOption(
            'DD-MM-YYYY',
            DateTimeUtils.formatDate(now, 'dd-MM-yyyy'),
            context,
          ),
          _buildFormatOption(
            'MM-DD-YYYY',
            DateTimeUtils.formatDate(now, 'MM-dd-yyyy'),
            context,
          ),
          _buildFormatOption(
            'YYYY-MM-DD',
            DateTimeUtils.formatDate(now, 'yyyy-MM-dd'),
            context,
          ),
          _buildFormatOption(
            'DD MMM YYYY',
            DateTimeUtils.formatDateWithMonthName(now),
            context,
          ),
          _buildFormatOption(
            'MMM DD, YYYY',
            DateTimeUtils.formatDate(now, 'MMM dd, yyyy'),
            context,
          ),
        ],
      ),
    );
  }

  Widget _buildFormatOption(
      String format, String example, BuildContext context) {
    final isSelected = selectedFormat == format;
    final theme = Theme.of(context);

    return RadioListTile<String>(
      title: Text(format),
      subtitle: Text('Example: $example'),
      value: format,
      groupValue: selectedFormat,
      onChanged: (value) {
        if (value != null) {
          onFormatChanged(value);
        }
      },
      activeColor: theme.colorScheme.primary,
      selected: isSelected,
      dense: true,
    );
  }
}
