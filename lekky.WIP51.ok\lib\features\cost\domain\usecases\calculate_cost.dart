// File: lib/features/cost/domain/usecases/calculate_cost.dart
import '../models/cost_period.dart';
import '../models/cost_result.dart';

/// Use case for calculating the cost of electricity
class CalculateCost {
  /// Execute the use case
  CostResult execute({
    required double averageUsage,
    required CostPeriod period,
    required String meterUnit,
  }) {
    // Calculate the cost per period
    final costPerPeriod = averageUsage * period.days;

    return CostResult(
      averageUsage: averageUsage,
      costPerPeriod: costPerPeriod,
      period: period,
      meterUnit: meterUnit,
    );
  }
}
