// File: lib/core/settings/models/settings_validator.dart

/// A utility class for validating settings inputs
class SettingsValidator {
  /// Validates a threshold value
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateThreshold(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot be empty',
        'severity': 'error'
      };
    }

    final double? threshold = double.tryParse(value);
    if (threshold == null) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold must be a valid number',
        'severity': 'error'
      };
    }

    if (threshold < 0) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot be negative',
        'severity': 'error'
      };
    }

    // Check if the threshold has more than 2 decimal places
    final decimalPlaces = value.contains('.')
        ? value.split('.')[1].length
        : 0;
    
    if (decimalPlaces > 2) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot have more than 2 decimal places',
        'severity': 'error'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates days in advance
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateDaysInAdvance(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance cannot be empty',
        'severity': 'error'
      };
    }

    final int? days = int.tryParse(value);
    if (days == null) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance must be a valid number',
        'severity': 'error'
      };
    }

    if (days <= 0) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance must be greater than zero',
        'severity': 'error'
      };
    }

    if (days > 30) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance cannot be more than 30',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a meter unit
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateMeterUnit(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Meter unit cannot be empty',
        'severity': 'error'
      };
    }

    if (value.length > 5) {
      return {
        'isValid': false,
        'errorMessage': 'Meter unit cannot be more than 5 characters',
        'severity': 'error'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a monetary amount (can be negative)
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateMonetaryAmount(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Amount cannot be empty',
        'severity': 'error'
      };
    }

    final double? amount = double.tryParse(value);
    if (amount == null) {
      return {
        'isValid': false,
        'errorMessage': 'Amount must be a valid number',
        'severity': 'error'
      };
    }
    
    // Check if the amount has more than 2 decimal places
    final decimalPlaces = value.contains('.')
        ? value.split('.')[1].length
        : 0;
    
    if (decimalPlaces > 2) {
      return {
        'isValid': false,
        'errorMessage': 'Amount cannot have more than 2 decimal places',
        'severity': 'error'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }
}
