// File: lib/features/home/<USER>/models/meter_entry.dart

/// MeterEntry class for storing meter data
class MeterEntry {
  final int? id;
  final double reading;
  final double amountToppedUp;
  final DateTime timestamp;
  double? shortAverageAfterTopUp; // Daily usage average after a top-up
  double?
      totalAverageUpToThisPoint; // Cumulative average of readings up to this point

  MeterEntry({
    this.id,
    required this.reading,
    required this.amountToppedUp,
    required this.timestamp,
    this.shortAverageAfterTopUp,
    this.totalAverageUpToThisPoint,
  });

  /// Creates a copy of this MeterEntry with the given fields replaced with the new values
  MeterEntry copyWith({
    int? id,
    double? reading,
    double? amountToppedUp,
    DateTime? timestamp,
    double? shortAverageAfterTopUp,
    double? totalAverageUpToThisPoint,
  }) {
    return MeterEntry(
      id: id ?? this.id,
      reading: reading ?? this.reading,
      amountToppedUp: amountToppedUp ?? this.amountToppedUp,
      timestamp: timestamp ?? this.timestamp,
      shortAverageAfterTopUp:
          shortAverageAfterTopUp ?? this.shortAverageAfterTopUp,
      totalAverageUpToThisPoint:
          totalAverageUpToThisPoint ?? this.totalAverageUpToThisPoint,
    );
  }

  /// Calculates the amount left on the meter
  /// This is useful for cost calculations
  double get amountLeft => reading;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reading': reading,
      'amountToppedUp': amountToppedUp,
      'timestamp': timestamp.toIso8601String(),
      'shortAverageAfterTopUp': shortAverageAfterTopUp,
      'totalAverageUpToThisPoint': totalAverageUpToThisPoint,
    };
  }

  factory MeterEntry.fromMap(Map<String, dynamic> map) {
    return MeterEntry(
      id: map['id'],
      reading: map['reading']?.toDouble() ?? 0.0,
      amountToppedUp: map['amountToppedUp']?.toDouble() ?? 0.0,
      timestamp: DateTime.parse(map['timestamp']),
      shortAverageAfterTopUp: map['shortAverageAfterTopUp']?.toDouble(),
      totalAverageUpToThisPoint: map['totalAverageUpToThisPoint']?.toDouble(),
    );
  }
}
