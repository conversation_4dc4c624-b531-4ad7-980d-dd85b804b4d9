import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/settings/models/app_settings.dart';
import 'package:lekky/features/setup/domain/models/setup_config.dart';

void main() {
  group('Language Settings Tests', () {
    test('SetupConfig should have default language set to English', () {
      final config = SetupConfig.defaultConfig();
      expect(config.language, 'en');
    });

    test('SetupConfig should allow setting a different language', () {
      final config = SetupConfig.defaultConfig().copyWith(language: 'fr');
      expect(config.language, 'fr');
    });

    test('AppSettings should have default language set to English', () {
      final settings = AppSettings.defaultSettings();
      expect(settings.language, 'en');
    });

    test('AppSettings should allow setting a different language', () {
      final settings = AppSettings.defaultSettings().copyWith(language: 'es');
      expect(settings.language, 'es');
    });

    test('SetupConfig should include language in toMap and fromMap', () {
      final config = SetupConfig.defaultConfig().copyWith(language: 'de');
      final map = config.toMap();
      expect(map['language'], 'de');

      final newConfig = SetupConfig.fromMap(map);
      expect(newConfig.language, 'de');
    });

    test('AppSettings should include language in toMap and fromMap', () {
      final settings = AppSettings.defaultSettings().copyWith(language: 'it');
      final map = settings.toMap();
      expect(map['language'], 'it');

      final newSettings = AppSettings.fromMap(map);
      expect(newSettings.language, 'it');
    });
  });
}
