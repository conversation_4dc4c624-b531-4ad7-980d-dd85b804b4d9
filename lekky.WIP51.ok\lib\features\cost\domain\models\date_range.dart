// File: lib/features/cost/domain/models/date_range.dart
import 'cost_period.dart';

/// Represents a date range for cost calculation
class DateRange {
  final DateTime? startDate;
  final DateTime? endDate;

  const DateRange({
    this.startDate,
    this.endDate,
  });

  /// Create a copy of this date range with the given fields replaced
  DateRange copyWith({
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return DateRange(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }

  /// Check if the date range is valid
  bool get isValid {
    if (startDate == null || endDate == null) {
      return false;
    }
    return !startDate!.isAfter(endDate!);
  }

  /// Get the number of days in the date range
  /// This includes both the start and end dates (inclusive)
  int get days {
    if (startDate == null || endDate == null) {
      return 0;
    }

    // Calculate the difference in days and add 1 to include both start and end dates
    return endDate!.difference(startDate!).inDays + 1;
  }

  /// Create a date range for a past period
  static DateRange forPastPeriod(CostPeriod period) {
    final now = DateTime.now();
    final endDate = DateTime(now.year, now.month, now.day);

    DateTime startDate;
    switch (period.days) {
      case 1: // Day
        startDate = endDate;
        break;
      case 7: // Week
        startDate = endDate.subtract(const Duration(days: 6));
        break;
      case 30: // Month
        // Get the first day of the current month
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 365: // Year
        startDate = DateTime(now.year, 1, 1);
        break;
      default:
        startDate = endDate.subtract(Duration(days: period.days - 1));
    }

    return DateRange(startDate: startDate, endDate: endDate);
  }

  /// Create a date range for a future period
  static DateRange forFuturePeriod(CostPeriod period) {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);

    DateTime endDate;
    switch (period.days) {
      case 1: // Day
        endDate = startDate;
        break;
      case 7: // Week
        endDate = startDate.add(const Duration(days: 6));
        break;
      case 30: // Month
        // Get the day before the same day next month
        final month = now.month == 12 ? 1 : now.month + 1;
        final year = now.month == 12 ? now.year + 1 : now.year;

        // Check if the day exists in the target month
        final daysInTargetMonth = DateTime(year, month + 1, 0).day;
        final targetDay =
            now.day <= daysInTargetMonth ? now.day : daysInTargetMonth;

        // Create the target date and subtract one day
        final targetDate = DateTime(year, month, targetDay);
        endDate = targetDate.subtract(const Duration(days: 1));
        break;
      case 365: // Year
        // Get the day before the same day next year
        final nextYear = now.year + 1;

        // Check for leap year (Feb 29)
        if (now.month == 2 && now.day == 29) {
          // If current date is Feb 29, use Feb 28 in non-leap years
          final isLeapYear = (nextYear % 4 == 0 && nextYear % 100 != 0) ||
              (nextYear % 400 == 0);
          final targetDay = isLeapYear ? 29 : 28;

          // Create the date and subtract one day
          final targetDate = DateTime(nextYear, 2, targetDay);
          endDate = targetDate.subtract(const Duration(days: 1));
        } else {
          // Create the date and subtract one day
          final targetDate = DateTime(nextYear, now.month, now.day);
          endDate = targetDate.subtract(const Duration(days: 1));
        }
        break;
      default:
        endDate = startDate.add(Duration(days: period.days - 1));
    }

    return DateRange(startDate: startDate, endDate: endDate);
  }

  /// Create a date range based on the period and mode
  static DateRange forPeriod(CostPeriod period) {
    return period.isPast ? forPastPeriod(period) : forFuturePeriod(period);
  }
}
