// File: lib/features/home/<USER>/usecases/calculate_top_up_date.dart
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/utils/average_calculator.dart';

/// Use case for calculating the estimated date to top up
class CalculateTopUpDate {
  final SettingsRepository _settingsRepository;

  CalculateTopUpDate(this._settingsRepository);

  /// Execute the use case
  Future<DateTime?> execute(double meterTotal, double averageUsage) async {
    if (averageUsage <= 0) {
      return null;
    }
    
    // Get the alert threshold from settings
    final alertThreshold = await _settingsRepository.getAlertThreshold();
    
    // Calculate the date to top up
    return AverageCalculator.calculateDateToTopUp(
      meterTotal, 
      alertThreshold, 
      averageUsage
    );
  }
}
