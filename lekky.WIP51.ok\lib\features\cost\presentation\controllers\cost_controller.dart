// File: lib/features/cost/presentation/controllers/cost_controller.dart
import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/utils/event_bus.dart';
import '../../data/cost_repository.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_result.dart';
import '../../domain/models/date_range.dart';
import '../../domain/models/cost_mode.dart';

/// Controller for the cost screen
class CostController extends ChangeNotifier {
  final CostRepository _repository;

  // State variables
  CostResult? _costResult;
  CostPeriod _selectedPeriod =
      CostPeriod.futureDay; // Default to future day (today)
  bool _isLoading = true;
  String _error = '';
  CostMode _costMode = CostMode.future; // Default to future mode
  DateRange _dateRange = const DateRange();

  // Event subscription
  StreamSubscription<EventType>? _eventSubscription;

  // Getters
  CostResult? get costResult => _costResult;
  CostPeriod get selectedPeriod => _selectedPeriod;
  bool get isLoading => _isLoading;
  String get error => _error;
  bool get isPastMode => _costMode == CostMode.past;
  CostMode get costMode => _costMode;
  DateRange get dateRange => _dateRange;

  // Check if there are enough meter readings to show custom date range
  bool get hasEnoughReadingsForCustomRange {
    final entries = _repository.getCachedMeterEntries();
    // Filter to only include meter readings (not top-ups)
    final readings =
        entries.where((e) => e.reading > 0 && e.amountToppedUp == 0).toList();
    // Need at least 2 readings for custom date range
    return readings.length >= 2;
  }

  // Check if there are any invalid history records
  bool get hasInvalidHistoryRecords {
    return _repository.hasInvalidHistoryRecords();
  }

  // Computed getters
  String get formattedCostPerPeriod =>
      _costResult?.formattedCostPerPeriod ?? '-';
  String get formattedAverageUsage => _costResult?.formattedAverageUsage ?? '-';
  String get periodName => _selectedPeriod.name;

  // Top-up and credit information
  bool get hasTopUp => _costResult?.hasTopUp ?? false;
  String? get formattedTopUpAmount => _costResult?.formattedTopUpAmount;
  DateTime? get topUpDate => _costResult?.topUpDate;

  // Initial credit information
  bool get hasInitialCredit => _costResult?.hasInitialCredit ?? false;
  String? get formattedInitialCredit => _costResult?.formattedInitialCredit;

  // Net cost (can be negative if there's initial credit)
  String get formattedNetCost => _costResult?.formattedNetCost ?? '-';
  double get netCost => _costResult?.netCost ?? 0.0;

  // Indicates if the calculation message should be shown
  // We always want to show the message for all calculation types
  bool get usesAverages {
    return true;
  }

  /// Get the total average usage
  Future<double> getTotalAverageUsage() async {
    return await _repository.getTotalAverageUsage();
  }

  DateTime? _fromDate;
  DateTime? _toDate;

  DateTime? get fromDate => _fromDate;
  DateTime? get toDate => _toDate;

  /// Date range validation
  DateTime? get earliestDate => _repository.earliestDate;
  DateTime? get latestDate => _repository.latestDate;

  bool get isValidDateRange {
    // Both dates must be selected
    if (_fromDate == null || _toDate == null) {
      return false;
    }

    // From date must be before or equal to To date
    if (_fromDate!.isAfter(_toDate!)) {
      return false;
    }

    // If we have earliest/latest dates, check that the selected dates are within range
    // Compare only the date parts (year, month, day) to ignore time components
    if (earliestDate != null) {
      final fromDateOnly =
          DateTime(_fromDate!.year, _fromDate!.month, _fromDate!.day);
      final earliestDateOnly =
          DateTime(earliestDate!.year, earliestDate!.month, earliestDate!.day);
      if (fromDateOnly.isBefore(earliestDateOnly)) {
        return false;
      }
    }

    // We no longer restrict "To date" to be before or at the latest reading
    // This allows future dates for projections based on total averages

    return true;
  }

  String? get dateRangeError {
    if (_fromDate == null || _toDate == null) {
      return 'Please select both dates';
    }
    if (_fromDate!.isAfter(_toDate!)) {
      return 'From date must be before To date';
    }
    // Compare only the date parts (year, month, day) to ignore time components
    if (earliestDate != null) {
      final fromDateOnly =
          DateTime(_fromDate!.year, _fromDate!.month, _fromDate!.day);
      final earliestDateOnly =
          DateTime(earliestDate!.year, earliestDate!.month, earliestDate!.day);
      if (fromDateOnly.isBefore(earliestDateOnly)) {
        return 'From date is before earliest reading';
      }
    }
    // We no longer restrict "To date" to be before or at the latest reading
    // This allows future dates for projections based on total averages
    return null;
  }

  set fromDate(DateTime? date) {
    _fromDate = date;
    _selectedPeriod = CostPeriod.custom;
    _dateRange = DateRange(startDate: date, endDate: _toDate);
    notifyListeners();
    if (isValidDateRange) {
      _calculateCost();
    }
  }

  set toDate(DateTime? date) {
    _toDate = date;
    _selectedPeriod = CostPeriod.custom;
    _dateRange = DateRange(startDate: _fromDate, endDate: date);
    notifyListeners();
    if (isValidDateRange) {
      _calculateCost();
    }
  }

  /// Update the date range
  Future<void> updateDateRange(DateRange dateRange) async {
    _dateRange = dateRange;
    _fromDate = dateRange.startDate;
    _toDate = dateRange.endDate;
    _selectedPeriod = CostPeriod.custom;
    notifyListeners();

    if (isValidDateRange) {
      await _calculateCost();
    }
  }

  // Removed unused fields for last two meter readings

  CostController(this._repository) {
    // Default to current date for To date
    _toDate = DateTime.now();
    // Default to yesterday for From date
    _fromDate = DateTime.now().subtract(const Duration(days: 1));
    // Initialize date range with default values
    _dateRange = DateRange(startDate: _fromDate, endDate: _toDate);

    // Subscribe to data update events
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        // Refresh data when entries are added/edited/deleted
        init();
      }
    });
  }

  @override
  void dispose() {
    // Cancel event subscription
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Initialize the controller
  Future<void> init() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Fetch earliest and latest dates from the repository first
      await _repository.fetchDateRange();

      // Set default values: today for both from and to date
      final today = DateTime.now();

      // Ensure today is not before the earliest date
      DateTime validFromDate = today;
      if (_repository.earliestDate != null &&
          today.isBefore(_repository.earliestDate!)) {
        validFromDate = _repository.earliestDate!;
      }

      // Set default date range (both from and to date are today)
      _fromDate = validFromDate;
      _toDate = validFromDate;

      // Initialize date range
      _dateRange = DateRange(startDate: _fromDate, endDate: _toDate);

      // Calculate cost for the default period
      await updatePeriod(_selectedPeriod);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to initialize cost data: $e';
      notifyListeners();
    }
  }

  /// Calculate the cost with date range validation
  Future<void> _calculateCost() async {
    if (_fromDate == null || _toDate == null) {
      _costResult = null;
      _error = 'Please select both dates';
      notifyListeners();
      return;
    }

    if (dateRangeError != null) {
      _costResult = null;
      _error = dateRangeError!;
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Calculate the cost for the custom date range
      _costResult = await _repository.calculateCostForPeriod(
          _selectedPeriod, _fromDate, _toDate);

      // Ensure we have a valid cost result
      if (_costResult == null) {
        _error = 'Failed to calculate cost for the selected date range';
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to calculate cost: $e';
      notifyListeners();
    }
  }

  /// Update the selected period
  Future<void> updatePeriod(CostPeriod period) async {
    _selectedPeriod = period;
    _isLoading = true;
    notifyListeners();

    try {
      // For standard periods (day, week, month, year), use total average
      if (period != CostPeriod.custom) {
        // Update date range based on the selected period
        _dateRange = DateRange.forPeriod(period);
        _fromDate = _dateRange.startDate;
        _toDate = _dateRange.endDate;

        // Calculate cost based on the period
        await _calculateProjectedCost(period);
      } else {
        // For custom period, use the existing date range
        await _calculateCost();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to calculate cost: $e';
      notifyListeners();
    }
  }

  /// Update the cost mode (past or future)
  Future<void> updateCostMode(CostMode mode) async {
    if (_costMode == mode) return;

    _costMode = mode;

    // Update the selected period based on the mode
    if (mode == CostMode.past) {
      if (_selectedPeriod == CostPeriod.futureDay) {
        _selectedPeriod = CostPeriod.pastDay;
      } else if (_selectedPeriod == CostPeriod.futureWeek) {
        _selectedPeriod = CostPeriod.pastWeek;
      } else if (_selectedPeriod == CostPeriod.futureMonth) {
        _selectedPeriod = CostPeriod.pastMonth;
      } else if (_selectedPeriod == CostPeriod.futureYear) {
        _selectedPeriod = CostPeriod.pastYear;
      } else {
        _selectedPeriod = CostPeriod.pastMonth; // Default
      }
    } else {
      if (_selectedPeriod == CostPeriod.pastDay) {
        _selectedPeriod = CostPeriod.futureDay;
      } else if (_selectedPeriod == CostPeriod.pastWeek) {
        _selectedPeriod = CostPeriod.futureWeek;
      } else if (_selectedPeriod == CostPeriod.pastMonth) {
        _selectedPeriod = CostPeriod.futureMonth;
      } else if (_selectedPeriod == CostPeriod.pastYear) {
        _selectedPeriod = CostPeriod.futureYear;
      } else {
        _selectedPeriod = CostPeriod.futureMonth; // Default
      }
    }

    // Update the date range and calculate cost
    await updatePeriod(_selectedPeriod);
  }

  /// Calculate projected cost based on total average usage
  Future<void> _calculateProjectedCost(CostPeriod period) async {
    try {
      // Get the total average usage
      final totalAverage = await _repository.getTotalAverageUsage();

      // Get the meter unit
      final meterUnit = await _repository.getMeterUnit();

      // Use the period's days value directly
      final days = period.days;

      // Calculate cost
      final costPerPeriod = totalAverage * days;

      // Create cost result
      _costResult = CostResult(
        averageUsage: totalAverage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
      );
    } catch (e) {
      throw Exception('Failed to calculate projected cost: $e');
    }
  }
}
