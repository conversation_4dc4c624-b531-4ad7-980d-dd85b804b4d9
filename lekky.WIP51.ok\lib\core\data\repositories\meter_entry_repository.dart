// File: lib/core/data/repositories/meter_entry_repository.dart
import '../../models/meter_entry.dart';
import '../../utils/error_handler.dart';
import '../database/db_helper.dart';
import '../database/db_optimizer.dart';
import '../../../features/history/domain/models/related_validation_result.dart';

/// Repository for meter entries
class MeterEntryRepository {
  final DBHelper _dbHelper;

  MeterEntryRepository({DBHelper? dbHelper})
      : _dbHelper = dbHelper ?? DBHelper();

  /// Get all meter entries
  Future<List<MeterEntry>> getAllEntries() async {
    try {
      return await _dbHelper.getMeterEntries();
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to get meter entries',
        details: e.toString(),
      ));
      return [];
    }
  }

  /// Get all meter entries with caching
  Future<List<MeterEntry>> getAllEntriesWithCache() async {
    return await DBOptimizer.getCachedMeterEntries(_dbHelper);
  }

  /// Add a new meter entry
  Future<bool> addEntry(MeterEntry entry) async {
    try {
      await _dbHelper.insertMeterEntry(entry);
      DBOptimizer.invalidateCache();
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to add meter entry',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Delete a meter entry
  Future<bool> deleteEntry(int id) async {
    try {
      await _dbHelper.deleteMeterEntry(id);
      DBOptimizer.invalidateCache();
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to delete meter entry',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Delete all meter entries
  Future<bool> deleteAllEntries() async {
    try {
      await _dbHelper.deleteAllMeterEntries();
      DBOptimizer.invalidateCache();
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to delete all meter entries',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Calculate the current meter total
  Future<double> calculateMeterTotal() async {
    try {
      return await _dbHelper.calculateMeterTotal();
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate meter total',
        details: e.toString(),
      ));
      return 0.0;
    }
  }

  /// Calculate the current meter total with caching
  Future<double> calculateMeterTotalWithCache() async {
    try {
      return await DBOptimizer.getCachedMeterTotal(_dbHelper);
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate meter total with cache',
        details: e.toString(),
      ));
      return 0.0;
    }
  }

  /// Validate a meter reading
  Future<Map<String, dynamic>> validateMeterReading(
      double reading, DateTime date,
      {int? entryId}) async {
    try {
      return await _dbHelper.validateMeterReading(reading, date,
          entryId: entryId);
    } catch (e) {
      ErrorHandler.addError(AppError.validation(
        'Failed to validate meter reading',
        details: e.toString(),
      ));
      return RelatedValidationResult(
        isValid: false,
        errorMessage: 'An error occurred while validating the meter reading',
        severity: 'error',
      ).toMap();
    }
  }

  /// Validate a meter entry against all other entries
  /// Returns a map of entry IDs to validation results
  Future<Map<int, Map<String, dynamic>>> validateAllEntries() async {
    try {
      final entries = await getAllEntries();
      final Map<int, Map<String, dynamic>> results = {};

      // First pass: validate each entry individually
      for (final entry in entries) {
        if (entry.id != null && entry.amountToppedUp == 0) {
          results[entry.id!] = await validateMeterReading(
            entry.reading,
            entry.timestamp,
            entryId: entry.id,
          );
        } else if (entry.id != null && entry.amountToppedUp > 0) {
          // Top-ups are always valid
          results[entry.id!] = RelatedValidationResult.valid().toMap();
        }
      }

      return results;
    } catch (e) {
      ErrorHandler.addError(AppError.validation(
        'Failed to validate all entries',
        details: e.toString(),
      ));
      return {};
    }
  }

  /// Optimize the database
  Future<bool> optimizeDatabase() async {
    try {
      if (await DBOptimizer.needsOptimization(_dbHelper)) {
        await DBOptimizer.compactDatabase(_dbHelper);
      }
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to optimize database',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Get the latest meter reading
  Future<MeterEntry?> getLatestMeterReading() async {
    try {
      final entries = await getAllEntries();
      if (entries.isEmpty) {
        return null;
      }

      // Filter to only include entries with readings (not top-ups)
      final readingEntries = entries.where((e) => e.reading > 0).toList();
      if (readingEntries.isEmpty) {
        return null;
      }

      // Sort by date, newest first
      readingEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return readingEntries.first;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to get latest meter reading',
        details: e.toString(),
      ));
      return null;
    }
  }

  /// Calculate the average usage per day
  Future<double> getAverageUsage() async {
    try {
      final entries = await getAllEntries();
      if (entries.isEmpty) {
        return 0.0;
      }

      // Filter to only include entries with readings (not top-ups)
      final readingEntries = entries.where((e) => e.reading > 0).toList();
      if (readingEntries.length < 2) {
        return 0.0;
      }

      // Sort by date, oldest first
      readingEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Calculate total usage
      double totalUsage = 0.0;
      for (int i = 0; i < readingEntries.length - 1; i++) {
        final current = readingEntries[i];
        final next = readingEntries[i + 1];
        final usage = current.reading - next.reading;
        if (usage > 0) {
          totalUsage += usage;
        }
      }

      // Calculate total days
      final firstDate = readingEntries.first.timestamp;
      final lastDate = readingEntries.last.timestamp;
      final days = lastDate.difference(firstDate).inDays;

      if (days <= 0) {
        return 0.0;
      }

      return totalUsage / days;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate average usage',
        details: e.toString(),
      ));
      return 0.0;
    }
  }
}
