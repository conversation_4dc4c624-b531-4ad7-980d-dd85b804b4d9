// File: lib/core/widgets/app_bottom_nav_bar.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../theme/app_colors.dart';
import 'bottom_nav_bar.dart';

/// A lifecycle event handler to detect when the app is resumed
class _LifecycleEventHandler extends WidgetsBindingObserver {
  final Future<void> Function()? resumeCallBack;

  _LifecycleEventHandler({
    this.resumeCallBack,
  });

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      await resumeCallBack?.call();
    }
  }
}

/// The app's bottom navigation bar
class AppBottomNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;

  const AppBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<AppBottomNavBar> createState() => _AppBottomNavBarState();
}

class _AppBottomNavBarState extends State<AppBottomNavBar> {
  String _currencySymbol = '£'; // Default currency symbol

  late final _LifecycleEventHandler _lifecycleHandler;

  @override
  void initState() {
    super.initState();
    _loadCurrencySymbol();

    // Add a listener to update when the app is resumed
    _lifecycleHandler = _LifecycleEventHandler(
      resumeCallBack: () async {
        await _loadCurrencySymbol();
        return;
      },
    );
    WidgetsBinding.instance.addObserver(_lifecycleHandler);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_lifecycleHandler);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload currency symbol when dependencies change
    _loadCurrencySymbol();
  }

  // Load the currency symbol from SharedPreferences
  Future<void> _loadCurrencySymbol() async {
    final prefs = await SharedPreferences.getInstance();
    final symbol = prefs.getString(AppConstants.keyMeterUnit) ?? '£';
    if (mounted) {
      setState(() {
        _currencySymbol = symbol;
      });
    }
  }

  // Get the appropriate icon for the cost tab based on the currency
  IconData _getCostIcon(bool isActive) {
    // Default to dollar sign
    if (_currencySymbol == '£') {
      return isActive ? Icons.currency_pound : Icons.currency_pound_outlined;
    } else if (_currencySymbol == '€') {
      return isActive ? Icons.euro : Icons.euro_outlined;
    } else if (_currencySymbol == '¥' || _currencySymbol == 'CN¥') {
      return isActive ? Icons.currency_yen : Icons.currency_yen_outlined;
    } else if (_currencySymbol.contains('\$')) {
      return isActive ? Icons.attach_money : Icons.attach_money_outlined;
    } else {
      // For other currencies, use a generic money icon
      return isActive ? Icons.payments : Icons.payments_outlined;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavBar(
      currentIndex: widget.currentIndex,
      onTap: widget.onTap,
      items: [
        const BottomNavItem(
          label: 'Home',
          icon: Icons.home_outlined,
          activeIcon: Icons.home,
          activeColor: AppColors.homeTab,
        ),
        BottomNavItem(
          label: 'Cost',
          icon: _getCostIcon(false),
          activeIcon: _getCostIcon(true),
          activeColor: AppColors.costTab,
        ),
        const BottomNavItem(
          label: 'History',
          icon: Icons.history_outlined,
          activeIcon: Icons.history,
          activeColor: AppColors.historyTab,
        ),
        // Settings icon removed from navigation bar
      ],
    );
  }
}
