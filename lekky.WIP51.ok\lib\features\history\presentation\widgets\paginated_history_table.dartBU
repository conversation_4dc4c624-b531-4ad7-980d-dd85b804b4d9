// File: lib/features/history/presentation/widgets/paginated_history_table.dart
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../controllers/history_controller.dart';

/// A paginated table that displays meter entries
class PaginatedHistoryTable extends StatefulWidget {
  final List<MeterEntry> entries;
  final HistoryController controller;
  final Function(MeterEntry) onEntryTap;
  final Function(MeterEntry)? onInvalidEntryTap;
  final int itemsPerPage;

  const PaginatedHistoryTable({
    super.key,
    required this.entries,
    required this.controller,
    required this.onEntryTap,
    this.onInvalidEntryTap,
    this.itemsPerPage = 10,
  });

  @override
  State<PaginatedHistoryTable> createState() => _PaginatedHistoryTableState();
}

class _PaginatedHistoryTableState extends State<PaginatedHistoryTable> {
  int _currentPage = 0;
  late int _totalPages;

  @override
  void initState() {
    super.initState();
    _calculateTotalPages();
  }

  @override
  void didUpdateWidget(PaginatedHistoryTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entries != widget.entries ||
        oldWidget.itemsPerPage != widget.itemsPerPage) {
      _calculateTotalPages();
      // Ensure current page is valid
      if (_currentPage >= _totalPages) {
        _currentPage = _totalPages > 0 ? _totalPages - 1 : 0;
      }
    }
  }

  void _calculateTotalPages() {
    _totalPages = (widget.entries.length / widget.itemsPerPage).ceil();
    if (_totalPages == 0) _totalPages = 1; // At least one page even if empty
  }

  List<MeterEntry> get _currentPageEntries {
    final startIndex = _currentPage * widget.itemsPerPage;
    final endIndex = (startIndex + widget.itemsPerPage <= widget.entries.length)
        ? startIndex + widget.itemsPerPage
        : widget.entries.length;

    if (startIndex >= widget.entries.length) {
      return [];
    }

    return widget.entries.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.entries.isEmpty) {
      return const Center(
        child: Text('No entries found'),
      );
    }

    return Column(
      children: [
        _buildTable(),
        const SizedBox(height: 16),
        _buildPagination(),
      ],
    );
  }

  Widget _buildTable() {
    return Stack(
      children: [
        // Main card with table
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: widget.controller.isEditMode
                  ? const BorderSide(color: AppColors.warning, width: 2)
                  : BorderSide.none,
            ),
            child: IntrinsicWidth(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (widget.controller.isEditMode)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                      color: AppColors.warning.withOpacity(0.1),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.edit,
                            color: AppColors.warning,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Edit Mode Active',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.warning,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  DataTable(
                    columnSpacing: 16,
                    headingRowHeight: 48,
                    dataRowMinHeight: 56,
                    dataRowMaxHeight: 56,
                    dividerThickness: 1,
                    showCheckboxColumn:
                        false, // Never show checkboxes as per design
                    headingRowColor: MaterialStateProperty.all(
                        const Color(0xFFE1E1E1)), // Light gray header
                    columns: _buildColumns(context),
                    rows: _buildRows(context),
                    horizontalMargin: 16,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Add warning icons for invalid entries
        for (int i = 0; i < _currentPageEntries.length; i++)
          if (!widget.controller.isEntryValid(_currentPageEntries[i].id ?? -1))
            _buildWarningIcon(i),
      ],
    );
  }

  List<DataColumn> _buildColumns(BuildContext context) {
    // Use the same style for all column headers
    final headerStyle = AppTextStyles.titleSmall.copyWith(
      fontWeight: FontWeight.w600,
      color: AppColors.primary,
    );

    return [
      // Date column
      DataColumn(
        label: Text('Date', style: headerStyle),
      ),
      // Amount column (£)
      DataColumn(
        label: Text(
          widget.controller.meterUnit,
          style: headerStyle,
          textAlign: TextAlign.center,
        ),
      ),
      // Short average column (£/day)
      DataColumn(
        label: Text(
          '${widget.controller.meterUnit}/day',
          style: headerStyle,
          textAlign: TextAlign.center,
        ),
      ),
      // Total average column (£/day)
      DataColumn(
        label: Text(
          '${widget.controller.meterUnit}/day',
          style: headerStyle,
          textAlign: TextAlign.center,
        ),
      ),
      // No actions column - editing and deletion handled through dialog
    ];
  }

  List<DataRow> _buildRows(BuildContext context) {
    return _currentPageEntries.asMap().entries.map((entry) {
      final index = entry.key;
      final meterEntry = entry.value;
      final isValid = widget.controller.isEntryValid(meterEntry.id ?? -1);
      final severity =
          widget.controller.getValidationSeverity(meterEntry.id ?? -1);
      final isTopUp = meterEntry.amountToppedUp > 0;

      // Determine row color based on the image
      Color? rowColor;
      if (!isValid) {
        // Yellow background for invalid entries
        rowColor = const Color(0xFFFFF9C4); // Light yellow
      } else if (isTopUp) {
        // Beige background for top-ups
        rowColor = const Color(0xFFFFF8E1); // Light beige
      } else if (index % 2 == 0) {
        // White background for even rows
        rowColor = Colors.white;
      } else {
        // Light gray for odd rows
        rowColor = const Color(0xFFF5F5F5); // Very light gray
      }

      return DataRow(
        color: MaterialStateProperty.all(rowColor),
        cells: _buildCells(context, meterEntry, isValid, severity),
      );
    }).toList();
  }

  List<DataCell> _buildCells(
      BuildContext context, MeterEntry entry, bool isValid, String severity) {
    final isTopUp = entry.amountToppedUp > 0;
    final amount = isTopUp ? entry.amountToppedUp : entry.reading;
    final shortAverage = entry.shortAverageAfterTopUp;
    final totalAverage = entry.totalAverageUpToThisPoint;

    // Date cell style
    const dateStyle = AppTextStyles.bodyMedium;

    // Amount cell style - orange for top-ups, blue for meter readings
    final amountStyle = AppTextStyles.bodyMedium.copyWith(
      fontWeight: FontWeight.w600,
      color: isTopUp
          ? const Color(0xFFFF9800)
          : const Color(0xFF003087), // Orange for top-ups, Blue for readings
    );

    // Average cell style
    final averageStyle = AppTextStyles.bodyMedium.copyWith(
      color: Colors.black87,
    );

    // n/a style
    final naStyle = AppTextStyles.bodyMedium.copyWith(
      color: Colors.grey,
    );

    return [
      // Date cell
      DataCell(
        Text(
          widget.controller.formatDate(entry.timestamp),
          style: dateStyle,
        ),
        // In edit mode: allow editing, otherwise show invalid entry explanation if invalid
        onTap: widget.controller.isEditMode
            ? () => widget.onEntryTap(entry)
            : (!isValid && widget.onInvalidEntryTap != null)
                ? () => widget.onInvalidEntryTap!(entry)
                : null,
      ),
      // Amount cell
      DataCell(
        Text(
          amount.toStringAsFixed(
              2), // Remove currency symbol as it's in the header
          style: amountStyle,
        ),
        // In edit mode: allow editing, otherwise show invalid entry explanation if invalid
        onTap: widget.controller.isEditMode
            ? () => widget.onEntryTap(entry)
            : (!isValid && widget.onInvalidEntryTap != null)
                ? () => widget.onInvalidEntryTap!(entry)
                : null,
      ),
      // Short average cell
      DataCell(
        Text(
          isTopUp || shortAverage == null
              ? 'n/a'
              : shortAverage.toStringAsFixed(2), // Remove currency symbol
          style: isTopUp || shortAverage == null ? naStyle : averageStyle,
        ),
        // In edit mode: allow editing, otherwise show invalid entry explanation if invalid
        onTap: widget.controller.isEditMode
            ? () => widget.onEntryTap(entry)
            : (!isValid && widget.onInvalidEntryTap != null)
                ? () => widget.onInvalidEntryTap!(entry)
                : null,
      ),
      // Total average cell
      DataCell(
        Text(
          isTopUp || totalAverage == null
              ? 'n/a'
              : totalAverage.toStringAsFixed(2), // Remove currency symbol
          style: isTopUp || totalAverage == null ? naStyle : averageStyle,
        ),
        // In edit mode: allow editing, otherwise show invalid entry explanation if invalid
        onTap: widget.controller.isEditMode
            ? () => widget.onEntryTap(entry)
            : (!isValid && widget.onInvalidEntryTap != null)
                ? () => widget.onInvalidEntryTap!(entry)
                : null,
      ),
      // No actions column - editing and deletion handled through dialog
    ];
  }

  Widget _buildPagination() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: const Icon(Icons.first_page),
          onPressed: _currentPage > 0 ? () => _goToPage(0) : null,
          tooltip: 'First Page',
        ),
        IconButton(
          icon: const Icon(Icons.chevron_left),
          onPressed:
              _currentPage > 0 ? () => _goToPage(_currentPage - 1) : null,
          tooltip: 'Previous Page',
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Page ${_currentPage + 1} of $_totalPages',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.chevron_right),
          onPressed: _currentPage < _totalPages - 1
              ? () => _goToPage(_currentPage + 1)
              : null,
          tooltip: 'Next Page',
        ),
        IconButton(
          icon: const Icon(Icons.last_page),
          onPressed: _currentPage < _totalPages - 1
              ? () => _goToPage(_totalPages - 1)
              : null,
          tooltip: 'Last Page',
        ),
      ],
    );
  }

  void _goToPage(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  /// Builds a warning icon positioned at the left edge of the table for invalid entries
  Widget _buildWarningIcon(int rowIndex) {
    final entry = _currentPageEntries[rowIndex];
    const headerHeight = 48.0; // DataTable header height
    const rowHeight = 56.0; // DataRow height
    final editModeHeaderHeight = widget.controller.isEditMode ? 32.0 : 0.0;

    // Calculate the position of the warning icon
    final top = headerHeight +
        (rowIndex * rowHeight) +
        (rowHeight / 2) -
        10 +
        editModeHeaderHeight;

    return Positioned(
      left: -10, // Position so half the icon is outside the table
      top: top,
      child: GestureDetector(
        onTap: widget.onInvalidEntryTap != null
            ? () => widget.onInvalidEntryTap!(entry)
            : null,
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.amber, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 2,
                spreadRadius: 1,
              ),
            ],
          ),
          child: const Center(
            child: Icon(
              Icons.warning,
              color: Colors.amber,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}
