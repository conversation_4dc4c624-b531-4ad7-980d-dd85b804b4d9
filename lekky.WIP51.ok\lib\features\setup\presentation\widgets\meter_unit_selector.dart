// File: lib/features/setup/presentation/widgets/meter_unit_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A widget for selecting the meter unit
class MeterUnitSelector extends StatefulWidget {
  final String selectedUnit;
  final Function(String) onUnitChanged;

  const MeterUnitSelector({
    Key? key,
    required this.selectedUnit,
    required this.onUnitChanged,
  }) : super(key: key);

  @override
  State<MeterUnitSelector> createState() => _MeterUnitSelectorState();
}

class _MeterUnitSelectorState extends State<MeterUnitSelector> {
  // Define currency options
  final List<Map<String, String>> currencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'United States Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'CHF', 'symbol': 'Fr.', 'name': 'Swiss Franc'},
    {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
    {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
  ];

  late TextEditingController _customCurrencyController;
  late String _selectedSymbol;
  late bool _hasCustomOption;

  @override
  void initState() {
    super.initState();
    _selectedSymbol = widget.selectedUnit;
    _hasCustomOption =
        !currencies.any((c) => c['symbol'] == widget.selectedUnit);
    _customCurrencyController = TextEditingController();
  }

  @override
  void dispose() {
    _customCurrencyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? Colors.blue[300] : Colors.blue[700];

    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Currency',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          const Text(
            'Select the currency for your meter readings',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),

          // Currency grid
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...currencies.map((currency) {
                return ChoiceChip(
                  label: Text('${currency['symbol']} (${currency['code']})'),
                  selected: _selectedSymbol == currency['symbol'],
                  selectedColor: primaryColor?.withOpacity(0.2),
                  labelStyle: TextStyle(
                    color: _selectedSymbol == currency['symbol']
                        ? primaryColor
                        : isDarkMode
                            ? Colors.white
                            : Colors.black,
                    fontWeight: _selectedSymbol == currency['symbol']
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedSymbol = currency['symbol']!;
                      });
                      widget.onUnitChanged(_selectedSymbol);
                    }
                  },
                );
              }),

              // Custom option if current value isn't in the list
              if (_hasCustomOption)
                ChoiceChip(
                  label: Text('${widget.selectedUnit} (Custom)'),
                  selected: _selectedSymbol == widget.selectedUnit,
                  selectedColor: primaryColor?.withOpacity(0.2),
                  labelStyle: TextStyle(
                    color: _selectedSymbol == widget.selectedUnit
                        ? primaryColor
                        : isDarkMode
                            ? Colors.white
                            : Colors.black,
                    fontWeight: _selectedSymbol == widget.selectedUnit
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedSymbol = widget.selectedUnit;
                      });
                      widget.onUnitChanged(_selectedSymbol);
                    }
                  },
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Custom currency input
          TextField(
            controller: _customCurrencyController,
            decoration: const InputDecoration(
              labelText: 'Custom Currency Symbol',
              hintText: 'e.g. ₹, ₽, ฿',
              helperText: 'Enter a custom symbol if not in the list above',
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                setState(() {
                  _selectedSymbol = value;
                  _hasCustomOption =
                      !currencies.any((c) => c['symbol'] == value);
                });
                widget.onUnitChanged(_selectedSymbol);
              }
            },
          ),
        ],
      ),
    );
  }
}
