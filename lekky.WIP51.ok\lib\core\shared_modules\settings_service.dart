// File: lib/core/shared_modules/settings_service.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'settings_model.dart';

/// Service for managing all app settings
class SettingsService {
  static const String _currencyKey = 'meter_unit';
  static const String _dateFormatKey = 'date_format';
  static const String _dateInfoKey = 'date_info';
  static const String _alertThresholdKey = 'alert_threshold';
  static const String _daysInAdvanceKey = 'days_in_advance';
  static const String _themeKey = 'theme_mode';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _lowBalanceAlertsKey = 'low_balance_alerts';
  static const String _daysInAdvanceAlertsKey = 'days_in_advance_alerts';
  static const String _setupCompletedKey = 'setup_completed';

  // Default values
  static const String defaultCurrency = '£';
  static const String defaultDateFormat = 'DD-MM-YYYY';
  static const String defaultDateInfo = 'Date only';
  static const double defaultAlertThreshold = 5.0;
  static const int defaultDaysInAdvance = 2;
  static const int defaultThemeMode = 0; // system
  static const bool defaultNotificationsEnabled = false;
  static const bool defaultLowBalanceAlerts = true;
  static const bool defaultDaysInAdvanceAlerts = true;

  // Singleton instance
  static final SettingsService _instance = SettingsService._internal();
  
  // Factory constructor
  factory SettingsService() => _instance;
  
  // Internal constructor
  SettingsService._internal();

  // Currency methods
  Future<String> getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_currencyKey) ?? defaultCurrency;
  }

  Future<void> setCurrency(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currencyKey, value);
  }

  // Date format methods
  Future<String> getDateFormat() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_dateFormatKey) ?? defaultDateFormat;
  }

  Future<void> setDateFormat(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_dateFormatKey, value);
  }

  // Date info methods
  Future<String> getDateInfo() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_dateInfoKey) ?? defaultDateInfo;
  }

  Future<void> setDateInfo(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_dateInfoKey, value);
  }

  // Alert threshold methods
  Future<double> getAlertThreshold() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_alertThresholdKey) ?? defaultAlertThreshold;
  }

  Future<void> setAlertThreshold(double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_alertThresholdKey, value);
  }

  // Days in advance methods
  Future<int> getDaysInAdvance() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_daysInAdvanceKey) ?? defaultDaysInAdvance;
  }

  Future<void> setDaysInAdvance(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_daysInAdvanceKey, value);
  }

  // Theme mode methods
  Future<ThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final value = prefs.getInt(_themeKey) ?? defaultThemeMode;
    return ThemeMode.values[value];
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, mode.index);
  }

  // Notification methods
  Future<bool> getNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? defaultNotificationsEnabled;
  }

  Future<void> setNotificationsEnabled(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, value);
  }

  Future<bool> getLowBalanceAlerts() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_lowBalanceAlertsKey) ?? defaultLowBalanceAlerts;
  }

  Future<void> setLowBalanceAlerts(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_lowBalanceAlertsKey, value);
  }

  Future<bool> getDaysInAdvanceAlerts() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_daysInAdvanceAlertsKey) ?? defaultDaysInAdvanceAlerts;
  }

  Future<void> setDaysInAdvanceAlerts(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_daysInAdvanceAlertsKey, value);
  }

  // Setup completion methods
  Future<bool> isSetupCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_setupCompletedKey) ?? false;
  }

  Future<void> setSetupCompleted(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_setupCompletedKey, value);
  }

  // Get all settings as a map
  Future<Map<String, dynamic>> getAllSettings() async {
    return {
      'currency': await getCurrency(),
      'dateFormat': await getDateFormat(),
      'dateInfo': await getDateInfo(),
      'alertThreshold': await getAlertThreshold(),
      'daysInAdvance': await getDaysInAdvance(),
      'themeMode': await getThemeMode(),
      'notificationsEnabled': await getNotificationsEnabled(),
      'lowBalanceAlerts': await getLowBalanceAlerts(),
      'daysInAdvanceAlerts': await getDaysInAdvanceAlerts(),
    };
  }
}
