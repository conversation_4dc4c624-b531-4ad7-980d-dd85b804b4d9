// File: lib/core/widgets/app_text_field.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../utils/responsive_layout.dart';

/// A reusable text field widget with consistent styling
class AppTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final bool obscureText;
  final bool enabled;
  final bool autofocus;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final FocusNode? focusNode;
  final bool readOnly;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final BorderRadius? borderRadius;
  final Color? fillColor;
  final Color? textColor;
  final TextStyle? style;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? errorStyle;
  final TextStyle? helperStyle;
  final bool showCursor;
  final bool autocorrect;
  final bool enableSuggestions;
  final TextCapitalization textCapitalization;
  final TextAlign textAlign;
  final bool expands;
  final bool showCounter;
  final String? counterText;
  final Widget? counter;
  final bool isDense;
  final bool filled;
  final bool autovalidate;
  final String? Function(String?)? validator;
  final AutovalidateMode? autovalidateMode;
  final bool selectAllOnFocus;

  const AppTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.obscureText = false,
    this.enabled = true,
    this.autofocus = false,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixText,
    this.focusNode,
    this.readOnly = false,
    this.onTap,
    this.contentPadding,
    this.borderRadius,
    this.fillColor,
    this.textColor,
    this.style,
    this.labelStyle,
    this.hintStyle,
    this.errorStyle,
    this.helperStyle,
    this.showCursor = true,
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.textCapitalization = TextCapitalization.none,
    this.textAlign = TextAlign.start,
    this.expands = false,
    this.showCounter = true,
    this.counterText,
    this.counter,
    this.isDense = false,
    this.filled = true,
    this.autovalidate = false,
    this.validator,
    this.autovalidateMode,
    this.selectAllOnFocus = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultFillColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;
    final defaultTextColor =
        isDarkMode ? AppColors.onSurfaceDark : AppColors.onSurface;

    final responsiveBorderRadius = borderRadius ??
        BorderRadius.circular(
            ResponsiveLayout.getBorderRadiusForScreenType(context));
    final responsiveContentPadding = contentPadding ??
        const EdgeInsets.symmetric(horizontal: 16, vertical: 16);

    // Create a focus node that selects all text when focused if selectAllOnFocus is true
    final FocusNode effectiveFocusNode = focusNode ?? FocusNode();

    if (selectAllOnFocus) {
      effectiveFocusNode.addListener(() {
        if (effectiveFocusNode.hasFocus && controller != null) {
          controller!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: controller!.text.length,
          );
        }
      });
    }

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      enabled: enabled,
      autofocus: autofocus,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      onChanged: onChanged,
      onEditingComplete: onEditingComplete,
      onFieldSubmitted: onSubmitted,
      inputFormatters: inputFormatters,
      focusNode: effectiveFocusNode,
      readOnly: readOnly,
      onTap: () {
        onTap?.call();
        if (controller != null) {
          controller!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: controller!.text.length,
          );
        }
      },
      style: style?.copyWith(color: textColor ?? defaultTextColor) ??
          TextStyle(color: textColor ?? defaultTextColor),
      showCursor: showCursor,
      autocorrect: autocorrect,
      enableSuggestions: enableSuggestions,
      textCapitalization: textCapitalization,
      textAlign: textAlign,
      expands: expands,
      buildCounter: showCounter
          ? null
          : (context,
                  {required int currentLength,
                  required bool isFocused,
                  required int? maxLength}) =>
              null,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        helperText: helperText,
        errorText: errorText,
        prefixIcon: prefixIcon,
        prefixText: prefixText,
        suffixIcon: suffixIcon,
        contentPadding: responsiveContentPadding,
        filled: filled,
        fillColor: fillColor ?? defaultFillColor,
        labelStyle: labelStyle,
        hintStyle: hintStyle,
        errorStyle: errorStyle,
        helperStyle: helperStyle,
        counterText: counterText,
        counter: counter,
        isDense: isDense,
        border: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
          borderSide: BorderSide(
            color: isDarkMode ? AppColors.outlineDark : AppColors.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
          borderSide: BorderSide(
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
          borderSide: BorderSide(
            color: isDarkMode ? AppColors.errorDark : AppColors.error,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
          borderSide: BorderSide(
            color: isDarkMode ? AppColors.errorDark : AppColors.error,
            width: 2,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: responsiveBorderRadius,
          borderSide: BorderSide(
            color: (isDarkMode ? AppColors.outlineDark : AppColors.outline)
                .withOpacity(0.5),
          ),
        ),
      ),
      validator: validator,
      autovalidateMode: autovalidateMode ??
          (autovalidate
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled),
    );
  }
}
