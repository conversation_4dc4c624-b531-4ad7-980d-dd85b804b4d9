# Lekky App: Average Usage Calculation Logic

## Overview

The Lekky app uses two distinct average calculations to help users understand and predict their electricity usage patterns:

1. **Short Average**: Represents the daily usage rate between consecutive meter readings, providing insight into recent consumption patterns.

2. **Total Average**: Represents the overall daily usage rate across all historical data, providing a long-term view of consumption habits.

These averages serve critical functions within the app:
- Help predict when users will need to top up their meter
- Enable the app to send timely notifications before balance reaches critical levels
- Allow users to understand their usage patterns and potentially adjust consumption habits
- Provide data-driven insights rather than guesswork for financial planning

## Short Average

### Definition

The Short Average represents the daily usage rate between a meter reading and the previous entry. It shows how quickly electricity was consumed in the most recent period, which may differ from long-term patterns due to seasonal changes, temporary usage patterns, or other short-term factors.

### Calculation Logic

The Short Average is calculated using the following steps:

1. For a given meter reading entry at index `i`:
   - Skip calculation if it's a top-up entry (amountToppedUp > 0)
   - Skip calculation if it's the first entry (no previous entry to compare)

2. Get the previous entry (could be a top-up or a meter reading):
   - Look at the entry immediately before the current one

3. Calculate usage based on the type of the previous entry:
   - If previous entry is a top-up:
     - Usage = Previous Reading's Amount Left - Current Reading's Amount Left
   - If previous entry is a meter reading:
     - Find all top-ups between these two readings
     - Usage = Previous Reading - Current Reading - Sum of Top-Ups Between

4. Calculate days between entries:
   - Days = number of days between the timestamps of the two entries

5. Calculate the daily average:
   - Short Average = Usage / Days (if Days > 0, otherwise 0)

### Formula (Dart Code)

```dart
static double calculateShortAverage(List<MeterEntry> entries, int index) {
  // Validate input parameters
  if (index < 0 || index >= entries.length || entries[index].amountToppedUp > 0) {
    return 0.0;
  }

  // If this is the very first entry, return 0.0 as there's no previous entry
  if (index == 0) {
    return 0.0;
  }

  // Get the current meter reading
  final currentReading = entries[index];

  // Get the previous entry (could be a top-up or a meter reading)
  final previousEntry = entries[index - 1];

  // Calculate days between the entries
  final days = currentReading.timestamp.difference(previousEntry.timestamp).inDays;

  // Avoid division by zero
  if (days <= 0) return 0.0;

  // Calculate usage based on the type of the previous entry
  double usage;

  if (previousEntry.amountToppedUp > 0) {
    // Previous entry is a top-up
    // Calculate usage: Previous Reading's Amount Left - Current Reading's Amount Left
    usage = previousEntry.amountLeft - currentReading.amountLeft;
  } else {
    // Previous entry is a meter reading
    // Find all top-ups between these two readings
    double sumOfTopUpsBetween = 0.0;
    for (int i = index - 1; i > 0; i--) {
      if (entries[i].amountToppedUp > 0) {
        sumOfTopUpsBetween += entries[i].amountToppedUp;
      } else {
        // Found another meter reading, stop looking for top-ups
        break;
      }
    }

    // Calculate adjusted usage: Previous Reading - Current Reading - Sum of Top-Ups
    usage = previousEntry.amountLeft - currentReading.amountLeft - sumOfTopUpsBetween;
  }

  // Return the average daily usage
  return usage > 0 ? usage / days : 0.0;
}
```

### Edge Cases

- **Fewer than 2 entries**: Returns 0.0 as there's no previous reading to compare
- **Top-up entries**: Returns 0.0 as top-ups don't represent usage
- **Same-day entries**: If entries have the same date or are less than 24 hours apart, days will be 0, resulting in a return value of 0.0 to avoid division by zero
- **Negative usage**: Not explicitly handled, but would result in a negative average (indicating an increase in meter reading, which is typically an error condition)

### Example

Consider these meter entries:

| Entry | Date       | Reading | Top-up | Short Avg |
|-------|------------|---------|--------|-----------|
| 1     | 2023-01-01 | 100.0   | 0.0    | N/A       |
| 2     | 2023-01-03 | 90.0    | 0.0    | 5.0/day   |
| 3     | 2023-01-04 | 0.0     | 20.0   | N/A       |
| 4     | 2023-01-06 | 85.0    | 0.0    | 12.5/day  |

For Entry 1:
- It's the first entry, so Short Average = N/A

For Entry 2:
- Previous entry is Entry 1 (a meter reading)
- Usage = 100.0 - 90.0 = 10.0
- Days = 2 days (Jan 3 - Jan 1)
- Short Average = 10.0 / 2 = 5.0/day

For Entry 3:
- It's a top-up entry, so Short Average = N/A

For Entry 4:
- Previous entry is Entry 3 (a top-up)
- Usage = Previous Reading's Amount Left - Current Reading's Amount Left
- Usage = (90.0 + 20.0) - 85.0 = 110.0 - 85.0 = 25.0
- Days = 2 days (Jan 6 - Jan 4)
- Short Average = 25.0 / 2 = 12.5/day

## Total Average

### Definition

The Total Average represents the overall daily usage rate across all historical data up to a specific point in time. It provides a more stable, long-term view of consumption patterns by averaging out seasonal variations and temporary usage spikes.

### Calculation Logic

The Total Average is calculated using the following steps:

1. Ensure there are at least 2 entries in the dataset up to the specified index
2. If the current entry is the very first meter reading, return 0.0 as there's no previous reading to compare
3. Calculate usage between consecutive entries:
   - Skip if current entry is a top-up
   - Calculate days between entries
   - Calculate usage based on the type of the previous entry:
     - If previous entry is a top-up: Usage = Previous Reading's Amount Left - Current Reading's Amount Left
     - If previous entry is a meter reading: Usage = Previous Reading - Current Reading - Sum of Top-Ups Between
   - Add positive usage to the total
   - Add days to the total days
4. Calculate the daily average:
   - Total Average = Total Usage / Total Days (if Total Days > 0, otherwise 0)

### Formula (Dart Code)

```dart
static double calculateTotalAverage(List<MeterEntry> entries, [int upToIndex = -1]) {
  // Validate input parameters
  if (entries.length < 2) return 0.0;

  // If upToIndex is -1, use all entries, otherwise use entries up to the specified index
  final endIndex = upToIndex >= 0 && upToIndex < entries.length
      ? upToIndex + 1
      : entries.length;
  final entriesToUse = entries.sublist(0, endIndex);

  if (entriesToUse.length < 2) return 0.0;

  // Find all meter readings (not top-ups)
  final List<MeterEntry> meterReadings =
      entriesToUse.where((entry) => entry.amountToppedUp == 0).toList();

  if (meterReadings.length < 2) return 0.0;

  // If upToIndex corresponds to the very first meter reading, return 0.0
  if (upToIndex >= 0 && upToIndex < entries.length) {
    final currentEntry = entries[upToIndex];
    if (currentEntry.amountToppedUp == 0) {
      // It's a meter reading
      final meterReadingIndex = meterReadings.indexWhere((e) =>
          e.id == currentEntry.id && e.timestamp == currentEntry.timestamp);
      if (meterReadingIndex == 0) {
        // This is the very first meter reading, return 0.0
        return 0.0;
      }
    }
  }

  // Track total usage and days
  double totalUsage = 0.0;
  int totalDays = 0;

  // Calculate usage between consecutive entries
  for (int i = 1; i < entriesToUse.length; i++) {
    final currentEntry = entriesToUse[i];
    final previousEntry = entriesToUse[i - 1];

    // Skip if current entry is a top-up
    if (currentEntry.amountToppedUp > 0) continue;

    // Calculate days between entries
    final days = currentEntry.timestamp.difference(previousEntry.timestamp).inDays;
    if (days <= 0) continue;

    // Calculate usage based on the type of the previous entry
    double usage;

    if (previousEntry.amountToppedUp > 0) {
      // Previous entry is a top-up
      usage = previousEntry.amountLeft - currentEntry.amountLeft;
    } else {
      // Previous entry is a meter reading
      // Find all top-ups between these two readings
      double sumOfTopUpsBetween = 0.0;
      for (int j = i - 1; j > 0; j--) {
        if (entriesToUse[j].amountToppedUp > 0) {
          sumOfTopUpsBetween += entriesToUse[j].amountToppedUp;
        } else if (entriesToUse[j].amountToppedUp == 0) {
          // Found another meter reading, stop looking for top-ups
          break;
        }
      }

      // Calculate adjusted usage
      usage = previousEntry.amountLeft - currentEntry.amountLeft - sumOfTopUpsBetween;
    }

    // Only add positive usage to the total
    if (usage > 0) {
      totalUsage += usage;
      totalDays += days;
    }
  }

  // Avoid division by zero and return the average usage per day
  return totalDays > 0 ? totalUsage / totalDays : 0.0;
}
```

### Key Improvements

The updated Total Average calculation includes several important improvements:

1. **Progressive Historical View**: Calculates the average up to each specific point in history
2. **Consecutive Reading Pairs**: Only considers pairs of consecutive meter readings (non-top-up entries)
3. **Dynamic Date Range**: Uses the actual first and last valid reading dates rather than assuming the entire dataset
4. **Null Safety**: Properly handles cases where no valid reading pairs exist

### Edge Cases

- **Fewer than 2 entries**: Returns 0.0 as there's insufficient data
- **Zero days between first and last entry**: Returns 0.0 to avoid division by zero
- **All top-up entries**: Returns 0.0 as no valid reading pairs exist
- **No consecutive non-top-up entries**: Returns 0.0 as firstDate and lastDate would be null
- **Irregular intervals**: Handled naturally by using the actual time span between valid readings

### Example

Consider these meter entries over time:

| Entry | Date       | Reading | Top-up | Total Avg (up to this entry) |
|-------|------------|---------|--------|------------------------------|
| 1     | 2023-01-01 | 100.0   | 0.0    | N/A (first entry)           |
| 2     | 2023-01-05 | 80.0    | 0.0    | 5.0/day                     |
| 3     | 2023-01-06 | 0.0     | 30.0   | N/A (top-up entry)          |
| 4     | 2023-01-10 | 70.0    | 0.0    | 7.5/day                     |
| 5     | 2023-01-15 | 60.0    | 0.0    | 5.0/day                     |

Total Average calculation for Entry 2:
- Previous entry is Entry 1 (a meter reading)
- Usage = 100.0 - 80.0 = 20.0
- Days = 4 days (Jan 5 - Jan 1)
- Total Usage = 20.0
- Total Days = 4
- Total Average = 20.0 / 4 = 5.0/day

Total Average calculation for Entry 4:
- Previous entry is Entry 3 (a top-up)
- Usage = (80.0 + 30.0) - 70.0 = 110.0 - 70.0 = 40.0
- Days = 4 days (Jan 10 - Jan 6)
- Previous Total Usage = 20.0
- Previous Total Days = 4
- New Total Usage = 20.0 + 40.0 = 60.0
- New Total Days = 4 + 4 = 8
- Total Average = 60.0 / 8 = 7.5/day

Total Average calculation for Entry 5:
- Previous entry is Entry 4 (a meter reading)
- Usage = 70.0 - 60.0 = 10.0
- Days = 5 days (Jan 15 - Jan 10)
- Previous Total Usage = 60.0
- Previous Total Days = 8
- New Total Usage = 60.0 + 10.0 = 70.0
- New Total Days = 8 + 5 = 13
- Total Average = 70.0 / 14 = 5.0/day

## Calculation Triggers

Averages are calculated at several points throughout the app:

### Home Page

- On initial load: Both averages are calculated when the page loads
- After data refresh: Recalculated whenever meter data is updated
- For notifications: Used to predict when the meter will reach the alert threshold

```dart
@override
void initState() {
  super.initState();
  _loadPreferences();
  _loadMeterData();
  // ... other initialization
}

Future<void> _loadMeterData() async {
  final data = await dbHelper.getEntries();
  final total = await dbHelper.getMeterTotal();

  // Calculate average usage immediately
  double avgUsage = 0.0;
  if (data.length >= 2) {
    avgUsage = _calculateAverageUsage(data);
  }

  setState(() {
    meterEntries = data;
    _meterTotal = total;
    _averageUsage = avgUsage;
  });

  _checkForAlerts();
}
```

### Meter History Page

- On page load: Both Short and Total Averages are calculated for all entries
- For each entry: Both averages are calculated up to that specific point in history
- Only non-top-up entries display average values

```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      title: const Text('Meter History'),
    ),
    body: entries.isEmpty
        ? const Center(child: Text("No meter entries available."))
        : ListView.builder(
            itemCount: entries.length,
            itemBuilder: (context, index) {
              final entry = entries[index];
              final date = DateFormat('dd-MM-yyyy').format(entry.timestamp);
              final time = DateFormat('HH:mm').format(entry.timestamp);

              final shortAvg = _calculateShortAverage(index);
              final totalAvg = _calculateTotalAverage(index);

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                elevation: 3,
                child: ListTile(
                  title: Text("Date: $date $time"),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (entry.amountToppedUp > 0)
                        Text("💰 Top-Up: ${entry.amountToppedUp.toStringAsFixed(2)}")
                      else
                        Text("📉 Reading: ${entry.reading.toStringAsFixed(2)}"),

                      if (entry.amountToppedUp == 0) ...[  // Only show averages for meter readings
                        Text("🔁 Short Avg: ${shortAvg > 0 ? "${shortAvg.toStringAsFixed(2)} / day" : 'N/A'}"),
                        Text("📊 Total Avg: ${totalAvg > 0 ? "${totalAvg.toStringAsFixed(2)} / day" : 'N/A'}"),
                      ]
                    ],
                  ),
                ),
              );
            },
          ),
  );
}
```

## Rules and Exceptions

### Invalid or Overlapping Timestamps

- The app sorts entries by timestamp before calculations
- No explicit handling for overlapping timestamps, but the difference calculation would still work
- For identical timestamps, the day difference would be 0, resulting in a 0.0 average

### Zero or Negative Usage

- Zero usage: Will result in a 0.0 average, which is valid
- Negative usage (meter reading increases): Not explicitly handled, but would result in a negative average
- Best practice would be to add validation to prevent negative usage entries

### Top-up Handling

- Top-up entries (amountToppedUp > 0) don't have Short or Total Averages displayed
- When a meter reading follows a top-up, the calculation uses the formula: Usage = Previous Reading's Amount Left - Current Reading's Amount Left
- When a meter reading follows another meter reading, the calculation accounts for any top-ups in between using the formula: Usage = Previous Reading - Current Reading - Sum of Top-Ups Between
- Top-ups are essential for accurate usage calculations, as they represent additions to the meter balance

### Data Consistency

- The app assumes meter readings decrease over time (as electricity is consumed)
- No explicit validation for inconsistent data patterns
- Developers should consider adding validation to prevent impossible usage patterns

## UI Display Rules

### When to Show "N/A"

Short Average is displayed as "N/A" when:
- The entry is a top-up (amountToppedUp > 0)
- It's the very first meter reading (no previous entry to compare)
- The calculated average is 0.0 (e.g., same-day entries or no usage)

```dart
Text(
  shortAvg > 0 ? shortAvg.toStringAsFixed(2) : 'N/A',
  style: AppTextStyles.bodySmall.copyWith(
    color: isDarkMode ? Colors.white : Colors.black87,
    fontWeight: FontWeight.w500,
  ),
  textAlign: TextAlign.center,
)
```

Total Average is displayed as "N/A" when:
- The entry is a top-up (amountToppedUp > 0)
- It's the very first meter reading (no previous entry to compare)
- There are fewer than 2 entries
- The calculated average is 0.0

```dart
Text(
  _averageUsage > 0
      ? '${_averageUsage.toStringAsFixed(2)} $_meterUnit/day'
      : 'N/A',
  style: AppTextStyles.valueText,
  textAlign: TextAlign.center,
)
```

### Formatting Rules

- All averages are displayed with 2 decimal places
- The meter unit (e.g., £) is appended with "/day" suffix
- Consistent styling is applied across the app

## Code Examples

### Updated Meter History Page Implementation

The following code shows the complete implementation of the Meter History Page with both Short and Total Average calculations using the shared AverageCalculator utility:

```dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../db_helper.dart';
import '../screens/meter_entry.dart';
import '../utils/average_calculator.dart';
import '../theme/colors.dart';
import '../theme/text_styles.dart';

class MeterHistoryPage extends StatefulWidget {
  const MeterHistoryPage({super.key});

  @override
  State<MeterHistoryPage> createState() => _MeterHistoryPageState();
}

class _MeterHistoryPageState extends State<MeterHistoryPage> {
  final DBHelper dbHelper = DBHelper();
  List<MeterEntry> filteredEntries = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Get entries from database
      final entries = await dbHelper.getMeterEntries();

      // Sort entries chronologically (oldest to newest) for calculations
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      setState(() {
        filteredEntries = entries;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading meter entries: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Calculates the Short-Term Usage per Day between the most recent previous meter reading and the current one.
  /// This excludes top-up entries and only considers valid meter readings.
  ///
  /// @param index The index of the current entry in the filteredEntries list
  /// @return The short-term average usage per day, or 0.0 if not applicable
  double _calculateShortAverage(int index) {
    // Use the shared AverageCalculator to ensure consistent calculations across the app
    return AverageCalculator.calculateShortAverage(filteredEntries, index);
  }

  /// Calculates the Total Average to Date based on the cumulative usage from the earliest valid reading
  /// to the current row, excluding top-ups and adjusted per actual time passed.
  ///
  /// @param upToIndex The index up to which to calculate the average (inclusive)
  /// @return The total average usage per day, or 0.0 if not applicable
  double _calculateTotalAverage(int upToIndex) {
    // Use the shared AverageCalculator to ensure consistent calculations across the app
    return AverageCalculator.calculateTotalAverage(filteredEntries, upToIndex);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final historyColor = isDarkMode ? AppColors.historyDark : AppColors.history;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter History'),
        backgroundColor: historyColor,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : filteredEntries.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.history, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        "No meter entries available.",
                        style: AppTextStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "Add meter readings or top-ups to see your history.",
                        style: AppTextStyles.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: filteredEntries.length,
                  itemBuilder: (context, index) {
                    // Display entries in reverse chronological order (newest to oldest)
                    final displayIndex = filteredEntries.length - 1 - index;
                    final entry = filteredEntries[displayIndex];

                    final date = DateFormat('dd-MM-yyyy').format(entry.timestamp);
                    final time = DateFormat('HH:mm').format(entry.timestamp);

                    final shortAvg = _calculateShortAverage(displayIndex);
                    final totalAvg = _calculateTotalAverage(displayIndex);

                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      elevation: 3,
                      child: ListTile(
                        title: Text(
                          "Date: $date $time",
                          style: AppTextStyles.titleSmall,
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            if (entry.amountToppedUp > 0)
                              Row(
                                children: [
                                  const Icon(Icons.add_circle, size: 16, color: Colors.green),
                                  const SizedBox(width: 4),
                                  Text(
                                    "Top-Up: ${entry.amountToppedUp.toStringAsFixed(2)}",
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: Colors.green[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              )
                            else
                              Row(
                                children: [
                                  const Icon(Icons.show_chart, size: 16, color: Colors.blue),
                                  const SizedBox(width: 4),
                                  Text(
                                    "Reading: ${entry.reading.toStringAsFixed(2)}",
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: Colors.blue[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),

                            if (entry.amountToppedUp == 0) ...[  // Only show averages for meter readings
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(Icons.speed, size: 16, color: Colors.orange),
                                  const SizedBox(width: 4),
                                  Text(
                                    "Short Avg: ${shortAvg > 0 ? "${shortAvg.toStringAsFixed(2)} / day" : 'N/A'}",
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: Colors.orange[700],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  const Icon(Icons.insights, size: 16, color: Colors.purple),
                                  const SizedBox(width: 4),
                                  Text(
                                    "Total Avg: ${totalAvg > 0 ? "${totalAvg.toStringAsFixed(2)} / day" : 'N/A'}",
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: Colors.purple[700],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            const SizedBox(height: 4),
                          ],
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
```

### Home Page Average Calculation

```dart
Future<void> _loadMeterEntries() async {
  try {
    // Get entries from database
    final entries = await dbHelper.getMeterEntries();

    // Sort entries chronologically (oldest to newest) for calculations
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Calculate the most recent total average using the shared utility
    final averageUsage = AverageCalculator.getMostRecentTotalAverage(entries);

    // Calculate date to top up
    DateTime? dateToTopUp;
    if (averageUsage > 0) {
      dateToTopUp = AverageCalculator.calculateDateToTopUp(
        _meterTotal,
        _alertThreshold,
        averageUsage
      );
    }

    // Update state with new values
    setState(() {
      _meterEntries = entries;
      _averageUsage = averageUsage;
      _dateToTopUp = dateToTopUp;
    });

    // Add a small delay and empty setState to trigger animations
    await Future.delayed(const Duration(milliseconds: 100));
    if (mounted) setState(() {});

  } catch (e) {
    print('Error loading meter entries: $e');
  }
}
```

### Predicting Next Top-up Date

```dart
static DateTime? calculateDateToTopUp(double meterTotal, double alertThreshold, double averageUsage) {
  if (averageUsage <= 0) return null;

  final daysUntilThreshold = (meterTotal - alertThreshold) / averageUsage;
  if (daysUntilThreshold <= 0) return null;

  return DateTime.now().add(Duration(days: daysUntilThreshold.round()));
}

// In MyHomePage widget:
String _getFormattedDateToTopUp() {
  if (_dateToTopUp == null) return 'N/A';

  return _dateInfo == 'Date only'
      ? _formatDate(_dateToTopUp!)
      : '${_formatDate(_dateToTopUp!)} ${_formatTime(_dateToTopUp!)}';
}
```

### Low Balance Alert Check

```dart
Future<void> _checkForLowBalanceAlerts() async {
  if (_daysInAdvance <= 0 || _averageUsage <= 0) return;

  final daysUntilThreshold = (_meterTotal - _alertThreshold) / _averageUsage;
  final formattedThreshold = _alertThreshold.toStringAsFixed(2);
  final notificationManager = NotificationManager();

  // If current balance is already below threshold
  if (_meterTotal < _alertThreshold) {
    await notificationManager.showLowBalanceNotification(
      title: 'Time to Top Up Now! ⚠️',
      message: 'Your current balance of $_meterUnit${_meterTotal.toStringAsFixed(2)} is already below the alert threshold of $_meterUnit$formattedThreshold!',
      balance: _meterTotal,
      threshold: _alertThreshold,
      urgent: true,
    );
  }
  // If balance will reach threshold within the days in advance setting
  else if (daysUntilThreshold <= _daysInAdvance && daysUntilThreshold > 0) {
    final daysText = daysUntilThreshold < 1
        ? 'less than a day'
        : daysUntilThreshold < 2
            ? 'about 1 day'
            : 'about ${daysUntilThreshold.round()} days';

    await notificationManager.showLowBalanceNotification(
      title: 'Time to Top Up Soon! ⚠️',
      message: 'Your meter will reach the alert threshold of $_meterUnit$formattedThreshold in $daysText at current usage rate.',
      balance: _meterTotal,
      threshold: _alertThreshold,
      urgent: false,
    );
  }
}
```

## Developer Tips

### Best Practices for Updating Average Logic

1. **Maintain Backward Compatibility**:
   - Ensure changes don't break existing data visualization
   - Consider adding new methods rather than modifying existing ones

2. **Handle Edge Cases**:
   - Always check for division by zero
   - Validate input data for consistency
   - Add explicit handling for negative usage values
   - Use null safety features (e.g., `DateTime?` and null checks)

3. **Optimize Performance**:
   - Consider caching average calculations for frequently accessed entries
   - For large datasets, implement pagination or windowing
   - Avoid recalculating averages unnecessarily

4. **Improve Accuracy**:
   - Consider weighted averages for more recent data
   - Implement seasonal adjustments if usage patterns vary by time of year
   - Filter out anomalous readings that could skew averages
   - Only use valid reading pairs (consecutive non-top-up entries)

### Implementation Improvements

1. **Progressive Historical View**:
   - Calculate averages up to each specific point in history
   - This provides users with a better understanding of how their usage patterns have changed over time

2. **Consistent Display Rules**:
   - Only show averages for meter readings (not top-ups)
   - Use clear visual indicators (icons, colors) to distinguish between different types of entries
   - Format all values consistently with the same number of decimal places

3. **Data Validation**:
   - Sort entries chronologically before calculations
   - Skip invalid entry combinations (e.g., top-ups when calculating usage)
   - Track the actual first and last valid reading dates rather than assuming the entire dataset

### Testing Suggestions

1. **Unit Tests**:
   - Test with empty datasets
   - Test with single entry
   - Test with multiple entries including top-ups
   - Test with same-day entries
   - Test with negative usage values
   - Test with large time gaps between entries
   - Test with various patterns of top-ups and readings

2. **Test Datasets**:
   - Create a standard test dataset with known averages
   - Include edge cases in the dataset
   - Test with real-world usage patterns
   - Include scenarios with multiple consecutive top-ups

3. **UI Testing**:
   - Verify correct display of "N/A" vs. numeric values
   - Test formatting with different locale settings
   - Verify correct unit display
   - Ensure averages are only shown for appropriate entries

4. **Integration Testing**:
   - Verify averages update correctly when new data is added
   - Test notification triggers based on average calculations
   - Verify prediction accuracy over time
   - Test with different user settings (units, date formats)

## Recent Improvements

### Improved Top-Up Handling

The most significant improvement to the average calculation logic is the enhanced handling of top-ups:

1. **Accurate Usage Calculation**: The new logic properly accounts for top-ups between meter readings, ensuring accurate usage calculations.

2. **Two Calculation Scenarios**: The logic now handles two distinct scenarios:
   - When a meter reading follows a top-up
   - When a meter reading follows another meter reading with top-ups in between

3. **Proper Formula Application**: The correct formula is applied in each scenario:
   - For meter readings after top-ups: Usage = Previous Reading's Amount Left - Current Reading's Amount Left
   - For meter readings after other readings: Usage = Previous Reading - Current Reading - Sum of Top-Ups Between

### Centralized Average Calculation Logic

The calculation logic is centralized in a dedicated `AverageCalculator` utility class, providing several benefits:

1. **Consistency**: All parts of the app now use the same calculation logic, ensuring consistent results across different screens.

2. **Maintainability**: Changes to the calculation logic only need to be made in one place, reducing the risk of inconsistencies.

3. **Testability**: The utility class can be easily unit tested in isolation, ensuring the accuracy of the calculations.

4. **Reusability**: The calculation methods can be reused throughout the app without duplicating code.

### Enhanced Calculation Methods

The calculation methods have been enhanced with several improvements:

1. **Better Parameter Handling**: Methods now accept explicit parameters rather than relying on class properties, making them more flexible and easier to test.

2. **Improved Documentation**: Each method now has comprehensive documentation explaining its purpose, parameters, and return values.

3. **Robust Error Handling**: Methods include thorough validation of input parameters and handle edge cases gracefully.

4. **Optimized Performance**: Calculations are more efficient, with unnecessary recalculations avoided.

### New Features

Several new features have been added to enhance the user experience:

1. **Cost Calculation**: A dedicated method for calculating the cost of electricity usage over a specified date range, accounting for top-ups and meter readings.

2. **Date to Top Up Prediction**: A dedicated method for calculating the estimated date when the meter will reach the alert threshold.

3. **Most Recent Total Average**: A convenience method for getting the most recent total average from all entries.

4. **Improved Visualization**: The history page now displays averages with appropriate icons and colors, making them easier to understand.

### Future Enhancements

Potential future enhancements to the average calculation logic include:

1. **Seasonal Adjustments**: Adjusting averages based on seasonal patterns (e.g., higher usage in winter).

2. **Weighted Averages**: Giving more weight to recent readings for more accurate predictions.

3. **Anomaly Detection**: Identifying and potentially excluding unusual readings that could skew averages.

4. **Usage Forecasting**: Predicting future usage based on historical patterns and trends.
