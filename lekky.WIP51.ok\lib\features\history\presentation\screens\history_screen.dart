// File: lib/features/history/presentation/screens/history_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../domain/models/related_validation_result.dart';
import '../controllers/history_controller.dart';
import '../widgets/entry_detail_dialog.dart';
import '../widgets/entry_edit_dialog.dart';
import '../widgets/filter_dialog.dart';
import '../widgets/history_info_dialog.dart';
import '../widgets/fixed_header_history_table.dart';

/// The history screen of the app
class HistoryScreen extends StatefulWidget {
  const HistoryScreen({Key? key}) : super(key: key);

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  // Index for the current message to display
  int _currentMessageIndex = 0;
  // Scroll controller to track scrolling for parallax effect
  final ScrollController _scrollController = ScrollController();
  // Value notifier to track scroll position for the parallax effect
  final ValueNotifier<double> _scrollPosition = ValueNotifier<double>(0.0);
  // Value notifier to track max scroll extent
  final ValueNotifier<double> _maxScrollExtent = ValueNotifier<double>(0.0);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HistoryController>().init();
    });

    // Add listener to update scroll position for parallax effect
    _scrollController.addListener(_updateScrollInfo);

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  // Update scroll information for parallax effect
  void _updateScrollInfo() {
    if (!mounted) return;

    _scrollPosition.value =
        _scrollController.hasClients ? _scrollController.offset : 0;
    _maxScrollExtent.value = _scrollController.hasClients
        ? _scrollController.position.maxScrollExtent
        : 0;
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  void dispose() {
    // Reset edit mode when leaving the screen
    final controller = context.read<HistoryController>();
    if (controller.isEditMode) {
      controller.toggleEditMode();
    }

    // Remove listener and dispose scroll controller
    _scrollController.removeListener(_updateScrollInfo);
    _scrollController.dispose();

    // Dispose value notifiers
    _scrollPosition.dispose();
    _maxScrollExtent.dispose();

    super.dispose();
  }

  List<Widget> _buildAppBarActions(HistoryController controller) {
    return [
      if (controller.hasValidationErrors)
        Transform.translate(
          offset:
              const Offset(0, 4), // Move down by 4px to align with other icons
          child: Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: const BoxDecoration(
              color: AppColors.error,
              shape: BoxShape.circle,
            ),
            child: GestureDetector(
              onTap: () => _filterInvalidEntries(controller),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  '${controller.invalidEntryCount}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ),
        ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: Icon(
            controller.isEditMode ? Icons.edit_off : Icons.edit,
            color: controller.isEditMode ? Colors.blue[800] : Colors.white,
            size: 24,
          ),
          onPressed: controller.toggleEditMode,
          tooltip: controller.isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode',
        ),
      ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white, size: 24),
          onPressed: () => _showFilterDialog(context, controller),
          tooltip: 'Filter Options',
        ),
      ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: const Icon(Icons.info_outline, color: Colors.white, size: 24),
          onPressed: () => _showInfoDialog(context, controller),
          tooltip: 'History Information',
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HistoryController>(
      builder: (context, controller, _) {
        return Scaffold(
          backgroundColor:
              Colors.transparent, // Match Homepage transparent background
          body: Stack(
            children: [
              // Main content
              _buildMainContent(controller),

              // Parallax line overlay - positioned by the _buildParallaxLine method
              if (controller.hasFilteredEntries && !controller.isLoading)
                _buildParallaxLine(controller),
            ],
          ),
          bottomNavigationBar: _buildFixedControlsBar(controller),
        );
      },
    );
  }

  // Main content with scrolling
  Widget _buildMainContent(HistoryController controller) {
    return RefreshIndicator(
      onRefresh: controller.refresh,
      child: CustomScrollView(
        controller: _scrollController, // Attach scroll controller
        slivers: [
          _buildAppBar(controller),
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(
                8, 16, 16, 16), // Reduced left padding to 8px
            sliver: SliverToBoxAdapter(
              child: controller.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : controller.hasFilteredEntries
                      ? Column(
                          children: [
                            // Table content
                            FixedHeaderHistoryTable(
                              entries: controller.filteredEntries,
                              controller: controller,
                              onEntryTap: (entry) =>
                                  _showEntryEditDialog(entry),
                              onInvalidEntryTap: (entry) =>
                                  _showInvalidEntryExplanationDialog(entry),
                              scrollController:
                                  _scrollController, // Pass controller for parallax sync
                            ),
                            const SizedBox(height: 16),
                          ],
                        )
                      : const Center(child: Text('No entries found')),
            ),
          ),
          if (controller.error.isNotEmpty)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  controller.error,
                  style:
                      AppTextStyles.bodyMedium.copyWith(color: AppColors.error),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Parallax line that moves with scrolling
  Widget _buildParallaxLine(HistoryController controller) {
    // Reduced height by 60% (from 200 to 80)
    const double lineHeight = 80;

    // Use ValueListenableBuilder to avoid direct ScrollController access
    return ValueListenableBuilder<double>(
      valueListenable: _scrollPosition,
      builder: (context, scrollOffset, _) {
        return ValueListenableBuilder<double>(
          valueListenable: _maxScrollExtent,
          builder: (context, maxScroll, _) {
            // Calculate available height for the line to move in
            final double screenHeight = MediaQuery.of(context).size.height;
            const double topStart = 96 + 32 + 16; // AppBar + Message + Padding
            const double bottomBarHeight = 60;
            final double availableHeight =
                screenHeight - topStart - bottomBarHeight - lineHeight;

            // Calculate position based on scroll percentage
            // Move the line in the opposite direction of the scroll for parallax effect
            double topPosition = 0.0;
            if (maxScroll > 0) {
              // Map scroll position to available height - INVERT the direction
              topPosition = availableHeight -
                  (scrollOffset / maxScroll) * availableHeight;
              topPosition =
                  topPosition.clamp(0.0, availableHeight); // Prevent overflow
            }

            return Positioned(
              right: 16,
              top: topStart + topPosition, // Dynamic top position
              child: Container(
                width: 8, // Thicker line for better visibility
                height: lineHeight,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300, // Light grey as requested
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 3,
                      spreadRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                // Add a subtle animation to make the line more noticeable
                child: const Center(
                  child: Icon(
                    Icons.arrow_upward,
                    size: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return Container(
      color: Colors.grey[200],
      height: 32, // Reduced height to 32px to match Homepage
      child: Center(
        // Ensure text is perfectly centered
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            HelpfulMessages.allMessages[_currentMessageIndex],
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.black87,
              height: 1.0, // Ensure single line height
              fontSize: 13.0, // Slightly smaller font size to fit more text
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(HistoryController controller) {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px for the banner
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use a Stack for the banner content
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.historyGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - aligned with Meter Total dialog box
            const Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Homepage
              child: Text(
                'History',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Same font size as Homepage
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            // App bar actions
            Positioned(
              top: 10, // Changed from 6 to 10 (moved down by 4px)
              right: 12,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: _buildAppBarActions(controller),
              ),
            ),
            // Active filter chip - moved up by 3px
            if (_hasActiveFilter(controller))
              Positioned(
                top:
                    59, // Changed from 61 to 59 (moved up by an additional 2px)
                right: 42,
                child: _buildActiveFilterChip(controller),
              ),
          ],
        ),
      ),
      // Remove notification button
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }

  Widget _buildFixedControlsBar(HistoryController controller) {
    // Get the current page and total pages from the controller
    final currentPage = controller.currentPage;
    final totalPages = controller.totalPages;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 60, // Fixed height for the bottom bar
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.surfaceDark : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Pagination controls centered horizontally
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Previous page button
                IconButton(
                  icon: const Icon(Icons.chevron_left, size: 20),
                  onPressed: currentPage > 0
                      ? () => controller.goToPage(currentPage - 1)
                      : null,
                  color: Theme.of(context).colorScheme.primary,
                  padding: EdgeInsets.zero,
                  constraints:
                      const BoxConstraints(minWidth: 28, minHeight: 28),
                ),

                // Page indicator - more compact
                Text(
                  '${currentPage + 1}/$totalPages',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color:
                        isDarkMode ? AppColors.onSurfaceDark : Colors.black87,
                  ),
                ),

                // Next page button
                IconButton(
                  icon: const Icon(Icons.chevron_right, size: 20),
                  onPressed: currentPage < totalPages - 1
                      ? () => controller.goToPage(currentPage + 1)
                      : null,
                  color: Theme.of(context).colorScheme.primary,
                  padding: EdgeInsets.zero,
                  constraints:
                      const BoxConstraints(minWidth: 28, minHeight: 28),
                ),
              ],
            ),
          ),

          // Add Entry button positioned 42px from the right edge
          Positioned(
            right: 42, // 42px from the right edge
            child: IconButton(
              onPressed: () => _showEntryEditDialog(null),
              icon: const Icon(
                Icons.add_circle,
                color: Color(
                    0xFF43E97B), // Match the green color from homepage add button
                size: 39, // 40% bigger than original size of 28
              ),
              tooltip: 'Add Entry',
            ),
          ),
        ],
      ),
    );
  }

  void _showEntryEditDialog(MeterEntry? entry) {
    final controller = context.read<HistoryController>();

    // If we're not in edit mode and entry is not null, show the detail dialog
    if (!controller.isEditMode && entry != null) {
      _showEntryDetailDialog(entry);
      return;
    }

    // Otherwise, show the edit dialog
    EntryEditDialog.show(
      context: context,
      entry: entry,
      controller: controller,
      onSave: (updatedEntry) => controller.addEntry(updatedEntry),
      onDelete: entry?.id != null ? (id) => controller.deleteEntry(id) : null,
    );
  }

  void _showEntryDetailDialog(MeterEntry entry) {
    final controller = context.read<HistoryController>();
    EntryDetailDialog.show(
      context: context,
      entry: entry,
      controller: controller,
    );
  }

  void _showInfoDialog(BuildContext context, HistoryController controller) {
    HistoryInfoDialog.show(context: context, controller: controller);
  }

  void _showFilterDialog(BuildContext context, HistoryController controller) {
    DateTime? earliestDate;
    DateTime? latestDate;

    if (controller.allEntries.isNotEmpty) {
      final sortedEntries = List<MeterEntry>.from(controller.allEntries)
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
      earliestDate = sortedEntries.first.timestamp;
      latestDate = sortedEntries.last.timestamp;
    }

    FilterDialog.show(
      context: context,
      filter: controller.filter,
      onApply: (filter) {
        // Check if we need to apply special filtering for invalid entries
        bool isInvalidFilter = filter.showMeterReadings &&
            filter.showTopUps &&
            filter.showInvalidEntries;

        if (isInvalidFilter) {
          // Apply special filtering for invalid entries
          controller.filterInvalidEntries(filter);
        } else {
          // Apply normal filtering
          controller.updateFilter(filter);
        }
      },
      earliestDate: earliestDate,
      latestDate: latestDate,
      showDateRangeFilter: controller.hasEnoughReadingsForDateRange,
    );
  }

  void _filterInvalidEntries(HistoryController controller) {
    final filter = controller.filter.copyWith(
      startDate: null,
      endDate: null,
      showMeterReadings: true,
      showTopUps: true,
      showInvalidEntries: true,
    );
    controller.filterInvalidEntries(filter);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Showing ${controller.invalidEntryCount} invalid entries'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInvalidEntryExplanationDialog(MeterEntry entry) {
    final controller = context.read<HistoryController>();
    final validationResult = controller.getValidationResult(entry.id ?? -1);

    if (validationResult.isValid) {
      // This entry might be related to an invalid entry
      // Check if this entry is related to any invalid entry
      bool isRelatedToInvalidEntry = false;
      int? invalidEntryId;
      MeterEntry? relatedEntry;

      for (final result in controller.validationResults.entries) {
        if (!result.value.isValid &&
            result.value is RelatedValidationResult &&
            (result.value as RelatedValidationResult).relatedEntryId ==
                entry.id) {
          isRelatedToInvalidEntry = true;
          invalidEntryId = result.key; // This is the ID of the invalid entry

          // Find the related entry (the invalid entry)
          for (final e in controller.filteredEntries) {
            if (e.id == invalidEntryId) {
              relatedEntry = e;
              break;
            }
          }
          break;
        }
      }

      if (!isRelatedToInvalidEntry) return; // Not related to any invalid entry

      // Create a detailed message for the related entry
      String detailedMessage =
          'This entry is used to validate another entry that has an issue.';

      if (relatedEntry != null) {
        final formattedDate =
            '${relatedEntry.timestamp.day}/${relatedEntry.timestamp.month}/${relatedEntry.timestamp.year}';
        final formattedReading = relatedEntry.reading.toStringAsFixed(2);

        // If this entry is used to validate a later entry, it means this entry might be too high
        if (relatedEntry.timestamp.isAfter(entry.timestamp)) {
          detailedMessage =
              'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
        } else {
          detailedMessage =
              'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
        }
      }

      // Show dialog for related entry with the same design as the invalid entry dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Validation Warning',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Validation issue:',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Text(
                  detailedMessage,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[800],
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 12),
          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
          actions: [
            SizedBox(
              width: 100,
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue,
                  side: const BorderSide(color: Colors.blue),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Cancel'),
              ),
            ),
            SizedBox(
              width: 100,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  controller.toggleEditMode();
                  _showEntryEditDialog(entry);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      AppColors.costTab, // Dark orange from Cost screen
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Edit Now',
                    style: TextStyle(color: Colors.white)),
              ),
            ),
          ],
        ),
      );
      return;
    }

    // Show dialog for invalid entry
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'Validation Warning',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Validation issue:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Text(
                validationResult is RelatedValidationResult
                    ? validationResult.getDetailedErrorMessage()
                    : (validationResult.errorMessage ??
                        'Unknown validation error'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red[800],
                  fontSize: 16,
                ),
              ),
            ),
            // Removed the "To do this enable..." text
          ],
        ),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 12),
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
        actions: [
          SizedBox(
            width: 100,
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue,
                side: const BorderSide(color: Colors.blue),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Cancel'),
            ),
          ),
          SizedBox(
            width: 100,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                controller.toggleEditMode();
                _showEntryEditDialog(entry);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    AppColors.costTab, // Dark orange from Cost screen
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child:
                  const Text('Edit Now', style: TextStyle(color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilter(HistoryController controller) {
    final filter = controller.filter;

    // Check if we're specifically filtering for invalid entries
    bool isInvalidFilter = filter.showInvalidEntries &&
        controller.filteredEntries.length == controller.invalidEntryCount &&
        controller.invalidEntryCount > 0;

    return filter.startDate != null ||
        filter.endDate != null ||
        !filter.showMeterReadings ||
        !filter.showTopUps ||
        isInvalidFilter;
  }

  Widget _buildActiveFilterChip(HistoryController controller) {
    final filter = controller.filter;
    String filterText = '';
    Color chipColor = AppColors.primary;
    IconData filterIcon = Icons.filter_list;

    if (!filter.showMeterReadings && filter.showTopUps) {
      filterText = 'Top Up';
      chipColor = Colors.orange;
      filterIcon = Icons.add_circle;
    } else if (filter.showMeterReadings && !filter.showTopUps) {
      filterText = 'Meter';
      chipColor = Colors.blue;
      filterIcon = Icons.electric_meter;
    } else if (filter.startDate != null || filter.endDate != null) {
      if (controller.hasEnoughReadingsForDateRange) {
        filterText = 'Date';
        chipColor = Colors.green;
        filterIcon = Icons.calendar_today;
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller
              .updateFilter(filter.copyWith(startDate: null, endDate: null));
        });
      }
    } else if (filter.showInvalidEntries &&
        controller.filteredEntries.length == controller.invalidEntryCount &&
        controller.invalidEntryCount > 0) {
      filterText = 'Invalid';
      chipColor = Colors.red;
      filterIcon = Icons.error_outline;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => controller.resetFilter(),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: chipColor.withOpacity(0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(filterIcon, color: Colors.white, size: 18),
              const SizedBox(width: 4),
              Text(
                filterText,
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.close, color: Colors.white, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
