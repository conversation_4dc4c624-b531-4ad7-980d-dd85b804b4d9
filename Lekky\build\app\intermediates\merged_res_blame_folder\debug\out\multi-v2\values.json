{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-24:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8adab4e681d0863a25dfe4be8db95bf\\transformed\\jetified-window-1.0.0-beta04\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,99,185,191,336,344,356", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "165,344,404,456,6281,13111,13306,17934,18216,18656", "endLines": "7,8,9,10,99,190,194,343,355,363", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "339,399,451,496,6336,13301,13432,18211,18651,18960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\473c6549ebdad33ab68cbd325533c84f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "120,138", "startColumns": "4,4", "startOffsets": "7373,9145", "endColumns": "67,166", "endOffsets": "7436,9307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35761e94c6d5258a9b643a9e38a6ce19\\transformed\\jetified-startup-runtime-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7511", "endColumns": "82", "endOffsets": "7589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "17,18,19,20,36,37,148,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,979,1045,1108,2337,2408,10438,10506,10573,10652", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "974,1040,1103,1165,2403,2475,10501,10568,10647,10716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5c275d7683859a49fefc7284389acf42\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "33,34,35,60,61,62,63,119,172,174,175,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2104,2193,2264,3946,3999,4052,4105,7313,12150,12326,12448,12710,12905", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "2188,2259,2332,3994,4047,4100,4153,7368,12211,12443,12504,12771,12967"}}, {"source": "D:\\000.Workspace\\Lekky\\build\\app\\generated\\res\\google-services\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,582", "endColumns": "81,103,108,119,111,80", "endOffsets": "132,236,345,465,577,658"}, "to": {"startLines": "153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4", "startOffsets": "10803,10885,10989,11098,11218,11330", "endColumns": "81,103,108,119,111,80", "endOffsets": "10880,10984,11093,11213,11325,11406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33a23a1921cc9e261ad760b2275ca606\\transformed\\jetified-play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "23,24,25,26,27,28,29,30,130,131,132,133,134,135,136,137,139,140,141,142,143,144,145,146,147,304,317", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1301,1391,1471,1561,1651,1731,1812,1892,8105,8210,8391,8516,8623,8803,8926,9042,9312,9500,9605,9786,9911,10086,10234,10297,10359,17202,17517", "endLines": "23,24,25,26,27,28,29,30,130,131,132,133,134,135,136,137,139,140,141,142,143,144,145,146,147,316,335", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1386,1466,1556,1646,1726,1807,1887,1967,8205,8386,8511,8618,8798,8921,9037,9140,9495,9600,9781,9906,10081,10229,10292,10354,10433,17512,17929"}}, {"source": "D:\\000.Workspace\\Lekky\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "160,164", "startColumns": "4,4", "startOffsets": "11482,11663", "endLines": "163,166", "endColumns": "12,12", "endOffsets": "11658,11827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2a5a688d87ded2dd8a90eb4f6288faf8\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "501,566,636,700", "endColumns": "64,69,63,60", "endOffsets": "561,631,695,756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ef264fe3e2e64afd12a5581102dbb0a0\\transformed\\jetified-firebase-messaging-23.4.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "10721", "endColumns": "81", "endOffsets": "10798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5389d6eda3e5eba8ba922c509419fb7d\\transformed\\lifecycle-runtime-2.3.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "7270", "endColumns": "42", "endOffsets": "7308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,15,16,21,22,31,32,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,123,124,125,126,127,128,129,159,167,168,173,176,181,183,184,195,201,211,244,265,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,761,833,1170,1235,1972,2041,2480,2550,2618,2690,2760,2821,2895,2968,3029,3090,3152,3216,3278,3339,3407,3507,3567,3633,3706,3775,3832,3884,4158,4230,4306,4371,4430,4489,4549,4609,4669,4729,4789,4849,4909,4969,5029,5089,5148,5208,5268,5328,5388,5448,5508,5568,5628,5688,5748,5807,5867,5927,5986,6045,6104,6163,6222,6341,6376,6411,6466,6529,6584,6642,6700,6761,6824,6881,6932,6982,7043,7100,7166,7200,7235,7441,7594,7661,7733,7802,7871,7945,8017,11411,11832,11949,12216,12509,12776,12972,13044,13437,13640,13941,15672,16353,17035", "endLines": "2,15,16,21,22,31,32,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,123,124,125,126,127,128,129,159,167,171,173,179,181,183,184,200,210,243,264,297,303", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "160,828,916,1230,1296,2036,2099,2545,2613,2685,2755,2816,2890,2963,3024,3085,3147,3211,3273,3334,3402,3502,3562,3628,3701,3770,3827,3879,3941,4225,4301,4366,4425,4484,4544,4604,4664,4724,4784,4844,4904,4964,5024,5084,5143,5203,5263,5323,5383,5443,5503,5563,5623,5683,5743,5802,5862,5922,5981,6040,6099,6158,6217,6276,6371,6406,6461,6524,6579,6637,6695,6756,6819,6876,6927,6977,7038,7095,7161,7195,7230,7265,7506,7656,7728,7797,7866,7940,8012,8100,11477,11944,12145,12321,12705,12900,13039,13106,13635,13936,15667,16348,17030,17197"}}]}]}